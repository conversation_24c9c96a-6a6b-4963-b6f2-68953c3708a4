// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPatchReleaseBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton btnDownload;

  @NonNull
  public final ImageView ivGameImage;

  @NonNull
  public final TextView tvGameDetails;

  @NonNull
  public final TextView tvGameName;

  private ItemPatchReleaseBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton btnDownload, @NonNull ImageView ivGameImage,
      @NonNull TextView tvGameDetails, @NonNull TextView tvGameName) {
    this.rootView = rootView;
    this.btnDownload = btnDownload;
    this.ivGameImage = ivGameImage;
    this.tvGameDetails = tvGameDetails;
    this.tvGameName = tvGameName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPatchReleaseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPatchReleaseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_patch_release, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPatchReleaseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnDownload;
      MaterialButton btnDownload = ViewBindings.findChildViewById(rootView, id);
      if (btnDownload == null) {
        break missingId;
      }

      id = R.id.ivGameImage;
      ImageView ivGameImage = ViewBindings.findChildViewById(rootView, id);
      if (ivGameImage == null) {
        break missingId;
      }

      id = R.id.tvGameDetails;
      TextView tvGameDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvGameDetails == null) {
        break missingId;
      }

      id = R.id.tvGameName;
      TextView tvGameName = ViewBindings.findChildViewById(rootView, id);
      if (tvGameName == null) {
        break missingId;
      }

      return new ItemPatchReleaseBinding((MaterialCardView) rootView, btnDownload, ivGameImage,
          tvGameDetails, tvGameName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
