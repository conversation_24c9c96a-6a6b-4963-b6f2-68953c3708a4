<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_gradient_modern"
    tools:context=".ui.splash.SplashActivity">

    <!-- Logo Container -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/logoContainer"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_marginBottom="32dp"
        app:cardCornerRadius="100dp"
        app:cardElevation="24dp"
        app:strokeWidth="0dp"
        app:cardBackgroundColor="@color/surface"
        app:layout_constraintBottom_toTopOf="@+id/appNameText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed">

        <ImageView
            android:id="@+id/logoImage"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:src="@drawable/logo"
            android:scaleType="centerCrop"
            android:contentDescription="@string/app_name" />

    </com.google.android.material.card.MaterialCardView>

    <!-- App Name -->
    <TextView
        android:id="@+id/appNameText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="BEAR-MOD"
        android:textColor="@color/white"
        android:textSize="42sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-condensed"
        android:layout_marginTop="24dp"
        android:letterSpacing="0.15"
        android:shadowColor="#80000000"
        android:shadowDx="2"
        android:shadowDy="2"
        android:shadowRadius="8"
        app:layout_constraintBottom_toTopOf="@+id/subtitleText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/logoContainer"
        tools:ignore="HardcodedText" />

    <!-- Subtitle -->
    <TextView
        android:id="@+id/subtitleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Advanced Mod Loader"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:fontFamily="sans-serif-light"
        android:layout_marginTop="8dp"
        android:alpha="0.9"
        android:shadowColor="#80000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="4"
        app:layout_constraintBottom_toTopOf="@+id/progressContainer"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appNameText"
        tools:ignore="HardcodedText" />

    <!-- Progress Container -->
    <LinearLayout
        android:id="@+id/progressContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginTop="48dp"
        app:layout_constraintBottom_toTopOf="@+id/versionText"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/subtitleText">

        <ProgressBar
            android:id="@+id/progressLoading"
            style="?android:attr/progressBarStyle"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:indeterminateTint="@color/white" />

        <TextView
            android:id="@+id/loadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Initializing..."
            android:textColor="@color/white"
            android:textSize="14sp"
            android:layout_marginTop="16dp"
            android:alpha="0.8"
            tools:ignore="HardcodedText" />

    </LinearLayout>

    <!-- Version Text -->
    <TextView
        android:id="@+id/versionText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_version"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:layout_marginBottom="32dp"
        android:alpha="0.6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressContainer" />

</androidx.constraintlayout.widget.ConstraintLayout>
