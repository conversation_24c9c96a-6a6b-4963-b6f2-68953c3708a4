<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    <!-- PUBG MOBILE TW Logo -->
    <!-- Background circle -->
    <path
        android:fillColor="#0D1117"
        android:pathData="M24,4C12.95,4 4,12.95 4,24s8.95,20 20,20 20,-8.95 20,-20S35.05,4 24,4z"/>
    <!-- Taiwan flag inspired outer ring -->
    <path
        android:fillColor="#58A6FF"
        android:pathData="M24,6C14.06,6 6,14.06 6,24s8.06,18 18,18 18,-8.06 18,-18S33.94,6 24,6zM24,40C15.16,40 8,32.84 8,24S15.16,8 24,8s16,7.16 16,16S32.84,40 24,40z"/>
    <!-- Taiwan flag blue canton -->
    <path
        android:fillColor="#58A6FF"
        android:pathData="M12,12h12v12h-12z"/>
    <!-- Sun symbol (Taiwan flag element) -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M18,16c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3S19.66,16 18,16z"/>
    <!-- Sun rays -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M18,13h1v2h-1zM18,21h1v2h-1zM13,18h2v1h-2zM21,18h2v1h-2z"/>
    <!-- Red stripes (Taiwan flag element) -->
    <path
        android:fillColor="#F85149"
        android:pathData="M12,26h24v2h-24zM12,30h24v2h-24zM12,34h24v2h-24z"/>
    <!-- PUBG TW text -->
    <path
        android:fillColor="#21262D"
        android:pathData="M26,12h10v12h-10z"/>
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M28,14h6v2h-6zM28,18h6v2h-6z"/>
    <!-- Tactical crosshair overlay -->
    <path
        android:fillColor="#00D084"
        android:pathData="M23.5,23.5h1v1h-1z"/>
    <path
        android:fillColor="#00D084"
        android:pathData="M22,24h1v1h-1zM25,24h1v1h-1zM24,22h1v1h-1zM24,25h1v1h-1z"/>
</vector>
