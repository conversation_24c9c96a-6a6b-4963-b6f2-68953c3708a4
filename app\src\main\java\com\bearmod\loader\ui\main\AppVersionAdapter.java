package com.bearmod.loader.ui.main;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import com.bearmod.loader.R;

import com.google.android.material.button.MaterialButton;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

public class AppVersionAdapter extends RecyclerView.Adapter<AppVersionAdapter.ViewHolder> {

    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.#");

    private Context context;
    private List<AppVersion> appVersions;
    private OnAppVersionClickListener listener;
    private String currentDownloadingAppName = null;

    // Current progress data
    private int currentProgress = 0;
    private double currentDownloadedMB = 0;
    private double currentTotalSizeMB = 0;
    private double currentSpeedMBps = 0;
    private int currentEtaMinutes = 0;
    private int currentEtaSeconds = 0;

    public interface OnAppVersionClickListener {
        void onAppVersionClick(AppVersion appVersion);
    }

    public AppVersionAdapter(Context context, OnAppVersionClickListener listener) {
        this.context = context;
        this.appVersions = new ArrayList<>();
        this.listener = listener;
    }

    public void setAppVersions(List<AppVersion> appVersions) {
        this.appVersions = appVersions;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        try {
            View view = LayoutInflater.from(context).inflate(R.layout.item_app_version, parent, false);
            return new ViewHolder(view);
        } catch (Exception e) {
            android.util.Log.e("AppVersionAdapter", "Error creating view holder: " + e.getMessage(), e);
            // Create a simple fallback view
            android.widget.TextView fallbackView = new android.widget.TextView(context);
            fallbackView.setText("Error loading item");
            fallbackView.setPadding(16, 16, 16, 16);
            return new ViewHolder(fallbackView);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        try {
            AppVersion version = appVersions.get(position);

            if (holder.appIcon != null) {
                holder.appIcon.setImageResource(version.getIconResId());
            }
            if (holder.appName != null) {
                holder.appName.setText(version.getName());
            }
            if (holder.appVersion != null) {
                holder.appVersion.setText(version.getVersion());
            }
            if (holder.appDescription != null) {
                holder.appDescription.setText(version.getDescription());
            }

            // Set status color and text
            if (holder.status != null) {
                try {
                    int statusColor;
                    String statusText;
                    switch (version.getStatus()) {
                        case AVAILABLE:
                            statusColor = R.color.info;
                            statusText = "Available";
                            break;
                        case DOWNLOADING:
                            statusColor = R.color.warning;
                            statusText = "Downloading";
                            break;
                        case INSTALLED:
                            statusColor = R.color.success;
                            statusText = "Installed";
                            break;
                        default:
                            statusColor = R.color.info;
                            statusText = "Available";
                            break;
                    }
                    holder.status.setBackgroundTintList(ContextCompat.getColorStateList(context, statusColor));
                    holder.status.setText(statusText);
                } catch (Exception e) {
                    android.util.Log.e("AppVersionAdapter", "Error setting status: " + e.getMessage());
                    holder.status.setText("Unknown");
                }
            }

            // Handle download progress display
            if (holder.downloadProgressContainer != null) {
                if (version.getStatus() == AppVersion.AppStatus.DOWNLOADING &&
                    version.getName().equals(currentDownloadingAppName)) {
                    holder.downloadProgressContainer.setVisibility(View.VISIBLE);

                    // Update progress views with current data
                    if (holder.progressBarInline != null) {
                        holder.progressBarInline.setProgress(currentProgress);
                    }
                    if (holder.tvProgressInline != null) {
                        holder.tvProgressInline.setText(currentProgress + "%");
                    }
                    if (holder.tvSpeedInline != null) {
                        holder.tvSpeedInline.setText(DECIMAL_FORMAT.format(currentSpeedMBps) + " MB/s");
                    }
                    if (holder.tvEtaInline != null) {
                        if (currentEtaMinutes > 0) {
                            holder.tvEtaInline.setText(currentEtaMinutes + "m " + currentEtaSeconds + "s");
                        } else if (currentEtaSeconds > 0) {
                            holder.tvEtaInline.setText(currentEtaSeconds + "s");
                        } else {
                            holder.tvEtaInline.setText("--");
                        }
                    }
                } else {
                    holder.downloadProgressContainer.setVisibility(View.GONE);
                }
            }

            if (holder.btnAction != null) {
                // Change button icon based on status
                if (version.getStatus() == AppVersion.AppStatus.DOWNLOADING) {
                    holder.btnAction.setIcon(ContextCompat.getDrawable(context, R.drawable.ic_cancel));
                    holder.btnAction.setOnClickListener(v -> {
                        // TODO: Cancel download
                    });
                } else {
                    holder.btnAction.setIcon(ContextCompat.getDrawable(context, R.drawable.ic_download));
                    holder.btnAction.setOnClickListener(v -> {
                        if (listener != null) {
                            listener.onAppVersionClick(version);
                        }
                    });
                }
            }
        } catch (Exception e) {
            android.util.Log.e("AppVersionAdapter", "Error binding view holder: " + e.getMessage(), e);
        }
    }

    @Override
    public int getItemCount() {
        return appVersions.size();
    }

    /**
     * Show download progress for a specific app
     * @param appName App name to show progress for
     * @param progress Progress percentage (0-100)
     * @param downloadedMB Downloaded size in MB
     * @param totalSizeMB Total size in MB
     * @param speedMBps Download speed in MB/s
     * @param etaMinutes ETA minutes
     * @param etaSeconds ETA seconds
     */
    public void showDownloadProgress(String appName, int progress, double downloadedMB,
                                   double totalSizeMB, double speedMBps, int etaMinutes, int etaSeconds) {
        currentDownloadingAppName = appName;

        // Store current progress data
        currentProgress = progress;
        currentDownloadedMB = downloadedMB;
        currentTotalSizeMB = totalSizeMB;
        currentSpeedMBps = speedMBps;
        currentEtaMinutes = etaMinutes;
        currentEtaSeconds = etaSeconds;

        // Find the app in the list and update its progress
        for (int i = 0; i < appVersions.size(); i++) {
            AppVersion app = appVersions.get(i);
            if (app.getName().equals(appName)) {
                app.setStatus(AppVersion.AppStatus.DOWNLOADING);
                notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * Hide download progress for a specific app
     * @param appName App name to hide progress for
     * @param success Whether download was successful
     */
    public void hideDownloadProgress(String appName, boolean success) {
        currentDownloadingAppName = null;

        // Find the app in the list and update its status
        for (int i = 0; i < appVersions.size(); i++) {
            AppVersion app = appVersions.get(i);
            if (app.getName().equals(appName)) {
                app.setStatus(success ? AppVersion.AppStatus.INSTALLED : AppVersion.AppStatus.AVAILABLE);
                notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * Update progress views for a specific position
     */
    private void updateProgressViews(int position, int progress, double downloadedMB,
                                   double totalSizeMB, double speedMBps, int etaMinutes, int etaSeconds) {
        // This will be called when the ViewHolder is bound
        // The actual update happens in onBindViewHolder
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView appIcon;
        TextView appName;
        TextView appVersion;
        TextView appDescription;
        TextView status;
        MaterialButton btnAction;

        // Progress views
        LinearLayout downloadProgressContainer;
        ProgressBar progressBarInline;
        TextView tvProgressInline;
        TextView tvSpeedInline;
        TextView tvEtaInline;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            try {
                appIcon = itemView.findViewById(R.id.app_icon);
                appName = itemView.findViewById(R.id.app_name);
                appVersion = itemView.findViewById(R.id.app_version);
                appDescription = itemView.findViewById(R.id.app_description);
                status = itemView.findViewById(R.id.status);
                btnAction = itemView.findViewById(R.id.btn_action);

                // Progress views
                downloadProgressContainer = itemView.findViewById(R.id.download_progress_container);
                progressBarInline = itemView.findViewById(R.id.progress_bar_inline);
                tvProgressInline = itemView.findViewById(R.id.tv_progress_inline);
                tvSpeedInline = itemView.findViewById(R.id.tv_speed_inline);
                tvEtaInline = itemView.findViewById(R.id.tv_eta_inline);
            } catch (Exception e) {
                android.util.Log.e("AppVersionAdapter", "Error finding views in ViewHolder: " + e.getMessage(), e);
            }
        }
    }
} 