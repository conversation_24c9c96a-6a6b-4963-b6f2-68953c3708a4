{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\.cxx\\Debug\\2129i685\\arm64-v8a", "soFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx\\Debug\\2129i685\\obj\\local\\arm64-v8a", "soRepublishFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\ndkBuild\\debug\\obj\\local\\arm64-v8a", "abiPlatformVersion": 30, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\.cxx", "intermediatesBaseFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates", "intermediatesFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "D:\\Augment_Code\\BearMod-Loader\\app", "moduleBuildFile": "D:\\Augment_Code\\BearMod-Loader\\app\\build.gradle.kts", "makeFile": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "buildSystem": "NDK_BUILD", "ndkFolder": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006", "ndkFolderBeforeSymLinking": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "SYSTEM", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Augment_Code\\BearMod-Loader", "sdkFolder": "D:\\AndroidBuildEnv\\SDK", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\.cxx\\Debug\\2129i685\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "2129i685z5d361t1o631h254n1n6z5y2s1e4c3e3mb02a4w1c2o3x59184x1w", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.10.1.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=$PROJECT/app/src/main/jni/Android.mk\nNDK_APPLICATION_MK=$PROJECT/app/src/main/jni/Application.mk\nAPP_ABI=$ABI\nNDK_ALL_ABIS=$ABI\nNDK_DEBUG=1\nAPP_PLATFORM=android-30\nNDK_OUT=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/obj\nNDK_LIBS_OUT=$PROJECT/app/build/intermediates/cxx/Debug/$HASH/lib", "configurationArguments": ["NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Application.mk", "APP_ABI=arm64-v8a", "NDK_ALL_ABIS=arm64-v8a", "NDK_DEBUG=1", "APP_PLATFORM=android-30", "NDK_OUT=D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx\\Debug\\2129i685/obj", "NDK_LIBS_OUT=D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx\\Debug\\2129i685/lib"], "stlLibraryFile": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "intermediatesParentFolder": "D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx\\Debug\\2129i685"}