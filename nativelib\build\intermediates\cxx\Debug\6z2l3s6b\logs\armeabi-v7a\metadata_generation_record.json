[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx\\Debug\\6z2l3s6b\\armeabi-v7a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]