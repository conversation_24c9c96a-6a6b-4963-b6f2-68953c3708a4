{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-my/values-my.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\9f99db3bdee5091140633361bed826be\\transformed\\material-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,386,485,561,652,736,842,971,1056,1119,1184,1274,1349,1408,1499,1562,1627,1686,1757,1819,1876,1995,2053,2114,2169,2242,2374,2465,2549,2649,2735,2824,2965,3043,3120,3243,3335,3412,3470,3521,3587,3659,3741,3812,3890,3965,4039,4111,4190,4298,4395,4476,4562,4654,4728,4807,4893,4947,5023,5091,5174,5255,5317,5381,5444,5512,5624,5735,5839,5952,6013,6068,6150,6237,6317", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "280,381,480,556,647,731,837,966,1051,1114,1179,1269,1344,1403,1494,1557,1622,1681,1752,1814,1871,1990,2048,2109,2164,2237,2369,2460,2544,2644,2730,2819,2960,3038,3115,3238,3330,3407,3465,3516,3582,3654,3736,3807,3885,3960,4034,4106,4185,4293,4390,4471,4557,4649,4723,4802,4888,4942,5018,5086,5169,5250,5312,5376,5439,5507,5619,5730,5834,5947,6008,6063,6145,6232,6312,6390"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,3728,3829,3928,4004,4095,4921,5027,5156,5241,5304,9232,9322,9397,9456,9547,9610,9675,9734,9805,9867,9924,10043,10101,10162,10217,10290,10422,10513,10597,10697,10783,10872,11013,11091,11168,11291,11383,11460,11518,11569,11635,11707,11789,11860,11938,12013,12087,12159,12238,12346,12443,12524,12610,12702,12776,12855,12941,12995,13071,13139,13222,13303,13365,13429,13492,13560,13672,13783,13887,14000,14061,14355,14523,14610,14690", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,100,98,75,90,83,105,128,84,62,64,89,74,58,90,62,64,58,70,61,56,118,57,60,54,72,131,90,83,99,85,88,140,77,76,122,91,76,57,50,65,71,81,70,77,74,73,71,78,107,96,80,85,91,73,78,85,53,75,67,82,80,61,63,62,67,111,110,103,112,60,54,81,86,79,77", "endOffsets": "962,3824,3923,3999,4090,4174,5022,5151,5236,5299,5364,9317,9392,9451,9542,9605,9670,9729,9800,9862,9919,10038,10096,10157,10212,10285,10417,10508,10592,10692,10778,10867,11008,11086,11163,11286,11378,11455,11513,11564,11630,11702,11784,11855,11933,12008,12082,12154,12233,12341,12438,12519,12605,12697,12771,12850,12936,12990,13066,13134,13217,13298,13360,13424,13487,13555,13667,13778,13882,13995,14056,14111,14432,14605,14685,14763"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\50cbe4bff5d1390b137919b7462144e4\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4179,4282,4386,4489,4591,4696,4802,14768", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "4277,4381,4484,4586,4691,4797,4916,14864"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\21d61e9b45cfab2ee4b69c56a0b52398\\transformed\\appcompat-1.7.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1080,1187,1303,1390,1499,1622,1701,1779,1870,1963,2058,2152,2252,2345,2440,2534,2625,2716,2801,2916,3025,3124,3250,3357,3465,3625,14437", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "1075,1182,1298,1385,1494,1617,1696,1774,1865,1958,2053,2147,2247,2340,2435,2529,2620,2711,2796,2911,3020,3119,3245,3352,3460,3620,3723,14518"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8c95fb1bf71448479a8aeb756e068ef5\\transformed\\exoplayer-ui-2.19.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,292,491,687,771,853,940,1042,1138,1211,1278,1377,1472,1540,1607,1674,1741,1864,1985,2106,2177,2257,2330,2401,2489,2575,2640,2704,2757,2815,2865,2926,2984,3046,3119,3188,3253,3311,3375,3440,3508,3562,3624,3700,3776", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,122,120,120,70,79,72,70,87,85,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "287,486,682,766,848,935,1037,1133,1206,1273,1372,1467,1535,1602,1669,1736,1859,1980,2101,2172,2252,2325,2396,2484,2570,2635,2699,2752,2810,2860,2921,2979,3041,3114,3183,3248,3306,3370,3435,3503,3557,3619,3695,3771,3825"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,387,586,5369,5453,5535,5622,5724,5820,5893,5960,6059,6154,6222,6289,6356,6423,6546,6667,6788,6859,6939,7012,7083,7171,7257,7322,8106,8159,8217,8267,8328,8386,8448,8521,8590,8655,8713,8777,8842,8910,8964,9026,9102,9178", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,83,81,86,101,95,72,66,98,94,67,66,66,66,122,120,120,70,79,72,70,87,85,64,63,52,57,49,60,57,61,72,68,64,57,63,64,67,53,61,75,75,53", "endOffsets": "382,581,777,5448,5530,5617,5719,5815,5888,5955,6054,6149,6217,6284,6351,6418,6541,6662,6783,6854,6934,7007,7078,7166,7252,7317,7381,8154,8212,8262,8323,8381,8443,8516,8585,8650,8708,8772,8837,8905,8959,9021,9097,9173,9227"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8fcff5946e9cb42bb9e1450a210c2616\\transformed\\navigation-ui-2.9.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "14116,14229", "endColumns": "112,125", "endOffsets": "14224,14350"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\eef25481716fc7890be7aca21c0506bf\\transformed\\exoplayer-core-2.19.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7386,7461,7533,7606,7675,7757,7832,7933,8028", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "7456,7528,7601,7670,7752,7827,7928,8023,8101"}}]}]}