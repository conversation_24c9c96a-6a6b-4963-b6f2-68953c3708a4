package com.bearmod.loader.ui.test;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.R;
import com.bearmod.loader.download.DownloadManager;
import com.bearmod.loader.utils.StorageManager;

import java.io.File;

/**
 * Test activity for demonstrating the new PUBG Mobile download functionality
 * This activity provides a simple interface to test downloads for all regions
 */
public class DownloadTestActivity extends AppCompatActivity {
    
    private static final String TAG = "DownloadTestActivity";
    
    private Button btnDownloadGlobal, btnDownloadKR, btnDownloadTW, btnDownloadVN;
    private Button btnTestStorage, btnCleanup;
    private TextView tvStatus, tvProgress, tvStorageInfo;
    private ProgressBar progressBar;
    
    private DownloadManager downloadManager;
    private boolean isDownloading = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_download_test);
        
        initViews();
        setupClickListeners();
        
        // Initialize download manager
        downloadManager = DownloadManager.getInstance();
        downloadManager.initialize(this);
        
        // Set up progress listener
        downloadManager.setProgressListener(this::updateProgress);
        
        // Update storage info
        updateStorageInfo();
        
        Log.d(TAG, "Download test activity initialized");
    }
    
    private void initViews() {
        btnDownloadGlobal = findViewById(R.id.btn_download_global);
        btnDownloadKR = findViewById(R.id.btn_download_kr);
        btnDownloadTW = findViewById(R.id.btn_download_tw);
        btnDownloadVN = findViewById(R.id.btn_download_vn);
        btnTestStorage = findViewById(R.id.btn_test_storage);
        btnCleanup = findViewById(R.id.btn_cleanup);
        
        tvStatus = findViewById(R.id.tv_status);
        tvProgress = findViewById(R.id.tv_progress);
        tvStorageInfo = findViewById(R.id.tv_storage_info);
        progressBar = findViewById(R.id.progress_bar);
        
        // Set initial state
        tvStatus.setText("Ready to download");
        tvProgress.setText("0%");
        progressBar.setProgress(0);
    }
    
    private void setupClickListeners() {
        btnDownloadGlobal.setOnClickListener(v -> startDownload("PUBG MOBILE Global"));
        btnDownloadKR.setOnClickListener(v -> startDownload("PUBG MOBILE KR"));
        btnDownloadTW.setOnClickListener(v -> startDownload("PUBG MOBILE TW"));
        btnDownloadVN.setOnClickListener(v -> startDownload("PUBG MOBILE VN"));
        
        btnTestStorage.setOnClickListener(v -> testStorageFeatures());
        btnCleanup.setOnClickListener(v -> performCleanup());
        
        // Back button
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }
    
    private void startDownload(String gameName) {
        if (isDownloading) {
            Toast.makeText(this, "Download already in progress", Toast.LENGTH_SHORT).show();
            return;
        }
        
        Log.d(TAG, "Starting download for: " + gameName);
        
        // Update UI
        setDownloadingState(true);
        tvStatus.setText("Starting download for " + gameName + "...");
        
        // Start enhanced download with progress dialog
        downloadManager.downloadGameFilesEnhanced(this, gameName, 
            DownloadManager.DownloadType.APK_AND_OBB,
            new DownloadManager.EnhancedDownloadListener() {
                @Override
                public void onSuccess(String filePath) {
                    runOnUiThread(() -> {
                        setDownloadingState(false);
                        tvStatus.setText("Download completed: " + gameName);
                        tvProgress.setText("100%");
                        progressBar.setProgress(100);
                        
                        Toast.makeText(DownloadTestActivity.this, 
                            "Download completed successfully!", Toast.LENGTH_LONG).show();
                        
                        updateStorageInfo();
                        
                        Log.d(TAG, "Download completed: " + filePath);
                    });
                }
                
                @Override
                public void onError(String error) {
                    runOnUiThread(() -> {
                        setDownloadingState(false);
                        tvStatus.setText("Download failed: " + error);
                        tvProgress.setText("Failed");
                        progressBar.setProgress(0);
                        
                        Toast.makeText(DownloadTestActivity.this, 
                            "Download failed: " + error, Toast.LENGTH_LONG).show();
                        
                        Log.e(TAG, "Download failed: " + error);
                    });
                }
            });
    }
    
    private void updateProgress(int progress, double downloadedMB, double totalSizeMB,
                               double speedMBps, int etaMinutes, int etaSeconds) {
        runOnUiThread(() -> {
            progressBar.setProgress(progress);
            
            String progressText = String.format("%d%% (%.1f/%.1f MB)", 
                progress, downloadedMB, totalSizeMB);
            
            if (speedMBps > 0) {
                progressText += String.format(" - %.1f MB/s", speedMBps);
                
                if (etaMinutes > 0 || etaSeconds > 0) {
                    progressText += String.format(" - ETA: %d:%02d", etaMinutes, etaSeconds);
                }
            }
            
            tvProgress.setText(progressText);
        });
    }
    
    private void setDownloadingState(boolean downloading) {
        isDownloading = downloading;
        
        btnDownloadGlobal.setEnabled(!downloading);
        btnDownloadKR.setEnabled(!downloading);
        btnDownloadTW.setEnabled(!downloading);
        btnDownloadVN.setEnabled(!downloading);
        btnTestStorage.setEnabled(!downloading);
        btnCleanup.setEnabled(!downloading);
    }
    
    private void testStorageFeatures() {
        Log.d(TAG, "Testing storage features");
        
        StorageManager.StorageInfo info = StorageManager.getStorageInfo();
        
        String message = String.format(
            "Storage Test Results:\n\n" +
            "Total Space: %d MB\n" +
            "Available Space: %d MB\n" +
            "Used Space: %d MB\n" +
            "Usage: %d%%\n\n" +
            "Can download 3GB file: %s\n" +
            "Can download 1GB file: %s",
            info.totalSpaceMB,
            info.availableSpaceMB,
            info.usedSpaceMB,
            info.usagePercentage,
            StorageManager.hasEnoughSpace(3000) ? "Yes" : "No",
            StorageManager.hasEnoughSpace(1000) ? "Yes" : "No"
        );
        
        tvStatus.setText("Storage test completed");
        
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Storage Test Results")
            .setMessage(message)
            .setPositiveButton("OK", null)
            .show();
        
        updateStorageInfo();
    }
    
    private void performCleanup() {
        Log.d(TAG, "Performing cleanup");
        
        tvStatus.setText("Cleaning up old files...");
        
        new Thread(() -> {
            boolean success = StorageManager.cleanupForSpace(this, 1000); // Try to free 1GB
            
            runOnUiThread(() -> {
                if (success) {
                    tvStatus.setText("Cleanup completed successfully");
                    Toast.makeText(this, "Cleanup completed", Toast.LENGTH_SHORT).show();
                } else {
                    tvStatus.setText("Cleanup completed (no files to clean)");
                    Toast.makeText(this, "No old files found to clean", Toast.LENGTH_SHORT).show();
                }
                
                updateStorageInfo();
            });
        }).start();
    }
    
    private void updateStorageInfo() {
        StorageManager.StorageInfo info = StorageManager.getStorageInfo();
        
        String storageText = String.format("Storage: %d MB available / %d MB total (%d%% used)",
            info.availableSpaceMB, info.totalSpaceMB, info.usagePercentage);
        
        tvStorageInfo.setText(storageText);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Clean up progress listener
        if (downloadManager != null) {
            downloadManager.setProgressListener(null);
        }
    }
}
