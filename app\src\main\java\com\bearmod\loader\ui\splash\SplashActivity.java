
package com.bearmod.loader.ui.splash;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.OvershootInterpolator;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;

import com.bearmod.loader.databinding.ActivitySplashBinding;

/**
 * Splash activity
 * Displays splash screen and validates build
 */
public class SplashActivity extends AppCompatActivity {

    private ActivitySplashBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        binding = ActivitySplashBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Start animations
        startSplashAnimations();

        // Check login status after animations
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            updateLoadingText("Checking authentication...");

            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (!com.bearmod.loader.BearLoaderApplication.getInstance().isLoggedIn()) {
                    updateLoadingText("Redirecting to login...");
                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        Intent intent = new Intent(this, com.bearmod.loader.ui.auth.LoginActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        startActivity(intent);
                        finish();
                    }, 500);
                    return;
                }

                // User is logged in, navigate to MainLoaderActivity
                updateLoadingText("Loading main interface...");
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    Intent intent = new Intent(this, com.bearmod.loader.ui.main.MainLoaderActivity.class);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                    startActivity(intent);
                    finish();
                }, 800);
            }, 1000);
        }, 1500);
    }

    private void startSplashAnimations() {
        // Initially hide all views
        binding.logoContainer.setAlpha(0f);
        binding.logoContainer.setScaleX(0.3f);
        binding.logoContainer.setScaleY(0.3f);
        binding.appNameText.setAlpha(0f);
        binding.appNameText.setTranslationY(50f);
        binding.subtitleText.setAlpha(0f);
        binding.subtitleText.setTranslationY(30f);
        binding.progressContainer.setAlpha(0f);
        binding.versionText.setAlpha(0f);

        // Animate logo container
        ObjectAnimator logoAlpha = ObjectAnimator.ofFloat(binding.logoContainer, "alpha", 0f, 1f);
        ObjectAnimator logoScaleX = ObjectAnimator.ofFloat(binding.logoContainer, "scaleX", 0.3f, 1f);
        ObjectAnimator logoScaleY = ObjectAnimator.ofFloat(binding.logoContainer, "scaleY", 0.3f, 1f);

        AnimatorSet logoSet = new AnimatorSet();
        logoSet.playTogether(logoAlpha, logoScaleX, logoScaleY);
        logoSet.setDuration(800);
        logoSet.setInterpolator(new OvershootInterpolator(1.2f));

        // Animate app name
        ObjectAnimator nameAlpha = ObjectAnimator.ofFloat(binding.appNameText, "alpha", 0f, 1f);
        ObjectAnimator nameTranslation = ObjectAnimator.ofFloat(binding.appNameText, "translationY", 50f, 0f);

        AnimatorSet nameSet = new AnimatorSet();
        nameSet.playTogether(nameAlpha, nameTranslation);
        nameSet.setDuration(600);
        nameSet.setStartDelay(400);
        nameSet.setInterpolator(new AccelerateDecelerateInterpolator());

        // Animate subtitle
        ObjectAnimator subtitleAlpha = ObjectAnimator.ofFloat(binding.subtitleText, "alpha", 0f, 1f);
        ObjectAnimator subtitleTranslation = ObjectAnimator.ofFloat(binding.subtitleText, "translationY", 30f, 0f);

        AnimatorSet subtitleSet = new AnimatorSet();
        subtitleSet.playTogether(subtitleAlpha, subtitleTranslation);
        subtitleSet.setDuration(500);
        subtitleSet.setStartDelay(600);
        subtitleSet.setInterpolator(new AccelerateDecelerateInterpolator());

        // Animate progress container
        ObjectAnimator progressAlpha = ObjectAnimator.ofFloat(binding.progressContainer, "alpha", 0f, 1f);
        progressAlpha.setDuration(400);
        progressAlpha.setStartDelay(1000);

        // Animate version text
        ObjectAnimator versionAlpha = ObjectAnimator.ofFloat(binding.versionText, "alpha", 0f, 1f);
        versionAlpha.setDuration(300);
        versionAlpha.setStartDelay(1200);

        // Start all animations
        logoSet.start();
        nameSet.start();
        subtitleSet.start();
        progressAlpha.start();
        versionAlpha.start();
    }

    private void updateLoadingText(String text) {
        if (binding.loadingText != null) {
            binding.loadingText.setText(text);
        }
    }


}
