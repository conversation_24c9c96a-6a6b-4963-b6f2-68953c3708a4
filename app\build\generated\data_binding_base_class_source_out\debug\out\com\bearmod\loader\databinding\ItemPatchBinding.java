// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPatchBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final Button btnApplyPatch;

  @NonNull
  public final MaterialButton btnDownload;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvGameVersion;

  @NonNull
  public final TextView tvPatchDescription;

  @NonNull
  public final TextView tvPatchName;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvUpdateDate;

  private ItemPatchBinding(@NonNull MaterialCardView rootView, @NonNull Button btnApplyPatch,
      @NonNull MaterialButton btnDownload, @NonNull ProgressBar progressBar,
      @NonNull TextView tvGameVersion, @NonNull TextView tvPatchDescription,
      @NonNull TextView tvPatchName, @NonNull TextView tvStatus, @NonNull TextView tvUpdateDate) {
    this.rootView = rootView;
    this.btnApplyPatch = btnApplyPatch;
    this.btnDownload = btnDownload;
    this.progressBar = progressBar;
    this.tvGameVersion = tvGameVersion;
    this.tvPatchDescription = tvPatchDescription;
    this.tvPatchName = tvPatchName;
    this.tvStatus = tvStatus;
    this.tvUpdateDate = tvUpdateDate;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPatchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPatchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_patch, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPatchBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_apply_patch;
      Button btnApplyPatch = ViewBindings.findChildViewById(rootView, id);
      if (btnApplyPatch == null) {
        break missingId;
      }

      id = R.id.btn_download;
      MaterialButton btnDownload = ViewBindings.findChildViewById(rootView, id);
      if (btnDownload == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_game_version;
      TextView tvGameVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvGameVersion == null) {
        break missingId;
      }

      id = R.id.tv_patch_description;
      TextView tvPatchDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvPatchDescription == null) {
        break missingId;
      }

      id = R.id.tv_patch_name;
      TextView tvPatchName = ViewBindings.findChildViewById(rootView, id);
      if (tvPatchName == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tv_update_date;
      TextView tvUpdateDate = ViewBindings.findChildViewById(rootView, id);
      if (tvUpdateDate == null) {
        break missingId;
      }

      return new ItemPatchBinding((MaterialCardView) rootView, btnApplyPatch, btnDownload,
          progressBar, tvGameVersion, tvPatchDescription, tvPatchName, tvStatus, tvUpdateDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
