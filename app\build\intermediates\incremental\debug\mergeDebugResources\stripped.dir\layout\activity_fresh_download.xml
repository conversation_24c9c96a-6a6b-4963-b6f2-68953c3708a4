<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#1A2B1A"
    android:fitsSystemWindows="true">

    <!-- Top Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:background="#1A2B1A"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_back"
            android:tint="#FFFFFF" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Bearmod..."
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginStart="16dp" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_settings"
            android:tint="#FFFFFF"
            android:layout_marginStart="16dp" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_download"
            android:tint="#FFFFFF"
            android:layout_marginStart="16dp" />

        <View
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/circle_background" />

    </LinearLayout>

    <!-- Region Tabs -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/tab_global"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_selected_bg"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_pubg_global_vector"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Global"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_kr"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_unselected_bg"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_pubg_kr_vector"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="KR"
                android:textColor="#888888"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_tw"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_unselected_bg"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_pubg_tw_vector"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="TW"
                android:textColor="#888888"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_vn"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_unselected_bg"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_pubg_vn_vector"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="VN"
                android:textColor="#888888"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Game Cards -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- PUBG MOBILE Global -->
            <LinearLayout
                android:id="@+id/card_global"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/game_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="PUBG MOBILE Global"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Version: 3.7.0 • Type: "
                            android:textColor="#888888"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Brutal"
                            android:textColor="#FF4444"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginTop="-2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Download Size: 1.08 GB"
                            android:textColor="#888888"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_pubg_global_vector"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/btn_download_global"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Download"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="@drawable/btn_download_bg"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btn_update_global"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Update"
                        android:textColor="#4CAF50"
                        android:textStyle="bold"
                        android:background="@drawable/btn_update_bg"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- PUBG MOBILE KR -->
            <LinearLayout
                android:id="@+id/card_kr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/game_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="PUBG MOBILE KR"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Version: 3.7.0 • Type: "
                            android:textColor="#888888"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Safe"
                            android:textColor="#4CAF50"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginTop="-2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Download Size: 1.07 GB"
                            android:textColor="#888888"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_pubg_kr_vector"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/btn_download_kr"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Download"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="@drawable/btn_download_bg"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btn_update_kr"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Update"
                        android:textColor="#4CAF50"
                        android:textStyle="bold"
                        android:background="@drawable/btn_update_bg"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Bottom Action Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:background="#1A2B1A"
        android:gravity="center"
        android:paddingHorizontal="32dp">

        <TextView
            android:id="@+id/action_logout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Logout"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:padding="12dp" />

        <TextView
            android:id="@+id/action_clear_data"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Clear Data"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:padding="12dp" />

        <TextView
            android:id="@+id/action_help"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Help"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:padding="12dp" />

    </LinearLayout>

</LinearLayout>
