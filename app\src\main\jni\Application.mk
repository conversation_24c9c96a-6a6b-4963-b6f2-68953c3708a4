# Application.mk for BearMod Loader
# This file configures the NDK build system

# Target ABI (ARM 64-bit only)
APP_ABI := arm64-v8a

# Platform version (Android 12+ / API 30+)
APP_PLATFORM := android-30

# STL library
APP_STL := c++_shared

# Build mode
APP_BUILD_SCRIPT := Android.mk

# Optimization flags
APP_CPPFLAGS := -std=c++17 -frtti -fexceptions

# Enable all warnings but don't treat them as errors
APP_CFLAGS := -Wall -Wno-error

# Strip symbols in release builds
ifeq ($(APP_OPTIM),release)
    APP_CFLAGS += -O3 -DNDEBUG
    APP_LDFLAGS += -Wl,--strip-all
else
    APP_CFLAGS += -O0 -g -DDEBUG
endif

# Allow undefined symbols (for dynamic linking)
APP_ALLOW_MISSING_DEPS := true

# Short commands for faster builds
APP_SHORT_COMMANDS := true
