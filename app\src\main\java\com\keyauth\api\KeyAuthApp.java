package com.keyauth.api;

import android.util.Log;

import androidx.annotation.NonNull;

import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class KeyAuthApp {
    private static final String TAG = "KeyAuthApp";
    private static final String API_URL = "https://enc.mod-key.click/1.2/";
    
    private boolean initialized = false;
    private String response = "";
    private String expiryDate = "";
    private String registrationDate = "";
    private String username = "";
    private boolean isLoggedIn = false;
    
    private final String appName;
    private final String appOwner;
    private final String appVersion;
    private final String hwid;
    
    public KeyAuthApp(String appName, String appOwner, String appVersion, String hwid) {
        this.appName = appName;
        this.appOwner = appOwner;
        this.appVersion = appVersion;
        this.hwid = hwid;
    }
    
    public boolean init() {
        try {
            String data = "type=init&name=" + appName + 
                         "&ownerid=" + appOwner + 
                         "&ver=" + appVersion + 
                         "&hwid=" + hwid;
            
            String response = sendRequest("init", data);
            JSONObject jsonResponse = new JSONObject(response);
            
            if (jsonResponse.getBoolean("success")) {
                initialized = true;
                this.response = jsonResponse.getString("message");
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error initializing KeyAuth: " + e.getMessage());
        }
        return false;
    }
    
    public boolean isInitialized() {
        return initialized;
    }
    
    public String getResponse() {
        return response;
    }
    
    public boolean license(String licenseKey) {
        if (!initialized) return false;
        
        try {
            String data = "type=license&key=" + licenseKey + 
                         "&hwid=" + hwid;
            
            String response = sendRequest("license", data);
            JSONObject jsonResponse = new JSONObject(response);
            
            if (jsonResponse.getBoolean("success")) {
                isLoggedIn = true;
                username = jsonResponse.getString("username");
                expiryDate = jsonResponse.getString("expiry");
                registrationDate = jsonResponse.getString("createdate");
                this.response = jsonResponse.getString("message");
                return true;
            } else {
                this.response = jsonResponse.getString("message");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error validating license: " + e.getMessage());
        }
        return false;
    }
    
    public String getExpiryDate() {
        return expiryDate;
    }
    
    public String getRegistrationDate() {
        return registrationDate;
    }
    
    public String getUsername() {
        return username;
    }
    
    public boolean isLoggedIn() {
        return isLoggedIn;
    }
    
    public void logout() {
        isLoggedIn = false;
        username = "";
        expiryDate = "";
        registrationDate = "";
        response = "Logged out successfully";
    }
    
    private String sendRequest(String endpoint, String data) throws Exception {
        HttpURLConnection connection = getHttpURLConnection(endpoint, data);

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                return response.toString();
            }
        } else {
            throw new Exception("HTTP error: " + responseCode);
        }
    }

    @NonNull
    private static HttpURLConnection getHttpURLConnection(String endpoint, String data) throws IOException {
        URL url = new URL(API_URL + endpoint);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        connection.setRequestProperty("User-Agent", "Android KeyAuth Client");
        connection.setRequestProperty("Accept", "application/json");
        connection.setDoOutput(true);

        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = data.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        return connection;
    }
}
