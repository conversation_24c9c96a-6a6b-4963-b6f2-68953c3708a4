{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8c95fb1bf71448479a8aeb756e068ef5\\transformed\\exoplayer-ui-2.19.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,689,769,837,915,992,1048,1109,1183,1257,1319,1380,1439,1504,1592,1677,1765,1828,1895,1960,2016,2090,2163,2224,2287,2339,2397,2444,2505,2562,2624,2681,2742,2798,2853,2916,2978,3041,3090,3142,3208,3274", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,87,84,87,62,66,64,55,73,72,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "282,445,603,684,764,832,910,987,1043,1104,1178,1252,1314,1375,1434,1499,1587,1672,1760,1823,1890,1955,2011,2085,2158,2219,2282,2334,2392,2439,2500,2557,2619,2676,2737,2793,2848,2911,2973,3036,3085,3137,3203,3269,3318"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,545,4804,4885,4965,5033,5111,5188,5244,5305,5379,5453,5515,5576,5635,5700,5788,5873,5961,6024,6091,6156,6212,6286,6359,6420,7042,7094,7152,7199,7260,7317,7379,7436,7497,7553,7608,7671,7733,7796,7845,7897,7963,8029", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,80,79,67,77,76,55,60,73,73,61,60,58,64,87,84,87,62,66,64,55,73,72,60,62,51,57,46,60,56,61,56,60,55,54,62,61,62,48,51,65,65,48", "endOffsets": "377,540,698,4880,4960,5028,5106,5183,5239,5300,5374,5448,5510,5571,5630,5695,5783,5868,5956,6019,6086,6151,6207,6281,6354,6415,6478,7089,7147,7194,7255,7312,7374,7431,7492,7548,7603,7666,7728,7791,7840,7892,7958,8024,8073"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\21d61e9b45cfab2ee4b69c56a0b52398\\transformed\\appcompat-1.7.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,945,1038,1138,1220,1317,1425,1502,1577,1669,1763,1860,1956,2051,2145,2241,2333,2425,2517,2595,2691,2786,2881,2978,3074,3172,3322,12584", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "940,1033,1133,1215,1312,1420,1497,1572,1664,1758,1855,1951,2046,2140,2236,2328,2420,2512,2590,2686,2781,2876,2973,3069,3167,3317,3411,12658"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\9f99db3bdee5091140633361bed826be\\transformed\\material-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,912,974,1052,1112,1172,1250,1311,1369,1425,1485,1543,1597,1682,1738,1796,1850,1915,2007,2081,2153,2235,2309,2386,2506,2569,2632,2731,2808,2882,2932,2983,3049,3112,3180,3251,3322,3383,3454,3521,3583,3670,3749,3814,3897,3982,4056,4120,4196,4244,4317,4381,4457,4535,4597,4661,4724,4790,4870,4948,5024,5103,5157,5212,5281,5356,5429", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "242,306,368,435,505,582,676,783,856,907,969,1047,1107,1167,1245,1306,1364,1420,1480,1538,1592,1677,1733,1791,1845,1910,2002,2076,2148,2230,2304,2381,2501,2564,2627,2726,2803,2877,2927,2978,3044,3107,3175,3246,3317,3378,3449,3516,3578,3665,3744,3809,3892,3977,4051,4115,4191,4239,4312,4376,4452,4530,4592,4656,4719,4785,4865,4943,5019,5098,5152,5207,5276,5351,5424,5494"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "703,3416,3480,3542,3609,3679,4417,4511,4618,4691,4742,8078,8156,8216,8276,8354,8415,8473,8529,8589,8647,8701,8786,8842,8900,8954,9019,9111,9185,9257,9339,9413,9490,9610,9673,9736,9835,9912,9986,10036,10087,10153,10216,10284,10355,10426,10487,10558,10625,10687,10774,10853,10918,11001,11086,11160,11224,11300,11348,11421,11485,11561,11639,11701,11765,11828,11894,11974,12052,12128,12207,12261,12515,12663,12738,12811", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,63,61,66,69,76,93,106,72,50,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,71,81,73,76,119,62,62,98,76,73,49,50,65,62,67,70,70,60,70,66,61,86,78,64,82,84,73,63,75,47,72,63,75,77,61,63,62,65,79,77,75,78,53,54,68,74,72,69", "endOffsets": "845,3475,3537,3604,3674,3751,4506,4613,4686,4737,4799,8151,8211,8271,8349,8410,8468,8524,8584,8642,8696,8781,8837,8895,8949,9014,9106,9180,9252,9334,9408,9485,9605,9668,9731,9830,9907,9981,10031,10082,10148,10211,10279,10350,10421,10482,10553,10620,10682,10769,10848,10913,10996,11081,11155,11219,11295,11343,11416,11480,11556,11634,11696,11760,11823,11889,11969,12047,12123,12202,12256,12311,12579,12733,12806,12876"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8fcff5946e9cb42bb9e1450a210c2616\\transformed\\navigation-ui-2.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,99", "endOffsets": "149,249"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "12316,12415", "endColumns": "98,99", "endOffsets": "12410,12510"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\50cbe4bff5d1390b137919b7462144e4\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3756,3848,3947,4041,4135,4228,4321,12881", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3843,3942,4036,4130,4223,4316,4412,12977"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\eef25481716fc7890be7aca21c0506bf\\transformed\\exoplayer-core-2.19.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,224,277,347,401,477,555", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "106,162,219,272,342,396,472,550,609"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6483,6539,6595,6652,6705,6775,6829,6905,6983", "endColumns": "55,55,56,52,69,53,75,77,58", "endOffsets": "6534,6590,6647,6700,6770,6824,6900,6978,7037"}}]}]}