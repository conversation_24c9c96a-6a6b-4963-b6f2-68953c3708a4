<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_download_progress"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="16dp"
    android:visibility="gone"
    app:cardBackgroundColor="@color/background_light"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    app:strokeColor="@color/success"
    app:strokeWidth="1dp"
    style="@style/Widget.Material3.CardView.Elevated"
    tools:visibility="visible">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Header with Icon and Title -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/progress_circular"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:indeterminate="false"
                android:progress="25"
                app:indicatorColor="#00FF3A"
                app:indicatorSize="32dp"
                app:trackColor="@color/background_light"
                app:trackThickness="3dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:text="@string/download_progress"
                android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                android:textColor="@color/text_primary" />

            <!-- Status Chip -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_download_status"
                style="@style/Widget.Material3.Chip.Assist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/downloading_status"
                android:textColor="#FFFFFF"
                app:chipBackgroundColor="@color/success"
                app:chipCornerRadius="8dp"
                app:chipIcon="@drawable/ic_download"
                app:chipIconTint="@color/success"
                app:chipMinHeight="32dp"
                tools:ignore="TouchTargetSizeCheck" />

        </LinearLayout>


        <TextView
            android:id="@+id/tv_download_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
            android:textColor="@color/on_primary"
            tools:text="Downloading... 45.2 MB / 175.6 MB • 12.3 MB/s • ETA: 1:23" />


        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progress_download"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:progress="25"
            app:indicatorColor="@color/warning"
            app:trackColor="@color/surface_container_high"
            app:trackCornerRadius="4dp"
            app:trackThickness="8dp" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_download_percentage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="@color/divider"
                android:textStyle="bold"
                tools:text="25%" />

            <TextView
                android:id="@+id/tv_download_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.LabelMedium"
                android:textColor="@color/accent"
                android:textStyle="bold"
                tools:text="12.3 MB/s" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">


            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                app:cardBackgroundColor="@color/background"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/tv_downloaded_size"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="@color/accent_dark"
                        android:textStyle="bold"
                        tools:text="45.2 MB" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="@string/downloaded"
                        android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                        android:textColor="@color/on_primary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- ETA Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                app:cardBackgroundColor="@color/black"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <TextView
                        android:id="@+id/tv_eta"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="@color/accent_dark"
                        android:textStyle="bold"
                        tools:text="1:23" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="@string/eta"
                        android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_pause_resume"
                style="@style/Widget.BearLoader.Button.Tonal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="@string/pause"
                app:icon="@drawable/ic_pause" />

            <Button
                android:id="@+id/btn_cancel_download"
                style="@style/Widget.BearLoader.Button.Secondary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="@string/cancel_download"
                app:icon="@drawable/ic_close" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
