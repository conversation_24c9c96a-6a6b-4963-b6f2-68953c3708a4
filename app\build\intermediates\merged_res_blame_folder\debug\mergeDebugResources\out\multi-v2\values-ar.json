{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-ar/values-ar.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1234,1297,1388,1457,1524,1624,1687,1752,1813,1881,1943,2001,2115,2175,2236,2293,2366,2489,2570,2662,2769,2867,2947,3095,3176,3257,3385,3474,3550,3603,3657,3723,3801,3881,3952,4034,4106,4180,4253,4323,4432,4523,4594,4684,4779,4853,4936,5029,5078,5159,5228,5314,5399,5461,5525,5588,5657,5766,5876,5973,6073,6130,6188,6268,6347,6422", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1229,1292,1383,1452,1519,1619,1682,1747,1808,1876,1938,1996,2110,2170,2231,2288,2361,2484,2565,2657,2764,2862,2942,3090,3171,3252,3380,3469,3545,3598,3652,3718,3796,3876,3947,4029,4101,4175,4248,4318,4427,4518,4589,4679,4774,4848,4931,5024,5073,5154,5223,5309,5394,5456,5520,5583,5652,5761,5871,5968,6068,6125,6183,6263,6342,6417,6493"}, "to": {"startLines": "27,62,63,64,65,66,74,75,76,77,78,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,193,195,196,197", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1409,4428,4506,4582,4666,4758,5553,5654,5773,5850,5909,9733,9824,9893,9960,10060,10123,10188,10249,10317,10379,10437,10551,10611,10672,10729,10802,10925,11006,11098,11205,11303,11383,11531,11612,11693,11821,11910,11986,12039,12093,12159,12237,12317,12388,12470,12542,12616,12689,12759,12868,12959,13030,13120,13215,13289,13372,13465,13514,13595,13664,13750,13835,13897,13961,14024,14093,14202,14312,14409,14509,14566,14845,15007,15086,15161", "endLines": "34,62,63,64,65,66,74,75,76,77,78,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,193,195,196,197", "endColumns": "12,77,75,83,91,82,100,118,76,58,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,91,106,97,79,147,80,80,127,88,75,52,53,65,77,79,70,81,71,73,72,69,108,90,70,89,94,73,82,92,48,80,68,85,84,61,63,62,68,108,109,96,99,56,57,79,78,74,75", "endOffsets": "1769,4501,4577,4661,4753,4836,5649,5768,5845,5904,5967,9819,9888,9955,10055,10118,10183,10244,10312,10374,10432,10546,10606,10667,10724,10797,10920,11001,11093,11200,11298,11378,11526,11607,11688,11816,11905,11981,12034,12088,12154,12232,12312,12383,12465,12537,12611,12684,12754,12863,12954,13025,13115,13210,13284,13367,13460,13509,13590,13659,13745,13830,13892,13956,14019,14088,14197,14307,14404,14504,14561,14619,14920,15081,15156,15232"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7913,7978,8037,8104,8166,8248,8329,8430,8525", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "7973,8032,8099,8161,8243,8324,8425,8520,8604"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,110", "endOffsets": "160,271"}, "to": {"startLines": "191,192", "startColumns": "4,4", "startOffsets": "14624,14734", "endColumns": "109,110", "endOffsets": "14729,14840"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "67,68,69,70,71,72,73,198", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4841,4934,5036,5131,5234,5337,5439,15237", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "4929,5031,5126,5229,5332,5434,5548,15333"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1774,1882,1986,2093,2175,2276,2390,2470,2549,2640,2733,2825,2919,3019,3112,3207,3300,3391,3485,3564,3669,3767,3865,3973,4073,4176,4331,14925", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "1877,1981,2088,2170,2271,2385,2465,2544,2635,2728,2820,2914,3014,3107,3202,3295,3386,3480,3559,3664,3762,3860,3968,4068,4171,4326,4423,15002"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,4006,4072,4124,4181,4252,4323", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,4001,4067,4119,4176,4247,4318,4374"}, "to": {"startLines": "2,11,19,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,444,944,5972,6051,6129,6205,6299,6391,6465,6530,6622,6712,6782,6846,6909,6978,7086,7195,7310,7376,7459,7531,7603,7695,7786,7850,8609,8662,8733,8788,8849,8907,8981,9045,9109,9169,9234,9298,9360,9426,9478,9535,9606,9677", "endLines": "10,18,26,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "439,939,1404,6046,6124,6200,6294,6386,6460,6525,6617,6707,6777,6841,6904,6973,7081,7190,7305,7371,7454,7526,7598,7690,7781,7845,7908,8657,8728,8783,8844,8902,8976,9040,9104,9164,9229,9293,9355,9421,9473,9530,9601,9672,9728"}}]}]}