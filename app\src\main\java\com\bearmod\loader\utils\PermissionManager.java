package com.bearmod.loader.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.util.Log;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AlertDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * Permission manager for handling storage and other permissions
 * Handles different Android versions and permission requirements
 */
public class PermissionManager {

    private static final String TAG = "PermissionManager";
    
    // Permission request codes
    public static final int REQUEST_STORAGE_PERMISSIONS = 1001;
    public static final int REQUEST_INSTALL_PACKAGES = 1002;
    public static final int REQUEST_MANAGE_EXTERNAL_STORAGE = 1003;

    // Required permissions for different Android versions
    private static final String[] STORAGE_PERMISSIONS_LEGACY = {
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    private static final String[] STORAGE_PERMISSIONS_API_33 = {
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.READ_MEDIA_VIDEO,
        Manifest.permission.READ_MEDIA_AUDIO
    };

    /**
     * Check if all required storage permissions are granted
     */
    public static boolean hasStoragePermissions(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+) - Check for MANAGE_EXTERNAL_STORAGE
            return Environment.isExternalStorageManager();
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) - Check media permissions
            for (String permission : STORAGE_PERMISSIONS_API_33) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
            return true;
        } else {
            // Android 10 and below - Check legacy storage permissions
            for (String permission : STORAGE_PERMISSIONS_LEGACY) {
                if (ContextCompat.checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * Check if install packages permission is granted
     */
    public static boolean hasInstallPackagesPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return context.getPackageManager().canRequestPackageInstalls();
        }
        return true; // Not required on older versions
    }

    /**
     * Request storage permissions based on Android version
     */
    public static void requestStoragePermissions(Activity activity, PermissionCallback callback) {
        Log.d(TAG, "Requesting storage permissions for Android " + Build.VERSION.SDK_INT);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+) - Request MANAGE_EXTERNAL_STORAGE
            requestManageExternalStoragePermission(activity, callback);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) - Request media permissions
            requestMediaPermissions(activity, callback);
        } else {
            // Android 10 and below - Request legacy storage permissions
            requestLegacyStoragePermissions(activity, callback);
        }
    }

    /**
     * Request MANAGE_EXTERNAL_STORAGE permission for Android 11+
     */
    private static void requestManageExternalStoragePermission(Activity activity, PermissionCallback callback) {
        if (Environment.isExternalStorageManager()) {
            callback.onPermissionResult(true, "Storage permissions already granted");
            return;
        }

        // Show explanation dialog
        new AlertDialog.Builder(activity)
            .setTitle("Storage Permission Required")
            .setMessage("BearMod Loader needs access to all files to download and manage game files (APK/OBB). " +
                       "This permission is required for:\n\n" +
                       "• Downloading APK files\n" +
                       "• Managing OBB files in Android/obb directory\n" +
                       "• Installing downloaded games\n\n" +
                       "Please grant 'All files access' permission in the next screen.")
            .setPositiveButton("Grant Permission", (dialog, which) -> {
                try {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                    intent.setData(Uri.parse("package:" + activity.getPackageName()));
                    activity.startActivityForResult(intent, REQUEST_MANAGE_EXTERNAL_STORAGE);
                } catch (Exception e) {
                    Log.e(TAG, "Failed to open manage external storage settings", e);
                    // Fallback to general settings
                    Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                    activity.startActivityForResult(intent, REQUEST_MANAGE_EXTERNAL_STORAGE);
                }
            })
            .setNegativeButton("Cancel", (dialog, which) -> {
                callback.onPermissionResult(false, "Storage permission denied");
            })
            .setCancelable(false)
            .show();
    }

    /**
     * Request media permissions for Android 13+
     */
    private static void requestMediaPermissions(Activity activity, PermissionCallback callback) {
        List<String> permissionsToRequest = new ArrayList<>();
        
        for (String permission : STORAGE_PERMISSIONS_API_33) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission);
            }
        }

        if (permissionsToRequest.isEmpty()) {
            callback.onPermissionResult(true, "Media permissions already granted");
            return;
        }

        // Show explanation dialog
        new AlertDialog.Builder(activity)
            .setTitle("Media Access Permission Required")
            .setMessage("BearMod Loader needs access to media files to manage downloaded content. " +
                       "This includes images, videos, and audio files related to game downloads.")
            .setPositiveButton("Grant Permission", (dialog, which) -> {
                ActivityCompat.requestPermissions(activity, 
                    permissionsToRequest.toArray(new String[0]), 
                    REQUEST_STORAGE_PERMISSIONS);
            })
            .setNegativeButton("Cancel", (dialog, which) -> {
                callback.onPermissionResult(false, "Media permissions denied");
            })
            .setCancelable(false)
            .show();
    }

    /**
     * Request legacy storage permissions for Android 10 and below
     */
    private static void requestLegacyStoragePermissions(Activity activity, PermissionCallback callback) {
        List<String> permissionsToRequest = new ArrayList<>();
        
        for (String permission : STORAGE_PERMISSIONS_LEGACY) {
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                permissionsToRequest.add(permission);
            }
        }

        if (permissionsToRequest.isEmpty()) {
            callback.onPermissionResult(true, "Storage permissions already granted");
            return;
        }

        // Show explanation dialog
        new AlertDialog.Builder(activity)
            .setTitle("Storage Permission Required")
            .setMessage("BearMod Loader needs access to external storage to download and manage game files. " +
                       "This permission is required for downloading APK and OBB files.")
            .setPositiveButton("Grant Permission", (dialog, which) -> {
                ActivityCompat.requestPermissions(activity, 
                    permissionsToRequest.toArray(new String[0]), 
                    REQUEST_STORAGE_PERMISSIONS);
            })
            .setNegativeButton("Cancel", (dialog, which) -> {
                callback.onPermissionResult(false, "Storage permissions denied");
            })
            .setCancelable(false)
            .show();
    }

    /**
     * Request install packages permission
     */
    public static void requestInstallPackagesPermission(Activity activity, PermissionCallback callback) {
        if (hasInstallPackagesPermission(activity)) {
            callback.onPermissionResult(true, "Install packages permission already granted");
            return;
        }

        // Show explanation dialog
        new AlertDialog.Builder(activity)
            .setTitle("Install Permission Required")
            .setMessage("BearMod Loader needs permission to install downloaded APK files. " +
                       "This permission allows automatic installation of downloaded games.")
            .setPositiveButton("Grant Permission", (dialog, which) -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                    intent.setData(Uri.parse("package:" + activity.getPackageName()));
                    activity.startActivityForResult(intent, REQUEST_INSTALL_PACKAGES);
                }
            })
            .setNegativeButton("Cancel", (dialog, which) -> {
                callback.onPermissionResult(false, "Install packages permission denied");
            })
            .setCancelable(false)
            .show();
    }

    /**
     * Handle permission request results
     */
    public static void handlePermissionResult(Activity activity, int requestCode, String[] permissions, 
                                            int[] grantResults, PermissionCallback callback) {
        switch (requestCode) {
            case REQUEST_STORAGE_PERMISSIONS:
                boolean allGranted = true;
                for (int result : grantResults) {
                    if (result != PackageManager.PERMISSION_GRANTED) {
                        allGranted = false;
                        break;
                    }
                }
                
                if (allGranted) {
                    callback.onPermissionResult(true, "Storage permissions granted");
                } else {
                    callback.onPermissionResult(false, "Storage permissions denied");
                }
                break;

            case REQUEST_MANAGE_EXTERNAL_STORAGE:
                if (Environment.isExternalStorageManager()) {
                    callback.onPermissionResult(true, "All files access granted");
                } else {
                    callback.onPermissionResult(false, "All files access denied");
                }
                break;

            case REQUEST_INSTALL_PACKAGES:
                if (hasInstallPackagesPermission(activity)) {
                    callback.onPermissionResult(true, "Install packages permission granted");
                } else {
                    callback.onPermissionResult(false, "Install packages permission denied");
                }
                break;
        }
    }

    /**
     * Request all required permissions
     */
    public static void requestAllPermissions(Activity activity, PermissionCallback callback) {
        Log.d(TAG, "Requesting all required permissions");

        // First check storage permissions
        if (!hasStoragePermissions(activity)) {
            requestStoragePermissions(activity, new PermissionCallback() {
                @Override
                public void onPermissionResult(boolean granted, String message) {
                    if (granted) {
                        // Storage granted, now check install permission
                        if (!hasInstallPackagesPermission(activity)) {
                            requestInstallPackagesPermission(activity, callback);
                        } else {
                            callback.onPermissionResult(true, "All permissions granted");
                        }
                    } else {
                        callback.onPermissionResult(false, message);
                    }
                }
            });
        } else if (!hasInstallPackagesPermission(activity)) {
            requestInstallPackagesPermission(activity, callback);
        } else {
            callback.onPermissionResult(true, "All permissions already granted");
        }
    }

    /**
     * Permission callback interface
     */
    public interface PermissionCallback {
        void onPermissionResult(boolean granted, String message);
    }
}
