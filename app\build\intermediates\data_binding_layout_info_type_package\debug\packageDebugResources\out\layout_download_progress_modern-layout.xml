<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_download_progress_modern" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\layout_download_progress_modern.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/card_download_progress"><Targets><Target id="@+id/card_download_progress" tag="layout/layout_download_progress_modern_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="233" endOffset="51"/></Target><Target id="@+id/progress_circular" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="30" startOffset="12" endLine="39" endOffset="42"/></Target><Target id="@+id/chip_download_status" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="51" startOffset="12" endLine="63" endOffset="53"/></Target><Target id="@+id/tv_download_status" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="75" endOffset="84"/></Target><Target id="@+id/progress_download" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="78" startOffset="8" endLine="87" endOffset="38"/></Target><Target id="@+id/tv_download_percentage" view="TextView"><Expressions/><location startLine="96" startOffset="12" endLine="104" endOffset="34"/></Target><Target id="@+id/tv_download_speed" view="TextView"><Expressions/><location startLine="106" startOffset="12" endLine="113" endOffset="40"/></Target><Target id="@+id/tv_downloaded_size" view="TextView"><Expressions/><location startLine="141" startOffset="20" endLine="148" endOffset="46"/></Target><Target id="@+id/tv_eta" view="TextView"><Expressions/><location startLine="179" startOffset="20" endLine="186" endOffset="43"/></Target><Target id="@+id/btn_pause_resume" view="Button"><Expressions/><location startLine="209" startOffset="12" endLine="217" endOffset="47"/></Target><Target id="@+id/btn_cancel_download" view="Button"><Expressions/><location startLine="219" startOffset="12" endLine="227" endOffset="47"/></Target></Targets></Layout>