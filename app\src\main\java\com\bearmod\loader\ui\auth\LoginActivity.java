package com.bearmod.loader.ui.auth;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.Toast;
import android.content.SharedPreferences;
import android.widget.CheckBox;
import android.widget.TextView;
import android.content.ClipData;
import android.content.ClipboardManager;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.BearLoaderApplication;
import com.bearmod.loader.R;
import com.bearmod.loader.auth.KeyAuthManager;
import com.bearmod.loader.auth.AuthResult;
import com.bearmod.loader.ui.splash.SplashActivity;
import com.google.android.material.textfield.TextInputLayout;
import com.google.android.material.textfield.TextInputEditText;

public class LoginActivity extends AppCompatActivity {
    // Use EncryptedSharedPreferences if available for secure key storage
    private SharedPreferences securePrefs;
    private TextInputLayout textInputLayout;
    private TextInputEditText editLicenseKey;
    private Button btnLogin;
    private ProgressBar progressBar;
    private CheckBox checkboxRemember;
    private CheckBox checkboxAutoLogin;
    private TextView versionText;
    private SharedPreferences prefs;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        // Try to use EncryptedSharedPreferences for secure key storage
        try {
            securePrefs = androidx.security.crypto.EncryptedSharedPreferences.create(
                "bearmod_secure_prefs",
                androidx.security.crypto.MasterKeys.getOrCreate(androidx.security.crypto.MasterKeys.AES256_GCM_SPEC),
                this,
                androidx.security.crypto.EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                androidx.security.crypto.EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
        } catch (Throwable t) {
            // Fallback to normal prefs if EncryptedSharedPreferences is not available
            securePrefs = prefs;
        }
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        textInputLayout = findViewById(R.id.textInputLayout);
        editLicenseKey = findViewById(R.id.editLicenseKey);
        btnLogin = findViewById(R.id.btnLogin);
        progressBar = findViewById(R.id.progressBar);
        checkboxRemember = findViewById(R.id.checkboxRemember);
        checkboxAutoLogin = findViewById(R.id.checkboxAutoLogin);
        versionText = findViewById(R.id.versionText);
        prefs = getSharedPreferences("bearmod_prefs", MODE_PRIVATE);

        // Restore preferences
        checkboxRemember.setChecked(prefs.getBoolean("remember_key", false));
        checkboxAutoLogin.setChecked(prefs.getBoolean("auto_login", false));
        String savedKey = prefs.getString("license_key", "");
        if (checkboxRemember.isChecked() && !savedKey.isEmpty()) {
            editLicenseKey.setText(savedKey);
        }

        // Set version
        try {
            String version = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
            versionText.setText(version);
        } catch (Exception e) {
            versionText.setText("1.0.0");
        }

        // Paste from clipboard when end icon is clicked
        textInputLayout.setEndIconOnClickListener(v -> {
            ClipboardManager clipboard = (ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
            if (clipboard != null && clipboard.hasPrimaryClip()) {
                ClipData.Item item = clipboard.getPrimaryClip().getItemAt(0);
                editLicenseKey.setText(item.getText());
            }
        });

        btnLogin.setOnClickListener(v -> attemptLogin());

        // Clear error on input change
        editLicenseKey.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                textInputLayout.setError(null);
            }
            @Override
            public void afterTextChanged(android.text.Editable s) {}
        });

        // Auto-login if enabled
        if (checkboxAutoLogin.isChecked() && !savedKey.isEmpty()) {
            attemptLogin();
        }
    }

    private void attemptLogin() {
        String licenseKey = editLicenseKey.getText() != null ? editLicenseKey.getText().toString().trim() : "";
        if (licenseKey.isEmpty()) {
            textInputLayout.setError("Enter your license key");
            return;
        } else {
            textInputLayout.setError(null);
        }
        btnLogin.setEnabled(false);
        progressBar.setVisibility(View.VISIBLE);
        // Mask key in logs
        String maskedKey = licenseKey.length() > 4 ? "****" + licenseKey.substring(licenseKey.length() - 4) : "****";
        android.util.Log.i("LoginActivity", "Attempting login with key: " + maskedKey);
        KeyAuthManager.getInstance().login(licenseKey, new KeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                BearLoaderApplication.getInstance().setLoggedIn(true);
                BearLoaderApplication.getInstance().saveLicenseKey(licenseKey);
                // Save preferences (normal)
                prefs.edit()
                    .putBoolean("remember_key", checkboxRemember.isChecked())
                    .putBoolean("auto_login", checkboxAutoLogin.isChecked())
                    .putString("license_key", checkboxRemember.isChecked() ? licenseKey : "")
                    .apply();
                // Save securely if possible
                if (checkboxRemember.isChecked()) {
                    securePrefs.edit().putString("license_key", licenseKey).apply();
                } else {
                    securePrefs.edit().remove("license_key").apply();
                }
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    Toast.makeText(LoginActivity.this, "Login successful!", Toast.LENGTH_SHORT).show();
                    Intent intent = new Intent(LoginActivity.this, SplashActivity.class);
                    startActivity(intent);
                    finish();
                });
            }
            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    progressBar.setVisibility(View.GONE);
                    btnLogin.setEnabled(true);
                    textInputLayout.setError(error);
                    Toast.makeText(LoginActivity.this, error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
}
