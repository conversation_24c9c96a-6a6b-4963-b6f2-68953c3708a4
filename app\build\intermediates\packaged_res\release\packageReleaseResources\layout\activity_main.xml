<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.main.MainActivity"
    tools:openDrawer="start">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/Theme.BearLoader.NoActionBar">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/primary"
                app:popupTheme="@style/Theme.BearLoader.NoActionBar"
                app:title="@string/dashboard_title"
                app:titleTextColor="@color/white" />

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/background"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">


            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_target_app"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardBackgroundColor="@color/card_background"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/target_app"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/spinner_target_app"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:background="@color/white"
                        android:minHeight="48dp"
                        android:padding="8dp" />

                    <Button
                        android:id="@+id/btn_scan_offsets"
                        style="@style/Widget.BearLoader.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/scan_offsets" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <TextView
                android:id="@+id/tv_available_patches"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="24dp"
                android:text="@string/available_patches"
                android:textColor="@color/background"
                android:textSize="20sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/card_target_app" />

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_available_patches">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_patches"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:listitem="@layout/item_patch" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>


            <com.facebook.shimmer.ShimmerFrameLayout
                android:id="@+id/shimmer_layout"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="8dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_available_patches"
                android:elevation="10dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <include layout="@layout/item_patch_shimmer" />
                    <include layout="@layout/item_patch_shimmer" />
                    <include layout="@layout/item_patch_shimmer" />
                    <include layout="@layout/item_patch_shimmer" />

                </LinearLayout>

            </com.facebook.shimmer.ShimmerFrameLayout>


            <LinearLayout
                android:id="@+id/layout_empty"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_available_patches">

                <ImageView
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:contentDescription="@string/no_patches_available"
                    android:src="@drawable/ic_empty" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/no_patches_available"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/btn_download_patches"
                    style="@style/Widget.BearLoader.Button.Secondary"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="@string/download_patches" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:background="@color/surface"
            app:itemIconTint="@color/primary"
            app:itemTextColor="@color/primary"
            app:menu="@menu/bottom_nav_menu"
            app:elevation="8dp" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@color/background_light"
        android:fitsSystemWindows="true"
        app:headerLayout="@layout/nav_header"
        app:itemIconTint="@color/text_primary"
        app:itemTextColor="@color/text_primary"
        app:menu="@menu/drawer_menu" />

</androidx.drawerlayout.widget.DrawerLayout>
