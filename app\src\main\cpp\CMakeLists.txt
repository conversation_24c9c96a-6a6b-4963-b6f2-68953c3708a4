# CMakeLists.txt for BearMod Loader Native Security

cmake_minimum_required(VERSION 3.18.1)

# Declare the project
project("signature_verifier")

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add security flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fstack-protector-strong -D_FORTIFY_SOURCE=2")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wformat -Wformat-security")

# Add optimization flags for release
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")

# Find required packages
find_package(PkgConfig REQUIRED)

# Create the signature verifier library
add_library(
    signature_verifier
    SHARED
    signature_verifier.cpp
)

# Find and link OpenSSL
find_library(crypto-lib crypto)
find_library(ssl-lib ssl)

# Link libraries
target_link_libraries(
    signature_verifier
    ${crypto-lib}
    ${ssl-lib}
    android
    log
)

# Include directories
target_include_directories(
    signature_verifier
    PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Set properties
set_target_properties(
    signature_verifier
    PROPERTIES
    ANDROID_ARM_MODE arm
)

# Add preprocessor definitions
target_compile_definitions(
    signature_verifier
    PRIVATE
    ANDROID_NDK
    __ANDROID__
)

# Strip symbols in release builds
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    add_custom_command(
        TARGET signature_verifier
        POST_BUILD
        COMMAND ${CMAKE_STRIP} $<TARGET_FILE:signature_verifier>
    )
endif()
