<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/background_light"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_patch_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/btn_download"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="8dp"
            tools:text="Memory Patch v1.2" />

        <TextView
            android:id="@+id/tv_patch_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/background"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_patch_name"
            tools:text="This patch modifies memory values to enhance gameplay" />

        <TextView
            android:id="@+id/tv_game_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/background"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_patch_description"
            tools:text="Game Version: 1.0.5" />

        <TextView
            android:id="@+id/tv_update_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/background"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_patch_description"
            tools:text="Updated: 2023-06-15" />

        <!-- Download Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_download"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:backgroundTint="@color/accent"
            android:icon="@drawable/ic_download"
            android:iconTint="@color/white"
            android:text=""
            app:cornerRadius="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_status"
            android:paddingStart="8dp"
            android:paddingTop="2dp"
            android:paddingEnd="8dp"
            android:paddingBottom="2dp"
            android:textColor="#263238"
            android:textSize="12sp"
            android:layout_marginEnd="8dp"
            app:layout_constraintEnd_toStartOf="@+id/btn_download"
            app:layout_constraintTop_toTopOf="parent"
            tools:backgroundTint="@color/success"
            tools:text="Up to Date" />

        <Button
            android:id="@+id/btn_apply_patch"
            style="@style/Widget.BearLoader.Button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/apply_patch"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_game_version"
            tools:ignore="VisualLintButtonSize" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="8dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/btn_apply_patch"
            app:layout_constraintEnd_toEndOf="@+id/btn_apply_patch"
            app:layout_constraintTop_toTopOf="@+id/btn_apply_patch" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
