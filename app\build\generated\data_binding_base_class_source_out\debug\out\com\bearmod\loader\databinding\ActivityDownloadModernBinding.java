// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDownloadModernBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final Button btnDownload;

  @NonNull
  public final MaterialCardView cardDownloadInfo;

  @NonNull
  public final Chip chipFilterAll;

  @NonNull
  public final Chip chipFilterLatest;

  @NonNull
  public final ChipGroup chipGroupFilters;

  @NonNull
  public final Chip chipSortDate;

  @NonNull
  public final Chip chipSortSize;

  @NonNull
  public final TextInputEditText etSearch;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final CircularProgressIndicator progressLoading;

  @NonNull
  public final RecyclerView rvReleases;

  @NonNull
  public final TextInputLayout searchLayout;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvApkSize;

  @NonNull
  public final TextView tvObbSize;

  @NonNull
  public final TextView tvTotalSize;

  private ActivityDownloadModernBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull Button btnDownload,
      @NonNull MaterialCardView cardDownloadInfo, @NonNull Chip chipFilterAll,
      @NonNull Chip chipFilterLatest, @NonNull ChipGroup chipGroupFilters,
      @NonNull Chip chipSortDate, @NonNull Chip chipSortSize, @NonNull TextInputEditText etSearch,
      @NonNull LinearLayout layoutEmptyState, @NonNull CircularProgressIndicator progressLoading,
      @NonNull RecyclerView rvReleases, @NonNull TextInputLayout searchLayout,
      @NonNull SwipeRefreshLayout swipeRefresh, @NonNull MaterialToolbar toolbar,
      @NonNull TextView tvApkSize, @NonNull TextView tvObbSize, @NonNull TextView tvTotalSize) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.btnDownload = btnDownload;
    this.cardDownloadInfo = cardDownloadInfo;
    this.chipFilterAll = chipFilterAll;
    this.chipFilterLatest = chipFilterLatest;
    this.chipGroupFilters = chipGroupFilters;
    this.chipSortDate = chipSortDate;
    this.chipSortSize = chipSortSize;
    this.etSearch = etSearch;
    this.layoutEmptyState = layoutEmptyState;
    this.progressLoading = progressLoading;
    this.rvReleases = rvReleases;
    this.searchLayout = searchLayout;
    this.swipeRefresh = swipeRefresh;
    this.toolbar = toolbar;
    this.tvApkSize = tvApkSize;
    this.tvObbSize = tvObbSize;
    this.tvTotalSize = tvTotalSize;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDownloadModernBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDownloadModernBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_download_modern, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDownloadModernBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar_layout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btn_download;
      Button btnDownload = ViewBindings.findChildViewById(rootView, id);
      if (btnDownload == null) {
        break missingId;
      }

      id = R.id.card_download_info;
      MaterialCardView cardDownloadInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardDownloadInfo == null) {
        break missingId;
      }

      id = R.id.chip_filter_all;
      Chip chipFilterAll = ViewBindings.findChildViewById(rootView, id);
      if (chipFilterAll == null) {
        break missingId;
      }

      id = R.id.chip_filter_latest;
      Chip chipFilterLatest = ViewBindings.findChildViewById(rootView, id);
      if (chipFilterLatest == null) {
        break missingId;
      }

      id = R.id.chip_group_filters;
      ChipGroup chipGroupFilters = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupFilters == null) {
        break missingId;
      }

      id = R.id.chip_sort_date;
      Chip chipSortDate = ViewBindings.findChildViewById(rootView, id);
      if (chipSortDate == null) {
        break missingId;
      }

      id = R.id.chip_sort_size;
      Chip chipSortSize = ViewBindings.findChildViewById(rootView, id);
      if (chipSortSize == null) {
        break missingId;
      }

      id = R.id.et_search;
      TextInputEditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.layout_empty_state;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.progress_loading;
      CircularProgressIndicator progressLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressLoading == null) {
        break missingId;
      }

      id = R.id.rv_releases;
      RecyclerView rvReleases = ViewBindings.findChildViewById(rootView, id);
      if (rvReleases == null) {
        break missingId;
      }

      id = R.id.search_layout;
      TextInputLayout searchLayout = ViewBindings.findChildViewById(rootView, id);
      if (searchLayout == null) {
        break missingId;
      }

      id = R.id.swipe_refresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_apk_size;
      TextView tvApkSize = ViewBindings.findChildViewById(rootView, id);
      if (tvApkSize == null) {
        break missingId;
      }

      id = R.id.tv_obb_size;
      TextView tvObbSize = ViewBindings.findChildViewById(rootView, id);
      if (tvObbSize == null) {
        break missingId;
      }

      id = R.id.tv_total_size;
      TextView tvTotalSize = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSize == null) {
        break missingId;
      }

      return new ActivityDownloadModernBinding((CoordinatorLayout) rootView, appBarLayout,
          btnDownload, cardDownloadInfo, chipFilterAll, chipFilterLatest, chipGroupFilters,
          chipSortDate, chipSortSize, etSearch, layoutEmptyState, progressLoading, rvReleases,
          searchLayout, swipeRefresh, toolbar, tvApkSize, tvObbSize, tvTotalSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
