// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPatchExecutionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final MaterialButton btnStartPatching;

  @NonNull
  public final MaterialCardView cardLogs;

  @NonNull
  public final MaterialCardView cardMode;

  @NonNull
  public final ProgressBar progressPatching;

  @NonNull
  public final RadioGroup radioGroupMode;

  @NonNull
  public final RadioButton radioNonRoot;

  @NonNull
  public final RadioButton radioRoot;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvLogs;

  @NonNull
  public final TextView tvLogsTitle;

  @NonNull
  public final TextView tvPatchName;

  @NonNull
  public final View viewOverlay;

  private ActivityPatchExecutionBinding(@NonNull ConstraintLayout rootView,
      @NonNull MaterialButton btnStartPatching, @NonNull MaterialCardView cardLogs,
      @NonNull MaterialCardView cardMode, @NonNull ProgressBar progressPatching,
      @NonNull RadioGroup radioGroupMode, @NonNull RadioButton radioNonRoot,
      @NonNull RadioButton radioRoot, @NonNull Toolbar toolbar, @NonNull TextView tvLogs,
      @NonNull TextView tvLogsTitle, @NonNull TextView tvPatchName, @NonNull View viewOverlay) {
    this.rootView = rootView;
    this.btnStartPatching = btnStartPatching;
    this.cardLogs = cardLogs;
    this.cardMode = cardMode;
    this.progressPatching = progressPatching;
    this.radioGroupMode = radioGroupMode;
    this.radioNonRoot = radioNonRoot;
    this.radioRoot = radioRoot;
    this.toolbar = toolbar;
    this.tvLogs = tvLogs;
    this.tvLogsTitle = tvLogsTitle;
    this.tvPatchName = tvPatchName;
    this.viewOverlay = viewOverlay;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPatchExecutionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPatchExecutionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_patch_execution, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPatchExecutionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_start_patching;
      MaterialButton btnStartPatching = ViewBindings.findChildViewById(rootView, id);
      if (btnStartPatching == null) {
        break missingId;
      }

      id = R.id.card_logs;
      MaterialCardView cardLogs = ViewBindings.findChildViewById(rootView, id);
      if (cardLogs == null) {
        break missingId;
      }

      id = R.id.card_mode;
      MaterialCardView cardMode = ViewBindings.findChildViewById(rootView, id);
      if (cardMode == null) {
        break missingId;
      }

      id = R.id.progress_patching;
      ProgressBar progressPatching = ViewBindings.findChildViewById(rootView, id);
      if (progressPatching == null) {
        break missingId;
      }

      id = R.id.radio_group_mode;
      RadioGroup radioGroupMode = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupMode == null) {
        break missingId;
      }

      id = R.id.radio_non_root;
      RadioButton radioNonRoot = ViewBindings.findChildViewById(rootView, id);
      if (radioNonRoot == null) {
        break missingId;
      }

      id = R.id.radio_root;
      RadioButton radioRoot = ViewBindings.findChildViewById(rootView, id);
      if (radioRoot == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_logs;
      TextView tvLogs = ViewBindings.findChildViewById(rootView, id);
      if (tvLogs == null) {
        break missingId;
      }

      id = R.id.tv_logs_title;
      TextView tvLogsTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvLogsTitle == null) {
        break missingId;
      }

      id = R.id.tv_patch_name;
      TextView tvPatchName = ViewBindings.findChildViewById(rootView, id);
      if (tvPatchName == null) {
        break missingId;
      }

      id = R.id.view_overlay;
      View viewOverlay = ViewBindings.findChildViewById(rootView, id);
      if (viewOverlay == null) {
        break missingId;
      }

      return new ActivityPatchExecutionBinding((ConstraintLayout) rootView, btnStartPatching,
          cardLogs, cardMode, progressPatching, radioGroupMode, radioNonRoot, radioRoot, toolbar,
          tvLogs, tvLogsTitle, tvPatchName, viewOverlay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
