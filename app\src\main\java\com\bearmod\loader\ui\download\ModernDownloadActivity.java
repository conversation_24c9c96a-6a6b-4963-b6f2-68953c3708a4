package com.bearmod.loader.ui.download;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.bearmod.loader.R;
import com.bearmod.loader.databinding.ActivityFreshDownloadBinding;
import com.bearmod.loader.download.DownloadManager;
import com.bearmod.loader.utils.PermissionManager;

public class ModernDownloadActivity extends AppCompatActivity {

    private static final String TAG = "ModernDownloadActivity";

    private ActivityFreshDownloadBinding binding;
    private boolean isDownloading = false;
    private String currentRegion = "Global";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "Starting ModernDownloadActivity onCreate");

            // Use data binding to avoid casting issues
            binding = ActivityFreshDownloadBinding.inflate(getLayoutInflater());
            setContentView(binding.getRoot());

            setupClickListeners();

            Log.d(TAG, "ModernDownloadActivity onCreate completed successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate: " + e.getMessage(), e);
            Toast.makeText(this, "Error loading interface: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }



    private void setupClickListeners() {
        try {
            Log.d(TAG, "Setting up click listeners...");

            // Back button click
            binding.btnBack.setOnClickListener(v -> onBackPressed());

            // Header button clicks
            binding.btnSettings.setOnClickListener(v -> openSettings());
            binding.btnDownloads.setOnClickListener(v -> showDownloadsInfo());

            // Region tab clicks
            binding.tabGlobal.setOnClickListener(v -> selectRegion("Global"));
            binding.tabKr.setOnClickListener(v -> selectRegion("KR"));
            binding.tabTw.setOnClickListener(v -> selectRegion("TW"));
            binding.tabVn.setOnClickListener(v -> selectRegion("VN"));

            // Download button clicks
            binding.btnDownloadGlobal.setOnClickListener(v -> startDownload("PUBG MOBILE Global", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateGlobal.setOnClickListener(v -> updateGame("PUBG MOBILE Global"));
            binding.btnDownloadKr.setOnClickListener(v -> startDownload("PUBG MOBILE KR", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateKr.setOnClickListener(v -> updateGame("PUBG MOBILE KR"));
            binding.btnDownloadTw.setOnClickListener(v -> startDownload("PUBG MOBILE TW", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateTw.setOnClickListener(v -> updateGame("PUBG MOBILE TW"));
            binding.btnDownloadVn.setOnClickListener(v -> startDownload("PUBG MOBILE VN", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateVn.setOnClickListener(v -> updateGame("PUBG MOBILE VN"));

            // Bottom action clicks
            binding.actionLogout.setOnClickListener(v -> logout());
            binding.actionClearData.setOnClickListener(v -> clearData());
            binding.actionHelp.setOnClickListener(v -> showHelp());

            Log.d(TAG, "Click listeners set up successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up click listeners: " + e.getMessage(), e);
            throw e;
        }
    }
    
    private void selectRegion(String region) {
        currentRegion = region;

        // Update tab appearances
        updateTabAppearance();

        // Show appropriate cards
        updateCardVisibility();

        android.widget.Toast.makeText(this, "Selected " + region + " region",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void updateTabAppearance() {
        // Reset all tabs to unselected
        binding.tabGlobal.setBackgroundResource(R.drawable.tab_unselected_bg);
        binding.tabKr.setBackgroundResource(R.drawable.tab_unselected_bg);
        binding.tabTw.setBackgroundResource(R.drawable.tab_unselected_bg);
        binding.tabVn.setBackgroundResource(R.drawable.tab_unselected_bg);

        // Set selected tab
        switch (currentRegion) {
            case "Global":
                binding.tabGlobal.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "KR":
                binding.tabKr.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "TW":
                binding.tabTw.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "VN":
                binding.tabVn.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
        }
    }

    private void updateCardVisibility() {
        // Show all cards for now - can be filtered by region later
        binding.cardGlobal.setVisibility(View.VISIBLE);
        binding.cardKr.setVisibility(View.VISIBLE);
        binding.cardTw.setVisibility(View.VISIBLE);
        binding.cardVn.setVisibility(View.VISIBLE);
    }

    private void startDownload(String gameName, DownloadManager.DownloadType downloadType) {
        if (isDownloading) return;

        Log.d(TAG, "Starting download for: " + gameName + " with type: " + downloadType);

        // First check if we have all required permissions
        if (!PermissionManager.hasStoragePermissions(this) || !PermissionManager.hasInstallPackagesPermission(this)) {
            Log.d(TAG, "Requesting permissions before download");

            android.widget.Toast.makeText(this, "Requesting required permissions...",
                android.widget.Toast.LENGTH_SHORT).show();

            PermissionManager.requestAllPermissions(this, new PermissionManager.PermissionCallback() {
                @Override
                public void onPermissionResult(boolean granted, String message) {
                    if (granted) {
                        Log.d(TAG, "Permissions granted, starting download");
                        android.widget.Toast.makeText(ModernDownloadActivity.this, "Permissions granted! Starting download...",
                            android.widget.Toast.LENGTH_SHORT).show();
                        // Retry download now that we have permissions
                        startDownloadWithPermissions(gameName, downloadType);
                    } else {
                        Log.e(TAG, "Permissions denied: " + message);
                        android.widget.Toast.makeText(ModernDownloadActivity.this,
                            "Permissions required for download: " + message,
                            android.widget.Toast.LENGTH_LONG).show();
                    }
                }
            });
            return;
        }

        // We have permissions, proceed with download
        startDownloadWithPermissions(gameName, downloadType);
    }

    private void startDownloadWithPermissions(String gameName, DownloadManager.DownloadType downloadType) {
        isDownloading = true;

        // Disable all download buttons
        binding.btnDownloadGlobal.setEnabled(false);
        binding.btnDownloadKr.setEnabled(false);
        binding.btnDownloadTw.setEnabled(false);
        binding.btnDownloadVn.setEnabled(false);

        // Show starting toast
        android.widget.Toast.makeText(this, "Initializing download system...",
            android.widget.Toast.LENGTH_SHORT).show();

        // Initialize DirectKeyAuthManager first in background
        Log.d(TAG, "Initializing DirectKeyAuthManager...");

        // Show initialization toast
        android.widget.Toast.makeText(this, "Initializing authentication system...",
            android.widget.Toast.LENGTH_SHORT).show();

        // Initialize in background thread to avoid blocking UI
        new Thread(() -> {
            com.bearmod.loader.auth.DirectKeyAuthManager directKeyAuth =
                com.bearmod.loader.auth.DirectKeyAuthManager.getInstance();

            boolean initSuccess = directKeyAuth.initialize(this);

            // Continue on main thread
            runOnUiThread(() -> {
                if (!initSuccess) {
                    Log.e(TAG, "DirectKeyAuthManager initialization failed");
                    android.widget.Toast.makeText(this, "Failed to initialize authentication system",
                        android.widget.Toast.LENGTH_LONG).show();
                    downloadComplete(false, gameName, "Authentication initialization failed");
                    return;
                }

                Log.d(TAG, "DirectKeyAuthManager initialized successfully");

                // Show starting download toast
                android.widget.Toast.makeText(this, "Starting download of " + gameName + " (Test ID: 568410)...",
                    android.widget.Toast.LENGTH_SHORT).show();

                // Start enhanced download with progress dialog
                Log.d(TAG, "Starting DownloadManager...");
                DownloadManager.getInstance().initialize(this);
                DownloadManager.getInstance().downloadGameFilesEnhanced(this, gameName, downloadType,
                    new DownloadManager.EnhancedDownloadListener() {
                        @Override
                        public void onSuccess(String filePath) {
                            Log.d(TAG, "Download completed successfully: " + filePath);
                            runOnUiThread(() -> downloadComplete(true, gameName, filePath));
                        }

                        @Override
                        public void onError(String error) {
                            Log.e(TAG, "Download failed: " + error);
                            runOnUiThread(() -> downloadComplete(false, gameName, error));
                        }
                    });
            });
        }).start();
    }

    private void updateGame(String gameName) {
        // For updates, we download APK only (faster update)
        if (isDownloading) return;

        isDownloading = true;

        // Disable all buttons during update
        binding.btnDownloadGlobal.setEnabled(false);
        binding.btnDownloadKr.setEnabled(false);
        binding.btnDownloadTw.setEnabled(false);
        binding.btnDownloadVn.setEnabled(false);
        binding.btnUpdateGlobal.setEnabled(false);
        binding.btnUpdateKr.setEnabled(false);
        binding.btnUpdateTw.setEnabled(false);
        binding.btnUpdateVn.setEnabled(false);

        android.widget.Toast.makeText(this, "Starting update for " + gameName + "...",
            android.widget.Toast.LENGTH_SHORT).show();

        // Start APK-only download for updates
        DownloadManager.getInstance().initialize(this);
        DownloadManager.getInstance().downloadGameFilesEnhanced(this, gameName,
            DownloadManager.DownloadType.APK_ONLY,
            new DownloadManager.EnhancedDownloadListener() {
                @Override
                public void onSuccess(String filePath) {
                    runOnUiThread(() -> updateComplete(true, gameName, filePath));
                }

                @Override
                public void onError(String error) {
                    runOnUiThread(() -> updateComplete(false, gameName, error));
                }
            });
    }

    private void updateComplete(boolean success, String gameName, String details) {
        isDownloading = false;

        // Re-enable all buttons
        binding.btnDownloadGlobal.setEnabled(true);
        binding.btnDownloadKr.setEnabled(true);
        binding.btnDownloadTw.setEnabled(true);
        binding.btnDownloadVn.setEnabled(true);
        binding.btnUpdateGlobal.setEnabled(true);
        binding.btnUpdateKr.setEnabled(true);
        binding.btnUpdateTw.setEnabled(true);
        binding.btnUpdateVn.setEnabled(true);

        // Show result
        if (success) {
            android.widget.Toast.makeText(this, gameName + " update completed successfully!",
                android.widget.Toast.LENGTH_LONG).show();
            Log.d(TAG, "Update completed: " + details);
        } else {
            android.widget.Toast.makeText(this, "Update failed: " + details,
                android.widget.Toast.LENGTH_LONG).show();
            Log.e(TAG, "Update failed: " + details);
        }
    }

    private void downloadComplete(boolean success, String gameName, String details) {
        isDownloading = false;

        // Re-enable download buttons
        binding.btnDownloadGlobal.setEnabled(true);
        binding.btnDownloadKr.setEnabled(true);
        binding.btnDownloadTw.setEnabled(true);
        binding.btnDownloadVn.setEnabled(true);

        // Show result
        if (success) {
            android.widget.Toast.makeText(this, gameName + " download completed successfully!",
                android.widget.Toast.LENGTH_LONG).show();
            Log.d(TAG, "Download completed: " + details);
        } else {
            android.widget.Toast.makeText(this, "Download failed: " + details,
                android.widget.Toast.LENGTH_LONG).show();
            Log.e(TAG, "Download failed: " + details);
        }
    }

    private void logout() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Logout")
            .setMessage("Are you sure you want to logout? This will clear your authentication data.")
            .setPositiveButton("Logout", (dialog, which) -> {
                // Clear authentication data
                com.bearmod.loader.BearLoaderApplication.getInstance().clearUserData();

                // Clear KeyAuth session
                com.bearmod.loader.auth.DirectKeyAuthManager.getInstance().logout();

                android.widget.Toast.makeText(this, "Logged out successfully",
                    android.widget.Toast.LENGTH_SHORT).show();

                // Return to login screen
                android.content.Intent intent = new android.content.Intent(this,
                    com.bearmod.loader.ui.auth.LoginActivity.class);
                intent.setFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK |
                               android.content.Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
                finish();
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void clearData() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Clear Data")
            .setMessage("This will clear all downloaded files and cached data. Are you sure?")
            .setPositiveButton("Clear", (dialog, which) -> {
                // Clear download cache
                try {
                    java.io.File downloadDir = new java.io.File(
                        android.os.Environment.getExternalStoragePublicDirectory(
                            android.os.Environment.DIRECTORY_DOWNLOADS), "BearMod");
                    if (downloadDir.exists()) {
                        deleteRecursive(downloadDir);
                    }

                    // Clear app cache
                    java.io.File cacheDir = getCacheDir();
                    if (cacheDir.exists()) {
                        deleteRecursive(cacheDir);
                    }

                    android.widget.Toast.makeText(this, "Data cleared successfully",
                        android.widget.Toast.LENGTH_SHORT).show();
                } catch (Exception e) {
                    android.widget.Toast.makeText(this, "Failed to clear data: " + e.getMessage(),
                        android.widget.Toast.LENGTH_LONG).show();
                }
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void showHelp() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("BearMod Loader Help")
            .setMessage("BearMod Loader v1.0\n\n" +
                       "Features:\n" +
                       "• Download PUBG Mobile APK files\n" +
                       "• Download OBB data files\n" +
                       "• Auto-installation support\n" +
                       "• Progress tracking\n" +
                       "• Multiple region support\n\n" +
                       "Support:\n" +
                       "• Discord: discord.gg/bearmod\n" +
                       "• Website: bearmod.com\n" +
                       "• Email: <EMAIL>")
            .setPositiveButton("OK", null)
            .setNeutralButton("Visit Website", (dialog, which) -> {
                try {
                    android.content.Intent intent = new android.content.Intent(
                        android.content.Intent.ACTION_VIEW,
                        android.net.Uri.parse("https://bearmod.com"));
                    startActivity(intent);
                } catch (Exception e) {
                    android.widget.Toast.makeText(this, "Could not open website",
                        android.widget.Toast.LENGTH_SHORT).show();
                }
            })
            .show();
    }

    /**
     * Open settings activity
     */
    private void openSettings() {
        try {
            android.content.Intent intent = new android.content.Intent(this,
                com.bearmod.loader.ui.settings.SettingsActivity.class);
            startActivity(intent);
        } catch (Exception e) {
            android.widget.Toast.makeText(this, "Settings not available",
                android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Show downloads information
     */
    private void showDownloadsInfo() {
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Download Manager")
            .setMessage("Download Features:\n\n" +
                       "• APK Downloads: Game application files\n" +
                       "• OBB Downloads: Game data files\n" +
                       "• Combined Downloads: APK + OBB packages\n" +
                       "• Progress Tracking: Real-time download progress\n" +
                       "• Auto-Installation: Automatic APK installation\n" +
                       "• Resume Support: Resume interrupted downloads\n\n" +
                       "Download Location:\n" +
                       "• APK: Downloads/BearMod/\n" +
                       "• OBB: Android/obb/[package]/")
            .setPositiveButton("OK", null)
            .setNeutralButton("Open Downloads Folder", (dialog, which) -> {
                try {
                    android.content.Intent intent = new android.content.Intent(
                        android.content.Intent.ACTION_VIEW);
                    intent.setDataAndType(
                        android.net.Uri.parse("content://com.android.externalstorage.documents/document/primary%3ADownload%2FBearMod"),
                        "resource/folder");
                    startActivity(intent);
                } catch (Exception e) {
                    android.widget.Toast.makeText(this, "Could not open downloads folder",
                        android.widget.Toast.LENGTH_SHORT).show();
                }
            })
            .show();
    }

    /**
     * Recursively delete a directory and its contents
     */
    private void deleteRecursive(java.io.File fileOrDirectory) {
        if (fileOrDirectory.isDirectory()) {
            java.io.File[] children = fileOrDirectory.listFiles();
            if (children != null) {
                for (java.io.File child : children) {
                    deleteRecursive(child);
                }
            }
        }
        fileOrDirectory.delete();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, android.content.Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // Handle permission results
        PermissionManager.handlePermissionResult(this, requestCode, null, null,
            new PermissionManager.PermissionCallback() {
                @Override
                public void onPermissionResult(boolean granted, String message) {
                    Log.d(TAG, "Permission result: " + granted + " - " + message);
                    if (granted) {
                        android.widget.Toast.makeText(ModernDownloadActivity.this,
                            "Permissions granted successfully!",
                            android.widget.Toast.LENGTH_SHORT).show();
                    } else {
                        android.widget.Toast.makeText(ModernDownloadActivity.this,
                            "Permission denied: " + message,
                            android.widget.Toast.LENGTH_LONG).show();
                    }
                }
            });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // Handle permission results
        PermissionManager.handlePermissionResult(this, requestCode, permissions, grantResults,
            new PermissionManager.PermissionCallback() {
                @Override
                public void onPermissionResult(boolean granted, String message) {
                    Log.d(TAG, "Permission result: " + granted + " - " + message);
                    if (granted) {
                        android.widget.Toast.makeText(ModernDownloadActivity.this,
                            "Permissions granted successfully!",
                            android.widget.Toast.LENGTH_SHORT).show();
                    } else {
                        android.widget.Toast.makeText(ModernDownloadActivity.this,
                            "Permission denied: " + message,
                            android.widget.Toast.LENGTH_LONG).show();
                    }
                }
            });
    }

    @Override
    public void onBackPressed() {
        // If currently downloading, show confirmation dialog
        if (isDownloading) {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Download in Progress")
                .setMessage("A download is currently in progress. Are you sure you want to go back?")
                .setPositiveButton("Yes", (dialog, which) -> {
                    super.onBackPressed();
                    finish();
                })
                .setNegativeButton("No", null)
                .show();
        } else {
            super.onBackPressed();
            finish();
        }
    }
}
