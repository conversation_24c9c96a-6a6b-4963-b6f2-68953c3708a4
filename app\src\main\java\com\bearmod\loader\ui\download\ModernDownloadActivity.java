package com.bearmod.loader.ui.download;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.bearmod.loader.R;
import com.bearmod.loader.download.DownloadManager;

public class ModernDownloadActivity extends AppCompatActivity {

    private LinearLayout tabGlobal, tabKr, tabTw, tabVn;
    private LinearLayout cardGlobal, cardKr, cardTw, cardVn;
    private android.widget.Button btnDownloadGlobal, btnUpdateGlobal, btnDownloadKr, btnUpdateKr;
    private android.widget.Button btnDownloadTw, btnUpdateTw, btnDownloadVn, btnUpdateVn;
    private TextView actionLogout, actionClearData, actionHelp;

    private boolean isDownloading = false;
    private String currentRegion = "Global";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fresh_download);

        initViews();
        setupClickListeners();
    }

    private void initViews() {
        // Region tabs
        tabGlobal = findViewById(R.id.tab_global);
        tabKr = findViewById(R.id.tab_kr);
        tabTw = findViewById(R.id.tab_tw);
        tabVn = findViewById(R.id.tab_vn);

        // Game cards
        cardGlobal = findViewById(R.id.card_global);
        cardKr = findViewById(R.id.card_kr);
        cardTw = findViewById(R.id.card_tw);
        cardVn = findViewById(R.id.card_vn);

        // Download buttons
        btnDownloadGlobal = findViewById(R.id.btn_download_global);
        btnUpdateGlobal = findViewById(R.id.btn_update_global);
        btnDownloadKr = findViewById(R.id.btn_download_kr);
        btnUpdateKr = findViewById(R.id.btn_update_kr);
        btnDownloadTw = findViewById(R.id.btn_download_tw);
        btnUpdateTw = findViewById(R.id.btn_update_tw);
        btnDownloadVn = findViewById(R.id.btn_download_vn);
        btnUpdateVn = findViewById(R.id.btn_update_vn);

        // Bottom actions
        actionLogout = findViewById(R.id.action_logout);
        actionClearData = findViewById(R.id.action_clear_data);
        actionHelp = findViewById(R.id.action_help);
    }

    private void setupClickListeners() {
        // Region tab clicks
        tabGlobal.setOnClickListener(v -> selectRegion("Global"));
        tabKr.setOnClickListener(v -> selectRegion("KR"));
        tabTw.setOnClickListener(v -> selectRegion("TW"));
        tabVn.setOnClickListener(v -> selectRegion("VN"));

        // Download button clicks
        btnDownloadGlobal.setOnClickListener(v -> startDownload("PUBG MOBILE Global", DownloadManager.DownloadType.APK_AND_OBB));
        btnUpdateGlobal.setOnClickListener(v -> updateGame("PUBG MOBILE Global"));
        btnDownloadKr.setOnClickListener(v -> startDownload("PUBG MOBILE KR", DownloadManager.DownloadType.APK_AND_OBB));
        btnUpdateKr.setOnClickListener(v -> updateGame("PUBG MOBILE KR"));
        btnDownloadTw.setOnClickListener(v -> startDownload("PUBG MOBILE TW", DownloadManager.DownloadType.APK_AND_OBB));
        btnUpdateTw.setOnClickListener(v -> updateGame("PUBG MOBILE TW"));
        btnDownloadVn.setOnClickListener(v -> startDownload("PUBG MOBILE VN", DownloadManager.DownloadType.APK_AND_OBB));
        btnUpdateVn.setOnClickListener(v -> updateGame("PUBG MOBILE VN"));

        // Bottom action clicks
        actionLogout.setOnClickListener(v -> logout());
        actionClearData.setOnClickListener(v -> clearData());
        actionHelp.setOnClickListener(v -> showHelp());
    }
    
    private void selectRegion(String region) {
        currentRegion = region;

        // Update tab appearances
        updateTabAppearance();

        // Show appropriate cards
        updateCardVisibility();

        android.widget.Toast.makeText(this, "Selected " + region + " region",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void updateTabAppearance() {
        // Reset all tabs to unselected
        tabGlobal.setBackgroundResource(R.drawable.tab_unselected_bg);
        tabKr.setBackgroundResource(R.drawable.tab_unselected_bg);
        tabTw.setBackgroundResource(R.drawable.tab_unselected_bg);
        tabVn.setBackgroundResource(R.drawable.tab_unselected_bg);

        // Set selected tab
        switch (currentRegion) {
            case "Global":
                tabGlobal.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "KR":
                tabKr.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "TW":
                tabTw.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "VN":
                tabVn.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
        }
    }

    private void updateCardVisibility() {
        // Show all cards for now - can be filtered by region later
        cardGlobal.setVisibility(View.VISIBLE);
        cardKr.setVisibility(View.VISIBLE);
        cardTw.setVisibility(View.VISIBLE);
        cardVn.setVisibility(View.VISIBLE);
    }

    private void startDownload(String gameName, DownloadManager.DownloadType downloadType) {
        if (isDownloading) return;

        isDownloading = true;

        // Disable all download buttons
        btnDownloadGlobal.setEnabled(false);
        btnDownloadKr.setEnabled(false);
        btnDownloadTw.setEnabled(false);
        btnDownloadVn.setEnabled(false);

        // Show progress toast
        android.widget.Toast.makeText(this, "Starting download of " + gameName + "...",
            android.widget.Toast.LENGTH_SHORT).show();

        // Start download
        DownloadManager.getInstance().initialize(this);
        DownloadManager.getInstance().downloadGameFiles(gameName, downloadType,
            new DownloadManager.DownloadListener() {
                @Override
                public void onSuccess(java.io.File downloadedFile) {
                    runOnUiThread(() -> downloadComplete(true, gameName));
                }

                @Override
                public void onError(String error) {
                    runOnUiThread(() -> downloadComplete(false, gameName));
                }
            });
    }

    private void updateGame(String gameName) {
        android.widget.Toast.makeText(this, "Updating " + gameName + "...",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void downloadComplete(boolean success, String gameName) {
        isDownloading = false;

        // Re-enable download buttons
        btnDownloadGlobal.setEnabled(true);
        btnDownloadKr.setEnabled(true);
        btnDownloadTw.setEnabled(true);
        btnDownloadVn.setEnabled(true);

        // Show result
        if (success) {
            android.widget.Toast.makeText(this, gameName + " download completed!",
                android.widget.Toast.LENGTH_SHORT).show();
        } else {
            android.widget.Toast.makeText(this, "Download failed",
                android.widget.Toast.LENGTH_SHORT).show();
        }
    }

    private void logout() {
        android.widget.Toast.makeText(this, "Logout clicked",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void clearData() {
        android.widget.Toast.makeText(this, "Clear Data clicked",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void showHelp() {
        android.widget.Toast.makeText(this, "Help clicked",
            android.widget.Toast.LENGTH_SHORT).show();
    }
}
