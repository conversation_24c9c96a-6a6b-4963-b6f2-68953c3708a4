package com.bearmod.loader.ui.download;

import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.bearmod.loader.R;
import com.bearmod.loader.download.DownloadManager;

public class ModernDownloadActivity extends AppCompatActivity {
    
    private LinearLayout downloadApk, downloadFull, downloadObb;
    private LinearLayout progressSection;
    private TextView downloadStatus, downloadCancel, progressText, progressSize;
    private ProgressBar progressBar;
    
    private boolean isDownloading = false;
    private String currentDownloadType = "";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_fresh_download);
        
        initViews();
        setupClickListeners();
    }
    
    private void initViews() {
        downloadApk = findViewById(R.id.download_apk);
        downloadFull = findViewById(R.id.download_full);
        downloadObb = findViewById(R.id.download_obb);
        
        progressSection = findViewById(R.id.progress_section);
        downloadStatus = findViewById(R.id.download_status);
        downloadCancel = findViewById(R.id.download_cancel);
        progressText = findViewById(R.id.progress_text);
        progressSize = findViewById(R.id.progress_size);
        progressBar = findViewById(R.id.progress_bar);
    }
    
    private void setupClickListeners() {
        downloadApk.setOnClickListener(v -> startDownload("APK Only", DownloadManager.DownloadType.APK_ONLY));
        downloadFull.setOnClickListener(v -> startDownload("APK + OBB", DownloadManager.DownloadType.APK_AND_OBB));
        downloadObb.setOnClickListener(v -> startDownload("OBB Only", DownloadManager.DownloadType.OBB_ONLY));
        
        downloadCancel.setOnClickListener(v -> cancelDownload());
    }
    
    private void startDownload(String type, DownloadManager.DownloadType downloadType) {
        if (isDownloading) return;
        
        isDownloading = true;
        currentDownloadType = type;
        
        // Show progress section
        progressSection.setVisibility(View.VISIBLE);
        downloadStatus.setText("Downloading " + type + "...");
        
        // Disable download buttons
        downloadApk.setEnabled(false);
        downloadFull.setEnabled(false);
        downloadObb.setEnabled(false);
        
        // Start download
        DownloadManager.getInstance().initialize(this);
        DownloadManager.getInstance().setProgressListener(new DownloadManager.DownloadProgressListener() {
            @Override
            public void onProgressUpdate(int progress, double downloadedMB, double totalSizeMB,
                                       double speedMBps, int etaMinutes, int etaSeconds) {
                runOnUiThread(() -> updateProgress(progress, downloadedMB, totalSizeMB, speedMBps));
            }
        });
        
        DownloadManager.getInstance().downloadGameFiles("PUBG MOBILE", downloadType,
            new DownloadManager.DownloadListener() {
                @Override
                public void onSuccess(java.io.File downloadedFile) {
                    runOnUiThread(() -> downloadComplete(true));
                }

                @Override
                public void onError(String error) {
                    runOnUiThread(() -> downloadComplete(false));
                }
            });
    }
    
    private void updateProgress(int progress, double downloadedMB, double totalSizeMB, double speedMBps) {
        progressBar.setProgress(progress);
        progressText.setText(progress + "% • " + String.format("%.1f", speedMBps) + " MB/s");
        progressSize.setText(String.format("%.1f", downloadedMB) + " / " + String.format("%.1f", totalSizeMB) + " MB");
    }
    
    private void downloadComplete(boolean success) {
        isDownloading = false;
        
        // Hide progress section
        progressSection.setVisibility(View.GONE);
        
        // Re-enable download buttons
        downloadApk.setEnabled(true);
        downloadFull.setEnabled(true);
        downloadObb.setEnabled(true);
        
        // Show result
        if (success) {
            android.widget.Toast.makeText(this, currentDownloadType + " download completed!", 
                android.widget.Toast.LENGTH_SHORT).show();
        } else {
            android.widget.Toast.makeText(this, "Download failed", 
                android.widget.Toast.LENGTH_SHORT).show();
        }
    }
    
    private void cancelDownload() {
        // TODO: Implement cancel functionality
        downloadComplete(false);
    }
}
