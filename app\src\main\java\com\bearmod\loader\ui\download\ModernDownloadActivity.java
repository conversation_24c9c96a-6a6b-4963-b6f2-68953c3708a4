package com.bearmod.loader.ui.download;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import com.bearmod.loader.R;
import com.bearmod.loader.databinding.ActivityFreshDownloadBinding;
import com.bearmod.loader.download.DownloadManager;

public class ModernDownloadActivity extends AppCompatActivity {

    private static final String TAG = "ModernDownloadActivity";

    private ActivityFreshDownloadBinding binding;
    private boolean isDownloading = false;
    private String currentRegion = "Global";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "Starting ModernDownloadActivity onCreate");

            // Use data binding to avoid casting issues
            binding = ActivityFreshDownloadBinding.inflate(getLayoutInflater());
            setContentView(binding.getRoot());

            setupClickListeners();

            Log.d(TAG, "ModernDownloadActivity onCreate completed successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate: " + e.getMessage(), e);
            Toast.makeText(this, "Error loading interface: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }



    private void setupClickListeners() {
        try {
            Log.d(TAG, "Setting up click listeners...");

            // Back button click
            binding.btnBack.setOnClickListener(v -> onBackPressed());

            // Region tab clicks
            binding.tabGlobal.setOnClickListener(v -> selectRegion("Global"));
            binding.tabKr.setOnClickListener(v -> selectRegion("KR"));
            binding.tabTw.setOnClickListener(v -> selectRegion("TW"));
            binding.tabVn.setOnClickListener(v -> selectRegion("VN"));

            // Download button clicks
            binding.btnDownloadGlobal.setOnClickListener(v -> startDownload("PUBG MOBILE Global", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateGlobal.setOnClickListener(v -> updateGame("PUBG MOBILE Global"));
            binding.btnDownloadKr.setOnClickListener(v -> startDownload("PUBG MOBILE KR", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateKr.setOnClickListener(v -> updateGame("PUBG MOBILE KR"));
            binding.btnDownloadTw.setOnClickListener(v -> startDownload("PUBG MOBILE TW", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateTw.setOnClickListener(v -> updateGame("PUBG MOBILE TW"));
            binding.btnDownloadVn.setOnClickListener(v -> startDownload("PUBG MOBILE VN", DownloadManager.DownloadType.APK_AND_OBB));
            binding.btnUpdateVn.setOnClickListener(v -> updateGame("PUBG MOBILE VN"));

            // Bottom action clicks
            binding.actionLogout.setOnClickListener(v -> logout());
            binding.actionClearData.setOnClickListener(v -> clearData());
            binding.actionHelp.setOnClickListener(v -> showHelp());

            Log.d(TAG, "Click listeners set up successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up click listeners: " + e.getMessage(), e);
            throw e;
        }
    }
    
    private void selectRegion(String region) {
        currentRegion = region;

        // Update tab appearances
        updateTabAppearance();

        // Show appropriate cards
        updateCardVisibility();

        android.widget.Toast.makeText(this, "Selected " + region + " region",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void updateTabAppearance() {
        // Reset all tabs to unselected
        binding.tabGlobal.setBackgroundResource(R.drawable.tab_unselected_bg);
        binding.tabKr.setBackgroundResource(R.drawable.tab_unselected_bg);
        binding.tabTw.setBackgroundResource(R.drawable.tab_unselected_bg);
        binding.tabVn.setBackgroundResource(R.drawable.tab_unselected_bg);

        // Set selected tab
        switch (currentRegion) {
            case "Global":
                binding.tabGlobal.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "KR":
                binding.tabKr.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "TW":
                binding.tabTw.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
            case "VN":
                binding.tabVn.setBackgroundResource(R.drawable.tab_selected_bg);
                break;
        }
    }

    private void updateCardVisibility() {
        // Show all cards for now - can be filtered by region later
        binding.cardGlobal.setVisibility(View.VISIBLE);
        binding.cardKr.setVisibility(View.VISIBLE);
        binding.cardTw.setVisibility(View.VISIBLE);
        binding.cardVn.setVisibility(View.VISIBLE);
    }

    private void startDownload(String gameName, DownloadManager.DownloadType downloadType) {
        if (isDownloading) return;

        isDownloading = true;

        // Disable all download buttons
        binding.btnDownloadGlobal.setEnabled(false);
        binding.btnDownloadKr.setEnabled(false);
        binding.btnDownloadTw.setEnabled(false);
        binding.btnDownloadVn.setEnabled(false);

        // Show starting toast
        android.widget.Toast.makeText(this, "Starting download of " + gameName + "...",
            android.widget.Toast.LENGTH_SHORT).show();

        // Start enhanced download with progress dialog
        DownloadManager.getInstance().initialize(this);
        DownloadManager.getInstance().downloadGameFilesEnhanced(this, gameName, downloadType,
            new DownloadManager.EnhancedDownloadListener() {
                @Override
                public void onSuccess(String filePath) {
                    runOnUiThread(() -> downloadComplete(true, gameName, filePath));
                }

                @Override
                public void onError(String error) {
                    runOnUiThread(() -> downloadComplete(false, gameName, error));
                }
            });
    }

    private void updateGame(String gameName) {
        // For updates, we download APK only (faster update)
        if (isDownloading) return;

        isDownloading = true;

        // Disable all buttons during update
        binding.btnDownloadGlobal.setEnabled(false);
        binding.btnDownloadKr.setEnabled(false);
        binding.btnDownloadTw.setEnabled(false);
        binding.btnDownloadVn.setEnabled(false);
        binding.btnUpdateGlobal.setEnabled(false);
        binding.btnUpdateKr.setEnabled(false);
        binding.btnUpdateTw.setEnabled(false);
        binding.btnUpdateVn.setEnabled(false);

        android.widget.Toast.makeText(this, "Starting update for " + gameName + "...",
            android.widget.Toast.LENGTH_SHORT).show();

        // Start APK-only download for updates
        DownloadManager.getInstance().initialize(this);
        DownloadManager.getInstance().downloadGameFilesEnhanced(this, gameName,
            DownloadManager.DownloadType.APK_ONLY,
            new DownloadManager.EnhancedDownloadListener() {
                @Override
                public void onSuccess(String filePath) {
                    runOnUiThread(() -> updateComplete(true, gameName, filePath));
                }

                @Override
                public void onError(String error) {
                    runOnUiThread(() -> updateComplete(false, gameName, error));
                }
            });
    }

    private void updateComplete(boolean success, String gameName, String details) {
        isDownloading = false;

        // Re-enable all buttons
        binding.btnDownloadGlobal.setEnabled(true);
        binding.btnDownloadKr.setEnabled(true);
        binding.btnDownloadTw.setEnabled(true);
        binding.btnDownloadVn.setEnabled(true);
        binding.btnUpdateGlobal.setEnabled(true);
        binding.btnUpdateKr.setEnabled(true);
        binding.btnUpdateTw.setEnabled(true);
        binding.btnUpdateVn.setEnabled(true);

        // Show result
        if (success) {
            android.widget.Toast.makeText(this, gameName + " update completed successfully!",
                android.widget.Toast.LENGTH_LONG).show();
            Log.d(TAG, "Update completed: " + details);
        } else {
            android.widget.Toast.makeText(this, "Update failed: " + details,
                android.widget.Toast.LENGTH_LONG).show();
            Log.e(TAG, "Update failed: " + details);
        }
    }

    private void downloadComplete(boolean success, String gameName, String details) {
        isDownloading = false;

        // Re-enable download buttons
        binding.btnDownloadGlobal.setEnabled(true);
        binding.btnDownloadKr.setEnabled(true);
        binding.btnDownloadTw.setEnabled(true);
        binding.btnDownloadVn.setEnabled(true);

        // Show result
        if (success) {
            android.widget.Toast.makeText(this, gameName + " download completed successfully!",
                android.widget.Toast.LENGTH_LONG).show();
            Log.d(TAG, "Download completed: " + details);
        } else {
            android.widget.Toast.makeText(this, "Download failed: " + details,
                android.widget.Toast.LENGTH_LONG).show();
            Log.e(TAG, "Download failed: " + details);
        }
    }

    private void logout() {
        android.widget.Toast.makeText(this, "Logout clicked",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void clearData() {
        android.widget.Toast.makeText(this, "Clear Data clicked",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    private void showHelp() {
        android.widget.Toast.makeText(this, "Help clicked",
            android.widget.Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onBackPressed() {
        // If currently downloading, show confirmation dialog
        if (isDownloading) {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Download in Progress")
                .setMessage("A download is currently in progress. Are you sure you want to go back?")
                .setPositiveButton("Yes", (dialog, which) -> {
                    super.onBackPressed();
                    finish();
                })
                .setNegativeButton("No", null)
                .show();
        } else {
            super.onBackPressed();
            finish();
        }
    }
}
