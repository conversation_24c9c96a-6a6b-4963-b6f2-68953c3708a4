package com.bearmod.loader.ui.download;

import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;

import com.bearmod.loader.R;

import java.text.DecimalFormat;

/**
 * Enhanced download progress dialog with beautiful animations and modern UI
 * Features:
 * - Smooth progress animations
 * - Real-time speed and ETA calculations
 * - Beautiful gradient progress bar
 * - Pulsing download icon
 * - Color-coded progress states
 */
public class EnhancedProgressDialog extends AlertDialog {
    
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.##");
    private static final DecimalFormat SIZE_FORMAT = new DecimalFormat("#.#");
    
    // Animation constants
    private static final int PROGRESS_ANIMATION_DURATION = 300;
    private static final int PULSE_ANIMATION_DURATION = 1500;
    
    // UI Components
    private ImageView ivGameIcon;
    private ImageView ivDownloadIcon;
    private TextView tvTitle;
    private TextView tvFileName;
    private TextView tvProgress;
    private TextView tvSpeed;
    private TextView tvEta;
    private TextView tvFileSize;
    private TextView tvStatus;
    private ProgressBar progressBar;
    private Button btnCancel;
    private View progressContainer;
    
    // Animation objects
    private ObjectAnimator progressAnimator;
    private ObjectAnimator pulseAnimator;
    private ValueAnimator colorAnimator;
    
    // Data
    private DownloadCancelListener cancelListener;
    private String downloadType;
    private String fileName;
    private int gameIconResId;
    private int currentProgress = 0;
    private Handler animationHandler = new Handler(Looper.getMainLooper());
    
    /**
     * Constructor
     */
    public EnhancedProgressDialog(@NonNull Context context, String downloadType, String fileName, int gameIconResId) {
        super(context, R.style.Theme_BearLoader_Dialog);
        this.downloadType = downloadType;
        this.fileName = fileName;
        this.gameIconResId = gameIconResId;
        setCancelable(false);
        setCanceledOnTouchOutside(false);
    }
    
    /**
     * Constructor with default PUBG icon
     */
    public EnhancedProgressDialog(@NonNull Context context, String downloadType, String fileName) {
        this(context, downloadType, fileName, R.drawable.ic_pubg_mobile_global_logo);
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Inflate custom layout
        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_enhanced_progress, null);
        setView(view);
        
        // Initialize views
        initViews(view);
        
        // Set initial values
        setupInitialState();
        
        // Start animations
        startAnimations();
    }
    
    /**
     * Initialize views
     */
    private void initViews(View view) {
        ivGameIcon = view.findViewById(R.id.iv_game_icon);
        ivDownloadIcon = view.findViewById(R.id.iv_download_icon);
        tvTitle = view.findViewById(R.id.tv_download_title);
        tvFileName = view.findViewById(R.id.tv_file_name);
        tvProgress = view.findViewById(R.id.tv_progress);
        tvSpeed = view.findViewById(R.id.tv_speed);
        tvEta = view.findViewById(R.id.tv_eta);
        tvFileSize = view.findViewById(R.id.tv_file_size);
        tvStatus = view.findViewById(R.id.tv_status);
        progressBar = view.findViewById(R.id.progress_bar);
        btnCancel = view.findViewById(R.id.btn_cancel);
        progressContainer = view.findViewById(R.id.progress_container);

        // Set game icon
        if (ivGameIcon != null && gameIconResId != 0) {
            ivGameIcon.setImageResource(gameIconResId);
        }

        // Set cancel button listener
        if (btnCancel != null) {
            btnCancel.setOnClickListener(v -> {
                if (cancelListener != null) {
                    cancelListener.onCancelRequested();
                }
            });
        }
    }
    
    /**
     * Setup initial state
     */
    private void setupInitialState() {
        tvTitle.setText("Downloading " + downloadType);
        tvFileName.setText(fileName);
        tvProgress.setText("0%");
        tvSpeed.setText("Initializing...");
        tvEta.setText("Calculating...");
        tvFileSize.setText("0 / 0 MB");
        tvStatus.setText("Preparing download...");
        progressBar.setProgress(0);
        
        // Set initial colors
        updateProgressColors(0);
    }
    
    /**
     * Start beautiful animations
     */
    private void startAnimations() {
        // Pulse animation for download icon
        if (ivDownloadIcon != null) {
            pulseAnimator = ObjectAnimator.ofFloat(ivDownloadIcon, "alpha", 0.3f, 1.0f);
            pulseAnimator.setDuration(PULSE_ANIMATION_DURATION);
            pulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
            pulseAnimator.setRepeatMode(ValueAnimator.REVERSE);
            pulseAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
            pulseAnimator.start();
        }
    }
    
    /**
     * Update download progress - API 35 compatible
     */
    public void updateProgress(int progress, double downloadedMB, double totalSizeMB,
                              double speedMBps, int etaMinutes, int etaSeconds) {

        // For API 35 compatibility, ensure we're on the main thread
        if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
            // Already on main thread, update directly
            updateProgressDirect(progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds);
        } else {
            // Post to main thread
            animationHandler.post(() -> updateProgressDirect(progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds));
        }
    }

    /**
     * Direct progress update on main thread only
     */
    private void updateProgressDirect(int progress, double downloadedMB, double totalSizeMB,
                                     double speedMBps, int etaMinutes, int etaSeconds) {
        try {
            // Simple, direct UI updates without animations
            progressBar.setProgress(progress);
            tvProgress.setText(progress + "%");

            // Update speed display
            updateSpeedDisplay(speedMBps);

            // Update ETA
            updateEtaDisplay(etaMinutes, etaSeconds);

            // Update file size
            updateFileSizeDisplay(downloadedMB, totalSizeMB);

            // Update status message
            updateStatusMessage(progress, speedMBps);

            // Update colors based on progress
            updateProgressColorsSimple(progress);

            // Store current progress
            currentProgress = progress;
        } catch (Exception e) {
            android.util.Log.e("EnhancedProgressDialog", "Error updating progress", e);
        }
    }
    
    /**
     * Animate progress bar smoothly
     */
    private void animateProgressBar(int targetProgress) {
        // This method should only be called from the main thread
        if (progressAnimator != null) {
            progressAnimator.cancel();
        }

        progressAnimator = ObjectAnimator.ofInt(progressBar, "progress", progressBar.getProgress(), targetProgress);
        progressAnimator.setDuration(PROGRESS_ANIMATION_DURATION);
        progressAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
        progressAnimator.start();
    }
    
    /**
     * Animate progress text
     */
    private void animateProgressText(int targetProgress) {
        // Ensure we're on the main thread for animations
        animationHandler.post(() -> {
            ValueAnimator textAnimator = ValueAnimator.ofInt(currentProgress, targetProgress);
            textAnimator.setDuration(PROGRESS_ANIMATION_DURATION);
            textAnimator.addUpdateListener(animation -> {
                int animatedValue = (int) animation.getAnimatedValue();
                tvProgress.setText(animatedValue + "%");
            });
            textAnimator.start();
        });
    }
    
    /**
     * Update speed display with formatting
     */
    private void updateSpeedDisplay(double speedMBps) {
        if (speedMBps > 0) {
            if (speedMBps >= 1.0) {
                tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + " MB/s");
            } else {
                tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps * 1024) + " KB/s");
            }
        } else {
            tvSpeed.setText("0 KB/s");
        }
    }
    
    /**
     * Update ETA display
     */
    private void updateEtaDisplay(int etaMinutes, int etaSeconds) {
        if (etaMinutes > 0 || etaSeconds > 0) {
            if (etaMinutes > 60) {
                int hours = etaMinutes / 60;
                int remainingMinutes = etaMinutes % 60;
                tvEta.setText(hours + "h " + remainingMinutes + "m");
            } else if (etaMinutes > 0) {
                tvEta.setText(etaMinutes + "m " + etaSeconds + "s");
            } else {
                tvEta.setText(etaSeconds + "s");
            }
        } else {
            tvEta.setText("--");
        }
    }
    
    /**
     * Update file size display
     */
    private void updateFileSizeDisplay(double downloadedMB, double totalSizeMB) {
        String downloadedStr = formatFileSize(downloadedMB);
        String totalStr = formatFileSize(totalSizeMB);
        tvFileSize.setText(downloadedStr + " / " + totalStr);
    }
    
    /**
     * Format file size with appropriate units
     */
    private String formatFileSize(double sizeMB) {
        if (sizeMB >= 1024) {
            return SIZE_FORMAT.format(sizeMB / 1024) + " GB";
        } else {
            return SIZE_FORMAT.format(sizeMB) + " MB";
        }
    }
    
    /**
     * Update status message based on progress
     */
    private void updateStatusMessage(int progress, double speedMBps) {
        if (progress == 0) {
            tvStatus.setText("Initializing download...");
        } else if (progress < 25) {
            tvStatus.setText("Download starting...");
        } else if (progress < 50) {
            tvStatus.setText("Download in progress...");
        } else if (progress < 75) {
            tvStatus.setText("More than halfway there...");
        } else if (progress < 95) {
            tvStatus.setText("Almost finished...");
        } else if (progress < 100) {
            tvStatus.setText("Finalizing...");
        } else {
            tvStatus.setText("Download complete!");
        }
    }
    
    /**
     * Update progress colors based on completion percentage (simplified)
     */
    private void updateProgressColorsSimple(int progress) {
        try {
            int color;
            if (progress < 25) {
                color = ContextCompat.getColor(getContext(), R.color.progress_red);
            } else if (progress < 50) {
                color = ContextCompat.getColor(getContext(), R.color.progress_orange);
            } else if (progress < 75) {
                color = ContextCompat.getColor(getContext(), R.color.progress_yellow);
            } else if (progress < 100) {
                color = ContextCompat.getColor(getContext(), R.color.progress_blue);
            } else {
                color = ContextCompat.getColor(getContext(), R.color.progress_green);
            }

            // Apply color to progress bar directly
            progressBar.getProgressDrawable().setTint(color);
        } catch (Exception e) {
            android.util.Log.e("EnhancedProgressDialog", "Error updating colors", e);
        }
    }

    /**
     * Update progress colors based on completion percentage (with animations)
     */
    private void updateProgressColors(int progress) {
        // Ensure color updates happen on the main thread
        animationHandler.post(() -> {
            int color;
            if (progress < 25) {
                color = ContextCompat.getColor(getContext(), R.color.progress_red);
            } else if (progress < 50) {
                color = ContextCompat.getColor(getContext(), R.color.progress_orange);
            } else if (progress < 75) {
                color = ContextCompat.getColor(getContext(), R.color.progress_yellow);
            } else if (progress < 100) {
                color = ContextCompat.getColor(getContext(), R.color.progress_blue);
            } else {
                color = ContextCompat.getColor(getContext(), R.color.progress_green);
            }

            // Animate color change
            if (colorAnimator != null) {
                colorAnimator.cancel();
            }

            // Apply color to progress bar
            progressBar.getProgressDrawable().setTint(color);
        });
    }
    
    /**
     * Show download completion with celebration animation
     */
    public void showCompletion(boolean success, String message) {
        // Ensure UI updates happen on the main thread
        animationHandler.post(() -> {
            try {
                // Stop pulse animation safely
                if (pulseAnimator != null) {
                    pulseAnimator.cancel();
                }

                if (success) {
                    tvTitle.setText("✅ Download Complete!");
                    tvProgress.setText("100%");
                    tvSpeed.setText("Complete");
                    tvEta.setText("Done");
                    tvStatus.setText("Ready to install!");
                    progressBar.setProgress(100);
                    btnCancel.setText("Close");
                } else {
                    tvTitle.setText("❌ Download Failed");
                    tvProgress.setText("Failed");
                    tvSpeed.setText("Error");
                    tvEta.setText("--");
                    tvStatus.setText(message);
                    btnCancel.setText("Close");
                }

                // Update cancel button to close dialog
                btnCancel.setOnClickListener(v -> dismiss());
            } catch (Exception e) {
                android.util.Log.e("EnhancedProgressDialog", "Error showing completion", e);
            }
        });
    }
    
    /**
     * Start celebration animation for successful download
     */
    private void startCelebrationAnimation() {
        if (progressContainer != null) {
            ObjectAnimator scaleX = ObjectAnimator.ofFloat(progressContainer, "scaleX", 1.0f, 1.05f, 1.0f);
            ObjectAnimator scaleY = ObjectAnimator.ofFloat(progressContainer, "scaleY", 1.0f, 1.05f, 1.0f);
            scaleX.setDuration(600);
            scaleY.setDuration(600);
            scaleX.start();
            scaleY.start();
        }
    }
    
    /**
     * Start error animation for failed download
     */
    private void startErrorAnimation() {
        if (progressContainer != null) {
            ObjectAnimator shake = ObjectAnimator.ofFloat(progressContainer, "translationX", 0, 25, -25, 25, -25, 15, -15, 6, -6, 0);
            shake.setDuration(600);
            shake.start();
        }
    }
    
    /**
     * Set download cancel listener
     */
    public void setDownloadCancelListener(DownloadCancelListener listener) {
        this.cancelListener = listener;
    }
    
    @Override
    public void dismiss() {
        // Clean up animations
        if (progressAnimator != null) {
            progressAnimator.cancel();
        }
        if (pulseAnimator != null) {
            pulseAnimator.cancel();
        }
        if (colorAnimator != null) {
            colorAnimator.cancel();
        }
        
        super.dismiss();
    }
    
    /**
     * Download cancel listener interface
     */
    public interface DownloadCancelListener {
        void onCancelRequested();
    }
}
