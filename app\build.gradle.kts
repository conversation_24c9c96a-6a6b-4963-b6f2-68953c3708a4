plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace = "com.bearmod.loader"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.bearmod.loader"
        minSdk = 30
        targetSdk = 35
        versionCode = 1
        versionName = "3.8.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters += "arm64-v8a"
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    buildFeatures {
        viewBinding = true
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    externalNativeBuild {
        ndkBuild {
            path = "src/main/jni/Android.mk"
        }
    }

    tasks.withType<JavaCompile>().configureEach {
        options.compilerArgs.addAll(listOf("-Xlint:unchecked", "-Xlint:deprecation"))
    }

    packaging {
        resources {
            excludes += listOf(
                "META-INF/DEPENDENCIES",
                "META-INF/LICENSE",
                "META-INF/LICENSE.txt",
                "META-INF/license.txt",
                "META-INF/NOTICE",
                "META-INF/NOTICE.txt",
                "META-INF/notice.txt",
                "META-INF/ASL2.0",
                "META-INF/*.kotlin_module"
            )
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
        disable += listOf("InvalidPackage", "MissingTranslation")
    }
    ndkVersion = "27.1.12297006"
}

dependencies {
    // Core Android dependencies
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.constraintlayout)
    implementation(libs.constraintlayout.v214)// Use the latest stable version
    //

    // Navigation components
    implementation(libs.navigation.fragment)
    implementation(libs.navigation.ui)

    // Lifecycle components
    implementation(libs.lifecycle.viewmodel)
    implementation(libs.lifecycle.livedata)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit.gson)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)
    implementation(libs.gson)

    // SwipeRefreshLayout
    implementation(libs.swiperefreshlayout)

    // Database (using newer versions below)
    implementation(libs.fragment)
    implementation(libs.recyclerview)

    // Image loading
    implementation(libs.glide)

    // UI effects
    implementation(libs.shimmer)
    implementation(libs.lottie)

    // ExoPlayer for video playback
    implementation(libs.exoplayer)


    // Direct KeyAuth API implementation
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)

    // AndroidX Security Crypto for EncryptedSharedPreferences
    implementation("androidx.security:security-crypto:1.1.0-alpha06")

    // Room database
    implementation(libs.room.runtime.v271)
    implementation(libs.room.ktx.v271)
    annotationProcessor(libs.room.compiler.v271)

    // Retrofit for API calls
    implementation(libs.retrofit.v2110)
    implementation(libs.converter.gson.v2110)

    // WorkManager
    implementation(libs.work.runtime)
    implementation(libs.room.ktx)
    implementation(libs.work.runtime.ktx)

    // For ListenableFuture support
    implementation(libs.concurrent.futures)
    implementation(libs.guava)

    // WebSocket
    implementation(libs.websocket)

    // Testing
    testImplementation(libs.junit)
    testImplementation(libs.mockito.core)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
    androidTestImplementation(libs.mockito.android)
}