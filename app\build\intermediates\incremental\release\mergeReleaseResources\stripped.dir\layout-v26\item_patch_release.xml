<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/card_bg">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Game Image -->
        <ImageView
            android:id="@+id/ivGameImage"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:scaleType="centerCrop"
            android:background="@drawable/rounded_image_bg"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Game Name -->
        <TextView
            android:id="@+id/tvGameName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textStyle="bold"
            android:textSize="18sp"
            app:layout_constraintStart_toEndOf="@id/ivGameImage"
            app:layout_constraintTop_toTopOf="@id/ivGameImage"
            app:layout_constraintEnd_toStartOf="@id/btnDownload"
            app:layout_constraintHorizontal_bias="0"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- Version, Type, Size -->
        <TextView
            android:id="@+id/tvGameDetails"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="@id/tvGameName"
            app:layout_constraintTop_toBottomOf="@id/tvGameName"
            app:layout_constraintEnd_toEndOf="@id/tvGameName"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Download Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnDownload"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:backgroundTint="@color/accent"
            android:icon="@drawable/ic_download"
            android:iconTint="@color/white"
            android:text=""
            app:cornerRadius="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView> 