package com.bearmod.loader;

/**
 * Placeholder BuildConfig class
 * This is normally generated by the build system, but we're creating a placeholder for development
 */
public final class BuildConfig {
    
    /**
     * Application ID
     */
    public static final String APPLICATION_ID = "com.bearmod.loader";
    
    /**
     * Build type
     */
    public static final String BUILD_TYPE = "debug";
    
    /**
     * Debug flag
     */
    public static final boolean DEBUG = true;
    
    /**
     * Version code
     */
    public static final int VERSION_CODE = 1;
    
    /**
     * Version name
     */
    public static final String VERSION_NAME = "1.0";
    
    /**
     * Private constructor to prevent instantiation
     */
    private BuildConfig() {
        // Private constructor
    }
}
