// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemReleaseBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialCardView cardRelease;

  @NonNull
  public final Chip chipFileSize;

  @NonNull
  public final Chip chipGameVersion;

  @NonNull
  public final ChipGroup chipGroupInfo;

  @NonNull
  public final Chip chipReleaseDate;

  @NonNull
  public final Chip chipStatus;

  @NonNull
  public final ImageView ivReleaseIcon;

  @NonNull
  public final View rippleEffect;

  @NonNull
  public final TextView tvReleaseDescription;

  @NonNull
  public final TextView tvReleaseName;

  @NonNull
  public final TextView tvReleaseVersion;

  private ItemReleaseBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialCardView cardRelease, @NonNull Chip chipFileSize,
      @NonNull Chip chipGameVersion, @NonNull ChipGroup chipGroupInfo,
      @NonNull Chip chipReleaseDate, @NonNull Chip chipStatus, @NonNull ImageView ivReleaseIcon,
      @NonNull View rippleEffect, @NonNull TextView tvReleaseDescription,
      @NonNull TextView tvReleaseName, @NonNull TextView tvReleaseVersion) {
    this.rootView = rootView;
    this.cardRelease = cardRelease;
    this.chipFileSize = chipFileSize;
    this.chipGameVersion = chipGameVersion;
    this.chipGroupInfo = chipGroupInfo;
    this.chipReleaseDate = chipReleaseDate;
    this.chipStatus = chipStatus;
    this.ivReleaseIcon = ivReleaseIcon;
    this.rippleEffect = rippleEffect;
    this.tvReleaseDescription = tvReleaseDescription;
    this.tvReleaseName = tvReleaseName;
    this.tvReleaseVersion = tvReleaseVersion;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemReleaseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemReleaseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_release, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemReleaseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      MaterialCardView cardRelease = (MaterialCardView) rootView;

      id = R.id.chip_file_size;
      Chip chipFileSize = ViewBindings.findChildViewById(rootView, id);
      if (chipFileSize == null) {
        break missingId;
      }

      id = R.id.chip_game_version;
      Chip chipGameVersion = ViewBindings.findChildViewById(rootView, id);
      if (chipGameVersion == null) {
        break missingId;
      }

      id = R.id.chip_group_info;
      ChipGroup chipGroupInfo = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupInfo == null) {
        break missingId;
      }

      id = R.id.chip_release_date;
      Chip chipReleaseDate = ViewBindings.findChildViewById(rootView, id);
      if (chipReleaseDate == null) {
        break missingId;
      }

      id = R.id.chip_status;
      Chip chipStatus = ViewBindings.findChildViewById(rootView, id);
      if (chipStatus == null) {
        break missingId;
      }

      id = R.id.iv_release_icon;
      ImageView ivReleaseIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivReleaseIcon == null) {
        break missingId;
      }

      id = R.id.ripple_effect;
      View rippleEffect = ViewBindings.findChildViewById(rootView, id);
      if (rippleEffect == null) {
        break missingId;
      }

      id = R.id.tv_release_description;
      TextView tvReleaseDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvReleaseDescription == null) {
        break missingId;
      }

      id = R.id.tv_release_name;
      TextView tvReleaseName = ViewBindings.findChildViewById(rootView, id);
      if (tvReleaseName == null) {
        break missingId;
      }

      id = R.id.tv_release_version;
      TextView tvReleaseVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvReleaseVersion == null) {
        break missingId;
      }

      return new ItemReleaseBinding((MaterialCardView) rootView, cardRelease, chipFileSize,
          chipGameVersion, chipGroupInfo, chipReleaseDate, chipStatus, ivReleaseIcon, rippleEffect,
          tvReleaseDescription, tvReleaseName, tvReleaseVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
