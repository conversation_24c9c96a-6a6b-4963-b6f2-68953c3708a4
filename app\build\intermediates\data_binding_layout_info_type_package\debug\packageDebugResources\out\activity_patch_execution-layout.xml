<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_patch_execution" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_patch_execution.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_patch_execution_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="174" endOffset="51"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="19" endOffset="43"/></Target><Target id="@+id/tv_patch_name" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="34" endOffset="40"/></Target><Target id="@+id/card_mode" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="36" startOffset="4" endLine="92" endOffset="55"/></Target><Target id="@+id/radio_group_mode" view="RadioGroup"><Expressions/><location startLine="64" startOffset="12" endLine="88" endOffset="24"/></Target><Target id="@+id/radio_root" view="RadioButton"><Expressions/><location startLine="71" startOffset="16" endLine="78" endOffset="61"/></Target><Target id="@+id/radio_non_root" view="RadioButton"><Expressions/><location startLine="80" startOffset="16" endLine="86" endOffset="61"/></Target><Target id="@+id/tv_logs_title" view="TextView"><Expressions/><location startLine="94" startOffset="4" endLine="105" endOffset="62"/></Target><Target id="@+id/card_logs" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="107" startOffset="4" endLine="138" endOffset="55"/></Target><Target id="@+id/tv_logs" view="TextView"><Expressions/><location startLine="127" startOffset="12" endLine="134" endOffset="46"/></Target><Target id="@+id/btn_start_patching" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="140" startOffset="4" endLine="150" endOffset="55"/></Target><Target id="@+id/progress_patching" view="ProgressBar"><Expressions/><location startLine="152" startOffset="4" endLine="161" endOffset="51"/></Target><Target id="@+id/view_overlay" view="View"><Expressions/><location startLine="163" startOffset="4" endLine="172" endOffset="51"/></Target></Targets></Layout>