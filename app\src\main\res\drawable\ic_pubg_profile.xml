<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
    <!-- PUBG Profile/Avatar Icon -->
    <!-- Helmet outline -->
    <path
        android:fillColor="#58A6FF"
        android:pathData="M16,4C11.58,4 8,7.58 8,12v4c0,2.21 1.79,4 4,4h8c2.21,0 4,-1.79 4,-4v-4C24,7.58 20.42,4 16,4z"/>
    <!-- Face/Visor -->
    <path
        android:fillColor="#21262D"
        android:pathData="M16,6c-3.31,0 -6,2.69 -6,6v2c0,1.1 0.9,2 2,2h8c1.1,0 2,-0.9 2,-2v-2C22,8.69 19.31,6 16,6z"/>
    <!-- Tactical visor reflection -->
    <path
        android:fillColor="#00D084"
        android:pathData="M12,10h8v1h-8z"/>
    <path
        android:fillColor="#00D084"
        android:pathData="M12,12h6v1h-6z"/>
    <!-- Crosshair on helmet -->
    <path
        android:fillColor="#F85149"
        android:pathData="M15.5,7.5h1v1h-1z"/>
    <!-- Body/Shoulders -->
    <path
        android:fillColor="#30363D"
        android:pathData="M10,20h12v8h-12z"/>
    <!-- Tactical vest details -->
    <path
        android:fillColor="#58A6FF"
        android:pathData="M12,22h2v1h-2zM18,22h2v1h-2z"/>
    <path
        android:fillColor="#00D084"
        android:pathData="M14,24h4v1h-4z"/>
</vector>
