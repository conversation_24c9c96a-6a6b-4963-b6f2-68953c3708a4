package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import android.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.MessageDigest;

/**
 * Target Mod Authentication System
 * This class is used by target mod apps to check for valid BearToken
 * If valid token exists, skip KeyAuth authentication
 */
public class TargetModAuth {
    
    private static final String TAG = "TargetModAuth";
    private static final String AES_KEY = "BearModLoader2024"; // Must match BearTokenManager
    
    /**
     * Check if target mod can skip KeyAuth authentication
     * @param context Target mod context
     * @return AuthResult indicating whether to skip KeyAuth
     */
    public static AuthResult checkBearTokenAuth(Context context) {
        try {
            Log.d(TAG, "Checking BearToken authentication for target mod");
            
            // Load shared BearToken
            BearTokenData tokenData = loadSharedBearToken(context);
            if (tokenData == null) {
                Log.d(TAG, "No BearToken found, KeyAuth required");
                return new AuthResult(false, "No BearToken found", null);
            }
            
            // Check token expiry
            if (System.currentTimeMillis() > tokenData.expiryTime) {
                Log.d(TAG, "BearToken expired, KeyAuth required");
                return new AuthResult(false, "BearToken expired", null);
            }
            
            // Verify device ID
            String currentDeviceId = getDeviceId();
            if (!currentDeviceId.equals(tokenData.deviceId)) {
                Log.e(TAG, "Device ID mismatch, KeyAuth required");
                return new AuthResult(false, "Device ID mismatch", null);
            }
            
            // Verify loader signature in token
            if (!verifyLoaderSignature(context, tokenData)) {
                Log.e(TAG, "Loader signature verification failed, KeyAuth required");
                return new AuthResult(false, "Invalid loader signature", null);
            }
            
            // Decrypt and validate token payload
            TokenPayload payload = decryptTokenPayload(tokenData.encryptedPayload);
            if (payload == null) {
                Log.e(TAG, "Token decryption failed, KeyAuth required");
                return new AuthResult(false, "Token decryption failed", null);
            }
            
            // Verify token integrity
            if (!verifyTokenIntegrity(tokenData, payload)) {
                Log.e(TAG, "Token integrity verification failed, KeyAuth required");
                return new AuthResult(false, "Token integrity verification failed", null);
            }
            
            Log.d(TAG, "BearToken authentication successful, skipping KeyAuth");
            return new AuthResult(true, "BearToken valid", payload);
            
        } catch (Exception e) {
            Log.e(TAG, "BearToken authentication failed", e);
            return new AuthResult(false, "Authentication error: " + e.getMessage(), null);
        }
    }
    
    /**
     * Load shared BearToken from loader app
     */
    private static BearTokenData loadSharedBearToken(Context context) {
        try {
            SharedPreferences sharedPrefs = context.getSharedPreferences("bearmod_shared", Context.MODE_WORLD_READABLE);
            
            String encryptedPayload = sharedPrefs.getString("bear_token", null);
            String signature = sharedPrefs.getString("bear_signature", null);
            long expiryTime = sharedPrefs.getLong("bear_expiry", 0);
            String deviceId = sharedPrefs.getString("bear_device", null);
            long createdTime = sharedPrefs.getLong("bear_created", 0);
            
            if (encryptedPayload == null || signature == null || expiryTime == 0 || deviceId == null) {
                return null;
            }
            
            return new BearTokenData(encryptedPayload, signature, expiryTime, deviceId, createdTime);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to load shared BearToken", e);
            return null;
        }
    }
    
    /**
     * Verify loader signature from token
     */
    private static boolean verifyLoaderSignature(Context context, BearTokenData tokenData) {
        try {
            // Get expected loader package name
            String loaderPackageName = "com.bearmod.loader"; // Your loader package name
            
            // Check if loader is installed
            try {
                context.getPackageManager().getPackageInfo(loaderPackageName, 0);
            } catch (Exception e) {
                Log.e(TAG, "BearMod Loader not installed");
                return false;
            }
            
            // For now, we trust the signature in the token
            // In a more secure implementation, you could verify the loader's actual signature
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Loader signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Decrypt token payload
     */
    private static TokenPayload decryptTokenPayload(String encryptedPayload) {
        try {
            byte[] combined = Base64.decode(encryptedPayload, Base64.DEFAULT);
            
            // Extract IV and encrypted data
            byte[] iv = new byte[16];
            byte[] encrypted = new byte[combined.length - 16];
            System.arraycopy(combined, 0, iv, 0, 16);
            System.arraycopy(combined, 16, encrypted, 0, encrypted.length);
            
            // Decrypt
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decrypted = cipher.doFinal(encrypted);
            
            String decryptedText = new String(decrypted);
            
            // Parse token payload
            String[] parts = decryptedText.split("\\|");
            if (parts.length != 6) {
                Log.e(TAG, "Invalid token payload format");
                return null;
            }
            
            return new TokenPayload(
                parts[0], // keyAuthSessionId
                parts[1], // hashedLicenseKey
                parts[2], // deviceId
                Long.parseLong(parts[3]), // createdTime
                Long.parseLong(parts[4]), // expiryTime
                parts[5]  // loaderSignature
            );
            
        } catch (Exception e) {
            Log.e(TAG, "Token decryption failed", e);
            return null;
        }
    }
    
    /**
     * Verify token integrity
     */
    private static boolean verifyTokenIntegrity(BearTokenData tokenData, TokenPayload payload) {
        try {
            // Generate expected signature
            String signatureInput = tokenData.encryptedPayload + "|" + payload.loaderSignature + "|" + AES_KEY;
            String expectedSignature = hashString(signatureInput);
            
            return expectedSignature.equals(tokenData.signature);
            
        } catch (Exception e) {
            Log.e(TAG, "Token integrity verification failed", e);
            return false;
        }
    }
    
    /**
     * Hash string using SHA-256
     */
    private static String hashString(String input) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(input.getBytes());
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
    
    /**
     * Get device ID for verification
     */
    private static String getDeviceId() {
        String deviceInfo = android.os.Build.FINGERPRINT + 
                           android.os.Build.SERIAL + 
                           android.os.Build.ID;
        try {
            return hashString(deviceInfo).substring(0, 16);
        } catch (Exception e) {
            return "unknown_device";
        }
    }
    
    /**
     * Authentication result
     */
    public static class AuthResult {
        public final boolean canSkipKeyAuth;
        public final String message;
        public final TokenPayload tokenPayload;
        
        public AuthResult(boolean canSkipKeyAuth, String message, TokenPayload tokenPayload) {
            this.canSkipKeyAuth = canSkipKeyAuth;
            this.message = message;
            this.tokenPayload = tokenPayload;
        }
        
        @Override
        public String toString() {
            return "AuthResult{canSkipKeyAuth=" + canSkipKeyAuth + ", message='" + message + "'}";
        }
    }
    
    /**
     * Token data from shared storage
     */
    private static class BearTokenData {
        final String encryptedPayload;
        final String signature;
        final long expiryTime;
        final String deviceId;
        final long createdTime;
        
        BearTokenData(String encryptedPayload, String signature, long expiryTime, String deviceId, long createdTime) {
            this.encryptedPayload = encryptedPayload;
            this.signature = signature;
            this.expiryTime = expiryTime;
            this.deviceId = deviceId;
            this.createdTime = createdTime;
        }
    }
    
    /**
     * Decrypted token payload
     */
    public static class TokenPayload {
        public final String keyAuthSessionId;
        public final String hashedLicenseKey;
        public final String deviceId;
        public final long createdTime;
        public final long expiryTime;
        public final String loaderSignature;
        
        TokenPayload(String keyAuthSessionId, String hashedLicenseKey, String deviceId, 
                    long createdTime, long expiryTime, String loaderSignature) {
            this.keyAuthSessionId = keyAuthSessionId;
            this.hashedLicenseKey = hashedLicenseKey;
            this.deviceId = deviceId;
            this.createdTime = createdTime;
            this.expiryTime = expiryTime;
            this.loaderSignature = loaderSignature;
        }
        
        public boolean isValid() {
            return System.currentTimeMillis() < expiryTime;
        }
        
        public long getRemainingTime() {
            return Math.max(0, expiryTime - System.currentTimeMillis());
        }
    }
}
