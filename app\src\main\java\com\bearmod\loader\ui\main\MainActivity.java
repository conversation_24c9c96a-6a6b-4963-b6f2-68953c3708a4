package com.bearmod.loader.ui.main;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import java.text.DecimalFormat;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.bearmod.loader.R;
import com.bearmod.loader.auth.AuthResult;
import com.bearmod.loader.auth.KeyAuthManager;

import com.bearmod.loader.cloud.CloudSyncManager;
import com.bearmod.loader.databinding.ActivityMainBinding;
import com.bearmod.loader.download.DownloadManager;
import com.bearmod.loader.model.Patch;
import com.bearmod.loader.patch.PatchManager;
import com.bearmod.loader.repository.PatchRepository;
import com.bearmod.loader.ui.auth.LoginActivity;

import com.bearmod.loader.ui.patch.PatchExecutionActivity;
import com.bearmod.loader.ui.settings.SettingsActivity;
import com.google.android.material.navigation.NavigationView;
import com.google.android.material.snackbar.Snackbar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Main activity (Dashboard)
 * Displays available patches and target app selection
 */
public class MainActivity extends AppCompatActivity implements
        NavigationView.OnNavigationItemSelectedListener,
        CloudSyncManager.CloudSyncListener {

    private ActivityMainBinding binding;
    private PatchAdapter patchAdapter;
    private KeyAuthManager keyAuthManager;
    private CloudSyncManager cloudSyncManager;

    // Mock data for demonstration
    private final List<String> targetApps = Arrays.asList(
            "PUBG MOBILE GLOBAL v.1.3.0",
            "PUBG MOBILE KOREA v.1.3.0",
            "PUBG MOBILE TAIWAN v.1.3.0",
            "PUBG MOBILE VNG v.1.3.0"
    );

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize view binding
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Initialize managers
        keyAuthManager = KeyAuthManager.getInstance();
        cloudSyncManager = CloudSyncManager.getInstance();
        cloudSyncManager.initialize(this);
        cloudSyncManager.addListener(this);

        PatchManager patchManager = PatchManager.getInstance();
        patchManager.initialize(this);

        // Set up toolbar
        setSupportActionBar(binding.toolbar);

        // Set up navigation drawer
        ActionBarDrawerToggle toggle = new ActionBarDrawerToggle(
                this, binding.drawerLayout, binding.toolbar,
                R.string.navigation_drawer_open, R.string.navigation_drawer_close);
        binding.drawerLayout.addDrawerListener(toggle);
        toggle.syncState();

        // Set up navigation view
        binding.navView.setNavigationItemSelectedListener(this);

        // Set up bottom navigation
        binding.bottomNavigation.setOnItemSelectedListener(item -> {
            int itemId = item.getItemId();
            if (itemId == R.id.nav_home) {
                return true; // Already on home
            } else if (itemId == R.id.nav_downloads) {
                // Show enhanced download info instead of navigating to separate activity
                new androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("Enhanced Download System")
                    .setMessage("Download features are integrated into the main interface:\n\n" +
                               "• Click download buttons on patches for APK/OBB options\n" +
                               "• Real-time progress tracking\n" +
                               "• Auto-installation support\n" +
                               "• Multiple download types available")
                    .setPositiveButton("OK", null)
                    .show();
                return true;
            } else if (itemId == R.id.nav_settings) {
                Intent intent = new Intent(this, SettingsActivity.class);
                startActivity(intent);
                return true;
            }
            return false;
        });

        // Set up target app spinner
        ArrayAdapter<String> targetAppAdapter = new ArrayAdapter<>(
                this, android.R.layout.simple_spinner_dropdown_item, targetApps);
        binding.spinnerTargetApp.setAdapter(targetAppAdapter);

        // Set up scan offsets button
        binding.btnScanOffsets.setOnClickListener(v -> scanOffsets());

        // Set up download patches button - now shows enhanced download options
        binding.btnDownloadPatches.setOnClickListener(v -> {
            // Show info about enhanced download system
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Enhanced Download System")
                .setMessage("Use the download buttons on individual patches to access:\n\n" +
                           "• APK Only downloads\n" +
                           "• APK + OBB packages\n" +
                           "• OBB data files\n" +
                           "• Progress tracking\n" +
                           "• Auto-installation")
                .setPositiveButton("OK", null)
                .show();
        });

        // Set up RecyclerView with lambda functions for click handling
        binding.rvPatches.setLayoutManager(new LinearLayoutManager(this));
        patchAdapter = new PatchAdapter(
                this,
                this::onPatchClick,  // Lambda for patch click
                this::onApplyPatchClick,  // Lambda for apply patch click
                this::onDownloadPatchClick  // Lambda for download patch click
        );
        binding.rvPatches.setAdapter(patchAdapter);

        // Set up SwipeRefreshLayout
        binding.swipeRefresh.setOnRefreshListener(() -> {
            // Sync patches from cloud
            cloudSyncManager.syncPatches();
        });
        binding.swipeRefresh.setColorSchemeResources(
                R.color.primary,
                R.color.accent,
                R.color.primary_dark
        );

        // Load patches
        loadPatches();

        // Update license info in navigation header
        updateLicenseInfo();
    }

    /**
     * Update license info in navigation header
     */
    private void updateLicenseInfo() {
        keyAuthManager.validateLicense(new KeyAuthManager.AuthCallback() {
            @Override
            public void onSuccess(AuthResult result) {
                // Get license info
                String expiryDate = keyAuthManager.formatExpiryDate(result.getExpiryDate());
                int remainingDays = keyAuthManager.getRemainingDays(result.getExpiryDate());

                // Update navigation header
                View headerView = binding.navView.getHeaderView(0);
                if (headerView != null) {
                    headerView.findViewById(R.id.tv_license_info).post(() -> {
                        if (headerView.findViewById(R.id.tv_license_info) != null) {
                            String licenseText;
                            if (remainingDays == -1) {
                                licenseText = "License: Active (No expiry)";
                            } else {
                                licenseText = getString(R.string.days_remaining, remainingDays);
                            }
                            ((android.widget.TextView) headerView.findViewById(R.id.tv_license_info))
                                    .setText(licenseText);
                        }
                    });
                }
            }

            @Override
            public void onError(String error) {
                // Handle error
                Toast.makeText(MainActivity.this, error, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * Load patches with lazy loading
     */
    private void loadPatches() {
        // Show shimmer effect
        binding.shimmerLayout.setVisibility(View.VISIBLE);
        binding.rvPatches.setVisibility(View.GONE);
        binding.layoutEmpty.setVisibility(View.GONE);

        // Get repository instance
        PatchRepository repository = PatchRepository.getInstance(this);

        // Observe patches from repository for lazy loading
        repository.getAllPatches().observe(this, patches -> {
            // Only update UI if we have patches and aren't currently syncing
            if (patches != null && !patches.isEmpty() && binding.shimmerLayout.getVisibility() != View.VISIBLE) {
                updatePatchesUI(patches);
            }
        });

        // Sync patches from cloud
        cloudSyncManager.syncPatches();
    }

    /**
     * Update patches UI
     * @param patches Patches to display
     */
    private void updatePatchesUI(List<Patch> patches) {
        // Hide shimmer effect
        binding.shimmerLayout.setVisibility(View.GONE);

        // Check if patches list is empty
        if (patches.isEmpty()) {
            binding.layoutEmpty.setVisibility(View.VISIBLE);
            binding.rvPatches.setVisibility(View.GONE);
        } else {
            binding.layoutEmpty.setVisibility(View.GONE);
            binding.rvPatches.setVisibility(View.VISIBLE);

            // Update adapter
            patchAdapter.updatePatches(patches);
        }
    }

    /**
     * Handle cloud sync completion
     * @param patches Synced patches
     */
    @Override
    public void onSyncComplete(List<Patch> patches) {
        // Stop refresh animation
        binding.swipeRefresh.setRefreshing(false);

        // Update UI with patches
        updatePatchesUI(patches);

        // Show snackbar with update info
        Snackbar.make(binding.getRoot(), R.string.patches_updated, Snackbar.LENGTH_SHORT).show();

        // Save patches to repository
        PatchRepository repository = PatchRepository.getInstance(this);
        for (Patch patch : patches) {
            repository.savePatch(patch, new PatchRepository.PatchCallback() {
                @Override
                public void onSuccess(List<Patch> patches) {
                    // Patch saved successfully
                }

                @Override
                public void onError(String error) {
                    // Log error but don't show to user
                    Log.e("MainActivity", "Error saving patch: " + error);
                }
            });
        }
    }

    /**
     * Handle cloud sync error
     * @param error Error message
     */
    @Override
    public void onSyncError(String error) {
        // Stop refresh animation
        binding.swipeRefresh.setRefreshing(false);

        // Show error message
        Toast.makeText(this, getString(R.string.sync_error, error), Toast.LENGTH_LONG).show();

        // Get repository instance
        PatchRepository repository = PatchRepository.getInstance(this);

        // Try to load patches from local database
        repository.getLatestPatches("1.0.5", new PatchRepository.PatchCallback() {
            @Override
            public void onSuccess(List<Patch> patches) {
                if (patches != null && !patches.isEmpty()) {
                    // Update UI with patches from database
                    updatePatchesUI(patches);
                } else {
                    // Load mock patches as fallback if database is empty
                    updatePatchesUI(createMockPatches());
                }
            }

            @Override
            public void onError(String error) {
                // Load mock patches as fallback
                updatePatchesUI(createMockPatches());
            }
        });
    }

    /**
     * Create mock patches for demonstration
     * @return List of mock patches
     */
    private List<Patch> createMockPatches() {
        List<Patch> patches = new ArrayList<>();

        // Add mock patches
        patches.add(new Patch(
                "1",
                "PUBG GLOBAL 3.8.0",
                "Memory values have been updated to improve gameplay.",
                "64-bit",
                "2025-05-26",
                Patch.PatchStatus.UP_TO_DATE
        ));

        patches.add(new Patch(
                "2",
                "PUBG KOREAN 3.8.0",
                "Memory values have been updated to improve gameplay.",
                "64-bit",
                "2025-05-26",
                Patch.PatchStatus.UPDATE_AVAILABLE
        ));

        patches.add(new Patch(
                "3",
                "PUBG VIETNAM 3.8.0",
                "Memory values have been updated to improve gameplay.",
                "64-bit",
                "2025-05-26",
                Patch.PatchStatus.NOT_INSTALLED
        ));

        return patches;
    }

    /**
     * Scan offsets
     */
    private void scanOffsets() {
        // Get selected target app
        String targetApp = (String) binding.spinnerTargetApp.getSelectedItem();

        // Show toast
        Toast.makeText(this,
                "Scanning offsets for " + targetApp,
                Toast.LENGTH_SHORT).show();

        // Show scanning dialog
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle(R.string.scanning_offsets)
                .setMessage(getString(R.string.scanning_offsets_for, targetApp))
                .setPositiveButton(R.string.ok, null)
                .show();
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        // Handle navigation view item clicks
        int id = item.getItemId();

        if (id == R.id.nav_dashboard) {
            // Already on dashboard, do nothing
        } else if (id == R.id.nav_download) {
            // Show enhanced download info instead of navigating to separate activity
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Enhanced Download System")
                .setMessage("Download features are integrated into the main interface:\n\n" +
                           "• Click download buttons on patches for APK/OBB options\n" +
                           "• Real-time progress tracking\n" +
                           "• Auto-installation support\n" +
                           "• Multiple download types available")
                .setPositiveButton("OK", null)
                .show();
        } else if (id == R.id.nav_settings) {
            // Navigate to settings activity
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        } else if (id == R.id.nav_logout) {
            // Logout
            logout();
        }

        // Close drawer
        binding.drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }

    /**
     * Logout user
     */
    private void logout() {
        // Logout with KeyAuth
        keyAuthManager.logout();

        // Navigate to login activity
        Intent intent = new Intent(this, LoginActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        // Close drawer if open
        if (binding.drawerLayout.isDrawerOpen(GravityCompat.START)) {
            binding.drawerLayout.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    /**
     * Handle patch click event
     * @param patch Clicked patch
     */
    private void onPatchClick(Patch patch) {
        // Show patch details
        Toast.makeText(this,
                "Patch details: " + patch.getName(),
                Toast.LENGTH_SHORT).show();
    }

    /**
     * Handle apply patch click event - now shows download options
     * @param patch Patch to apply
     */
    private void onApplyPatchClick(Patch patch) {
        // Show the enhanced APK/OBB download options dialog
        showEnhancedDownloadOptionsDialog(patch);
    }

    /**
     * Handle download patch click event - shows APK/OBB download options
     * @param patch Patch to download
     */
    private void onDownloadPatchClick(Patch patch) {
        Log.d("MainActivity", "Download button clicked for: " + patch.getName());
        try {
            // Show the enhanced APK/OBB download options dialog
            showEnhancedDownloadOptionsDialog(patch);
        } catch (Exception e) {
            Log.e("MainActivity", "Error showing download options dialog", e);
            Toast.makeText(this, "Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }



    /**
     * Show enhanced download options dialog with APK/OBB choices
     * @param patch Selected patch
     */
    private void showEnhancedDownloadOptionsDialog(Patch patch) {
        Log.d("MainActivity", "Showing enhanced download options dialog for: " + patch.getName());

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Download " + patch.getName());
        builder.setMessage("Choose download type:");

        // Enhanced download options with better descriptions
        String[] options = {
            "APK Only (120.5 MB)\nGame application file with auto-installation",
            "APK + OBB (1100.8 MB)\nComplete game package with data files",
            "OBB Only (980.3 MB)\nGame data files for existing installation"
        };

        builder.setItems(options, (dialog, which) -> {
            DownloadManager.DownloadType downloadType;
            switch (which) {
                case 0:
                    downloadType = DownloadManager.DownloadType.APK_ONLY;
                    break;
                case 1:
                    downloadType = DownloadManager.DownloadType.APK_AND_OBB;
                    break;
                case 2:
                    downloadType = DownloadManager.DownloadType.OBB_ONLY;
                    break;
                default:
                    return;
            }

            startEnhancedDownload(patch, downloadType);
        });

        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    /**
     * Start enhanced download with unified download manager
     * @param patch Patch to download
     * @param downloadType Type of download
     */
    private void startEnhancedDownload(Patch patch, DownloadManager.DownloadType downloadType) {
        Log.d("MainActivity", "Starting enhanced download for: " + patch.getName() + " with type: " + downloadType);

        try {
            DownloadManager downloadManager = DownloadManager.getInstance();
            Log.d("MainActivity", "DownloadManager instance obtained");

            downloadManager.downloadGameFilesEnhanced(this, patch, downloadType, new DownloadManager.EnhancedDownloadListener() {
                @Override
                public void onSuccess(String filePath) {
                    Log.d("MainActivity", "Download completed successfully: " + filePath);
                    runOnUiThread(() -> {
                        Toast.makeText(MainActivity.this,
                            "Download completed: " + patch.getName(),
                            Toast.LENGTH_SHORT).show();
                    });
                }

                @Override
                public void onError(String error) {
                    Log.e("MainActivity", "Download failed: " + error);
                    runOnUiThread(() -> {
                        Toast.makeText(MainActivity.this,
                            "Download failed: " + error,
                            Toast.LENGTH_LONG).show();
                    });
                }
            });

            Log.d("MainActivity", "Download request submitted successfully");

        } catch (Exception e) {
            Log.e("MainActivity", "Error starting download", e);
            Toast.makeText(this, "Error starting download: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }


}
