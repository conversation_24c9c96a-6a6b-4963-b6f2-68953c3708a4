<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/splash_gradient_modern">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:padding="24dp">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/logoContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                app:cardCornerRadius="32dp"
                app:cardElevation="8dp"
                app:strokeWidth="0dp"
                app:cardBackgroundColor="@color/surface_variant">

                <ImageView
                    android:id="@+id/logoImage"
                    android:layout_width="160dp"
                    android:layout_height="160dp"
                    android:importantForAccessibility="no"
                    android:src="@drawable/logo"
                    android:scaleType="centerCrop"
                    android:padding="16dp" />

            </com.google.android.material.card.MaterialCardView>

            <TextView
                android:id="@+id/appNameText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="BEAR-MOD"
                android:textColor="@color/white"
                android:textSize="32sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-condensed"
                android:layout_marginTop="24dp"
                android:letterSpacing="0.1"
                tools:ignore="HardcodedText" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayout"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="48dp"
                android:hint="License Key"
                app:hintTextColor="@color/white"
                app:boxStrokeColor="@color/white"
                app:endIconMode="password_toggle"
                app:endIconTint="@color/white"
                app:boxBackgroundColor="#40FFFFFF"
                app:boxCornerRadiusTopStart="16dp"
                app:boxCornerRadiusTopEnd="16dp"
                app:boxCornerRadiusBottomStart="16dp"
                app:boxCornerRadiusBottomEnd="16dp"
                tools:ignore="HardcodedText">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editLicenseKey"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:inputType="textPassword"
                    android:importantForAutofill="yes"
                    android:autofillHints="password"
                    android:imeOptions="actionDone" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/checkboxRemember"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Remember Key"
                android:textColor="@color/white"
                android:layout_marginTop="24dp"
                android:buttonTint="@color/white"
                tools:ignore="HardcodedText" />

            <com.google.android.material.checkbox.MaterialCheckBox
                android:id="@+id/checkboxAutoLogin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Automatic Login"
                android:textColor="@color/white"
                android:layout_marginTop="8dp"
                android:buttonTint="@color/white"
                tools:ignore="HardcodedText" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnLogin"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:text="LOGIN"
                android:textStyle="bold"
                android:textSize="16sp"
                android:layout_marginTop="32dp"
                app:cornerRadius="32dp"
                android:letterSpacing="0.1"
                app:elevation="8dp"
                android:backgroundTint="@color/white"
                android:textColor="@color/primary"
                tools:ignore="HardcodedText" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:visibility="gone"
                android:indeterminateTint="@color/white" />

            <TextView
                android:id="@+id/versionText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_version"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:layout_marginTop="32dp"
                android:alpha="0.7" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
