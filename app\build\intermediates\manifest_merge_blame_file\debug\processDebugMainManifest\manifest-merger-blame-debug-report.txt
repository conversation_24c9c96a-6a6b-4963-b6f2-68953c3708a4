1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="3"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission
16-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
18        android:maxSdkVersion="32" />
18-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-14:40
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
21        android:maxSdkVersion="32" />
21-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- Media permissions for Android 13+ -->
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-76
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:5-75
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:22-72
27
28    <!-- Selected Photos Access for Android 14+ -->
29    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:5-90
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:22-87
30
31    <!-- Vibration permission -->
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:5-66
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:22-63
33
34    <!-- Additional permissions -->
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-68
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-77
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-74
37    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:5-80
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:22-77
38
39    <!-- APK installation permission -->
40    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-83
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-80
41    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:5-35:47
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:22-72
42    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
43
44    <permission
44-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-112:19
51        android:name="com.bearmod.loader.BearLoaderApplication"
51-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-46
52        android:allowBackup="true"
52-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-54
58        android:icon="@mipmap/ic_launcher"
58-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-43
59        android:label="@string/app_name"
59-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-41
60        android:networkSecurityConfig="@xml/network_security_config"
60-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:48:9-69
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-54
62        android:supportsRtl="true"
62-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-35
63        android:theme="@style/Theme.BearLoader"
63-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-48
64        android:usesCleartextTraffic="true" >
64-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-44
65
66        <!-- Splash Activity -->
67        <activity
67-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:9-62:20
68            android:name="com.bearmod.loader.ui.splash.SplashActivity"
68-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-53
69            android:exported="true"
69-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-36
70            android:screenOrientation="unspecified"
70-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:13-52
71            android:theme="@style/Theme.BearLoader.NoActionBar" >
71-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-64
72            <intent-filter>
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:13-61:29
73                <action android:name="android.intent.action.MAIN" />
73-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:17-69
73-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:17-77
75-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:27-74
76            </intent-filter>
77        </activity>
78
79        <!-- Login Activity -->
80        <activity
80-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:9-69:58
81            android:name="com.bearmod.loader.ui.auth.LoginActivity"
81-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-50
82            android:exported="false"
82-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:67:13-37
83            android:theme="@style/Theme.BearLoader.NoActionBar"
83-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:68:13-64
84            android:windowSoftInputMode="adjustResize" />
84-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:69:13-55
85
86        <!-- Tap to Unlock Activity -->
87        <activity
87-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:9-75:67
88            android:name="com.bearmod.loader.ui.auth.TapToUnlockActivity"
88-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-56
89            android:exported="false"
89-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:74:13-37
90            android:theme="@style/Theme.BearLoader.NoActionBar" />
90-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:75:13-64
91
92        <!-- Main Loader Activity -->
93        <activity
93-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:9-82:46
94            android:name="com.bearmod.loader.ui.main.MainLoaderActivity"
94-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-55
95            android:exported="false"
95-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:80:13-37
96            android:launchMode="singleTop"
96-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:82:13-43
97            android:theme="@style/Theme.BearLoader.NoActionBar" />
97-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:81:13-64
98
99        <!-- Modern Download Activity -->
100        <activity
100-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:9-89:46
101            android:name="com.bearmod.loader.ui.download.ModernDownloadActivity"
101-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-63
102            android:exported="false"
102-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:87:13-37
103            android:launchMode="singleTop"
103-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:89:13-43
104            android:theme="@style/Theme.BearLoader.NoActionBar" />
104-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:88:13-64
105
106        <!-- Settings Activity -->
107        <activity
107-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:94:9-99:45
108            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
108-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:95:13-57
109            android:exported="false"
109-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:13-37
110            android:screenOrientation="unspecified"
110-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:13-52
111            android:theme="@style/Theme.BearLoader.NoActionBar" />
111-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:97:13-64
112
113        <!-- FileProvider for APK installation -->
114        <provider
115            android:name="androidx.core.content.FileProvider"
115-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:13-62
116            android:authorities="com.bearmod.loader.fileprovider"
116-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-64
117            android:exported="false"
117-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:13-37
118            android:grantUriPermissions="true" >
118-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:13-47
119            <meta-data
119-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:107:13-109:54
120                android:name="android.support.FILE_PROVIDER_PATHS"
120-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:108:17-67
121                android:resource="@xml/file_paths" />
121-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:109:17-51
122        </provider>
123        <provider
123-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
124            android:name="androidx.startup.InitializationProvider"
124-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
125            android:authorities="com.bearmod.loader.androidx-startup"
125-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
126            android:exported="false" >
126-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
127            <meta-data
127-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
128                android:name="androidx.work.WorkManagerInitializer"
128-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
129                android:value="androidx.startup" />
129-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
130            <meta-data
130-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.emoji2.text.EmojiCompatInitializer"
131-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
132                android:value="androidx.startup" />
132-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
134-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
135                android:value="androidx.startup" />
135-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
137-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
138                android:value="androidx.startup" />
138-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
139        </provider>
140
141        <service
141-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
142            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
142-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
144            android:enabled="@bool/enable_system_alarm_service_default"
144-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
145            android:exported="false" />
145-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
146        <service
146-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
147            android:name="androidx.work.impl.background.systemjob.SystemJobService"
147-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
148            android:directBootAware="false"
148-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
149            android:enabled="@bool/enable_system_job_service_default"
149-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
150            android:exported="true"
150-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
151            android:permission="android.permission.BIND_JOB_SERVICE" />
151-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
152        <service
152-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
153            android:name="androidx.work.impl.foreground.SystemForegroundService"
153-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
155            android:enabled="@bool/enable_system_foreground_service_default"
155-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
156            android:exported="false" />
156-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
157
158        <receiver
158-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
159            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
159-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
161            android:enabled="true"
161-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
162            android:exported="false" />
162-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
163        <receiver
163-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
164-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
169                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
169-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
169-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
170                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
170-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
170-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
174            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
174-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
176            android:enabled="false"
176-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
179                <action android:name="android.intent.action.BATTERY_OKAY" />
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
180                <action android:name="android.intent.action.BATTERY_LOW" />
180-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
180-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
184-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
186            android:enabled="false"
186-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
189                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
190                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
190-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
190-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
194            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
194-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
196            android:enabled="false"
196-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
197            android:exported="false" >
197-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
198            <intent-filter>
198-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
199                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
199-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
199-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
200            </intent-filter>
201        </receiver>
202        <receiver
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
203            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
203-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
205            android:enabled="false"
205-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
208                <action android:name="android.intent.action.BOOT_COMPLETED" />
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
209                <action android:name="android.intent.action.TIME_SET" />
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
210                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
210-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
210-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
211            </intent-filter>
212        </receiver>
213        <receiver
213-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
214            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
214-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
216            android:enabled="@bool/enable_system_alarm_service_default"
216-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
217            android:exported="false" >
217-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
218            <intent-filter>
218-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
219                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
219-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
219-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
220            </intent-filter>
221        </receiver>
222        <receiver
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
223            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
223-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
225            android:enabled="true"
225-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
226            android:exported="true"
226-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
227            android:permission="android.permission.DUMP" >
227-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
228            <intent-filter>
228-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
229                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
229-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
229-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
230            </intent-filter>
231        </receiver>
232
233        <uses-library
233-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
234            android:name="androidx.window.extensions"
234-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
235            android:required="false" />
235-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
236        <uses-library
236-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
237            android:name="androidx.window.sidecar"
237-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
238            android:required="false" />
238-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
239
240        <service
240-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
241            android:name="androidx.room.MultiInstanceInvalidationService"
241-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
242            android:directBootAware="true"
242-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
243            android:exported="false" />
243-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
244
245        <receiver
245-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
246            android:name="androidx.profileinstaller.ProfileInstallReceiver"
246-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
247            android:directBootAware="false"
247-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
248            android:enabled="true"
248-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
249            android:exported="true"
249-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
250            android:permission="android.permission.DUMP" >
250-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
252                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
252-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
252-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
255                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
255-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
255-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
258                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
258-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
258-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
261                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
261-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
261-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
262            </intent-filter>
263        </receiver>
264    </application>
265
266</manifest>
