1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="3"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission
16-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
18        android:maxSdkVersion="32" />
18-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
21        android:maxSdkVersion="32" />
21-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- Media permissions for Android 13+ -->
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:16:5-76
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:16:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-75
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
27
28    <!-- Selected Photos Access for Android 14+ -->
29    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:5-90
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:22-87
30
31    <!-- Vibration permission -->
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:24:5-66
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:24:22-63
33
34    <!-- Additional permissions -->
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:27:5-68
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:27:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-77
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-74
37    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-80
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-77
38
39    <!-- APK installation permission -->
40    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:5-83
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:22-80
41    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-34:47
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-72
42    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
43
44    <permission
44-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:36:5-120:19
51        android:name="com.bearmod.loader.BearLoaderApplication"
51-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:9-46
52        android:allowBackup="true"
52-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-54
58        android:icon="@mipmap/ic_launcher"
58-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-43
59        android:label="@string/app_name"
59-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-41
60        android:networkSecurityConfig="@xml/network_security_config"
60-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-69
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-54
62        android:supportsRtl="true"
62-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-35
63        android:theme="@style/Theme.BearLoader"
63-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-48
64        android:usesCleartextTraffic="true" >
64-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-44
65
66        <!-- Splash Activity -->
67        <activity
67-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:51:9-60:20
68            android:name="com.bearmod.loader.ui.splash.SplashActivity"
68-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:13-53
69            android:exported="true"
69-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-36
70            android:screenOrientation="unspecified"
70-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-52
71            android:theme="@style/Theme.BearLoader.NoActionBar" >
71-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-64
72            <intent-filter>
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:13-59:29
73                <action android:name="android.intent.action.MAIN" />
73-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:17-69
73-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:17-77
75-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:27-74
76            </intent-filter>
77        </activity>
78
79        <!-- Login Activity -->
80        <activity
80-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:63:9-67:58
81            android:name="com.bearmod.loader.ui.auth.LoginActivity"
81-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:64:13-50
82            android:exported="false"
82-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:13-37
83            android:theme="@style/Theme.BearLoader.NoActionBar"
83-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-64
84            android:windowSoftInputMode="adjustResize" />
84-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:67:13-55
85
86        <!-- Tap to Unlock Activity -->
87        <activity
87-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:70:9-73:67
88            android:name="com.bearmod.loader.ui.auth.TapToUnlockActivity"
88-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:71:13-56
89            android:exported="false"
89-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:13-37
90            android:theme="@style/Theme.BearLoader.NoActionBar" />
90-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-64
91
92        <!-- Main Activity -->
93        <activity
93-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:76:9-80:46
94            android:name="com.bearmod.loader.ui.main.MainActivity"
94-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:77:13-49
95            android:exported="false"
95-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:13-37
96            android:launchMode="singleTop"
96-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:80:13-43
97            android:theme="@style/Theme.BearLoader.NoActionBar" />
97-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-64
98
99        <!-- Main Loader Activity -->
100        <activity
100-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:83:9-87:46
101            android:name="com.bearmod.loader.ui.main.MainLoaderActivity"
101-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:84:13-55
102            android:exported="false"
102-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:13-37
103            android:launchMode="singleTop"
103-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:87:13-43
104            android:theme="@style/Theme.BearLoader.NoActionBar" />
104-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-64
105
106        <!-- Patch Execution Activity -->
107        <activity
107-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:90:9-93:67
108            android:name="com.bearmod.loader.ui.patch.PatchExecutionActivity"
108-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:91:13-60
109            android:exported="false"
109-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:92:13-37
110            android:theme="@style/Theme.BearLoader.NoActionBar" />
110-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:93:13-64
111
112        <!-- Download Activity -->
113        <activity
113-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:9-100:55
114            android:name="com.bearmod.loader.ui.download.DownloadActivity"
114-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:97:13-57
115            android:exported="false"
115-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:13-37
116            android:screenOrientation="unspecified"
116-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:100:13-52
117            android:theme="@style/Theme.BearLoader.NoActionBar" />
117-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:99:13-64
118
119        <!-- Settings Activity -->
120        <activity
120-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:9-107:55
121            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
121-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-57
122            android:exported="false"
122-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:13-37
123            android:screenOrientation="unspecified"
123-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:107:13-52
124            android:theme="@style/Theme.BearLoader.NoActionBar" />
124-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:13-64
125
126        <!-- FileProvider for APK installation -->
127        <provider
128            android:name="androidx.core.content.FileProvider"
128-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:111:13-62
129            android:authorities="com.bearmod.loader.fileprovider"
129-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:112:13-64
130            android:exported="false"
130-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:113:13-37
131            android:grantUriPermissions="true" >
131-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:114:13-47
132            <meta-data
132-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:115:13-117:54
133                android:name="android.support.FILE_PROVIDER_PATHS"
133-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:116:17-67
134                android:resource="@xml/file_paths" />
134-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:117:17-51
135        </provider>
136        <provider
136-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
137            android:name="androidx.startup.InitializationProvider"
137-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
138            android:authorities="com.bearmod.loader.androidx-startup"
138-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
139            android:exported="false" >
139-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
140            <meta-data
140-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
141                android:name="androidx.work.WorkManagerInitializer"
141-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
142                android:value="androidx.startup" />
142-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
143            <meta-data
143-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
144                android:name="androidx.emoji2.text.EmojiCompatInitializer"
144-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
145                android:value="androidx.startup" />
145-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
146            <meta-data
146-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
147                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
147-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
148                android:value="androidx.startup" />
148-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
149            <meta-data
149-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
150-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
151                android:value="androidx.startup" />
151-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
152        </provider>
153
154        <service
154-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
155            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
155-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
157            android:enabled="@bool/enable_system_alarm_service_default"
157-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
158            android:exported="false" />
158-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
159        <service
159-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
160            android:name="androidx.work.impl.background.systemjob.SystemJobService"
160-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
161            android:directBootAware="false"
161-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
162            android:enabled="@bool/enable_system_job_service_default"
162-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
163            android:exported="true"
163-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
164            android:permission="android.permission.BIND_JOB_SERVICE" />
164-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
165        <service
165-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
166            android:name="androidx.work.impl.foreground.SystemForegroundService"
166-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
168            android:enabled="@bool/enable_system_foreground_service_default"
168-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
169            android:exported="false" />
169-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
170
171        <receiver
171-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
172            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
172-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
174            android:enabled="true"
174-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
175            android:exported="false" />
175-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
176        <receiver
176-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
177-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
182                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
183                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
183-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
183-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
184            </intent-filter>
185        </receiver>
186        <receiver
186-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
187-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
188            android:directBootAware="false"
188-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
189            android:enabled="false"
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
190            android:exported="false" >
190-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
191            <intent-filter>
191-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
192                <action android:name="android.intent.action.BATTERY_OKAY" />
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
193                <action android:name="android.intent.action.BATTERY_LOW" />
193-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
193-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
194            </intent-filter>
195        </receiver>
196        <receiver
196-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
197            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
197-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
198            android:directBootAware="false"
198-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
199            android:enabled="false"
199-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
200            android:exported="false" >
200-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
201            <intent-filter>
201-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
202                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
203                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
203-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
203-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
204            </intent-filter>
205        </receiver>
206        <receiver
206-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
207            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
207-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
208            android:directBootAware="false"
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
209            android:enabled="false"
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
210            android:exported="false" >
210-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
211            <intent-filter>
211-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
212                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
212-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
212-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
213            </intent-filter>
214        </receiver>
215        <receiver
215-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
216            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
216-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
217            android:directBootAware="false"
217-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
218            android:enabled="false"
218-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
219            android:exported="false" >
219-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
220            <intent-filter>
220-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
221                <action android:name="android.intent.action.BOOT_COMPLETED" />
221-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
221-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
222                <action android:name="android.intent.action.TIME_SET" />
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
223                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
223-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
223-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
224            </intent-filter>
225        </receiver>
226        <receiver
226-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
227            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
227-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
228            android:directBootAware="false"
228-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
229            android:enabled="@bool/enable_system_alarm_service_default"
229-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
230            android:exported="false" >
230-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
231            <intent-filter>
231-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
232                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
232-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
232-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
233            </intent-filter>
234        </receiver>
235        <receiver
235-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
236            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
236-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
237            android:directBootAware="false"
237-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
238            android:enabled="true"
238-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
239            android:exported="true"
239-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
240            android:permission="android.permission.DUMP" >
240-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
241            <intent-filter>
241-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
242                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
242-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
242-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
243            </intent-filter>
244        </receiver>
245
246        <uses-library
246-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
247            android:name="androidx.window.extensions"
247-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
248            android:required="false" />
248-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
249        <uses-library
249-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
250            android:name="androidx.window.sidecar"
250-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
251            android:required="false" />
251-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
252
253        <service
253-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
254            android:name="androidx.room.MultiInstanceInvalidationService"
254-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
255            android:directBootAware="true"
255-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
256            android:exported="false" />
256-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
257
258        <receiver
258-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
259            android:name="androidx.profileinstaller.ProfileInstallReceiver"
259-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
260            android:directBootAware="false"
260-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
261            android:enabled="true"
261-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
262            android:exported="true"
262-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
263            android:permission="android.permission.DUMP" >
263-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
264            <intent-filter>
264-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
265                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
265-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
265-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
266            </intent-filter>
267            <intent-filter>
267-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
268                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
268-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
268-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
269            </intent-filter>
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
271                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
271-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
271-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
272            </intent-filter>
273            <intent-filter>
273-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
274                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
274-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
274-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
275            </intent-filter>
276        </receiver>
277    </application>
278
279</manifest>
