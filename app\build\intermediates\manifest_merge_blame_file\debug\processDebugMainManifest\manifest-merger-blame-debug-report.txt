1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="1"
5    android:versionName="3.8.0" >
6
7    <uses-sdk
8        android:minSdkVersion="30"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission
16-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
18        android:maxSdkVersion="32" />
18-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-14:40
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
21        android:maxSdkVersion="32" />
21-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- All files access permission for Android 11+ (API 30+) -->
24    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-18:40
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-79
25
26    <!-- Media permissions for Android 13+ -->
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:5-76
27-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:5-75
28-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:22-72
29    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:23:5-75
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:23:22-72
30
31    <!-- Selected Photos Access for Android 14+ -->
32    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:26:5-90
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:26:22-87
33
34    <!-- Vibration permission -->
35    <uses-permission android:name="android.permission.VIBRATE" />
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-66
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-63
36
37    <!-- Additional permissions -->
38    <uses-permission android:name="android.permission.WAKE_LOCK" />
38-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:5-68
38-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:22-65
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-77
39-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-74
40    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:5-80
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:22-77
41
42    <!-- APK installation permission -->
43    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
43-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-83
43-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:22-80
44    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
44-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:5-39:47
44-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:22-72
45    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
45-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
45-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
46
47    <permission
47-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
53-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:5-116:19
54        android:name="com.bearmod.loader.BearLoaderApplication"
54-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-46
55        android:allowBackup="true"
55-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-35
56        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
56-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
57        android:dataExtractionRules="@xml/data_extraction_rules"
57-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-65
58        android:debuggable="true"
59        android:extractNativeLibs="false"
60        android:fullBackupContent="@xml/backup_rules"
60-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-54
61        android:icon="@mipmap/ic_launcher"
61-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-43
62        android:label="@string/app_name"
62-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-41
63        android:networkSecurityConfig="@xml/network_security_config"
63-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:9-69
64        android:roundIcon="@mipmap/ic_launcher_round"
64-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:48:9-54
65        android:supportsRtl="true"
65-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:49:9-35
66        android:theme="@style/Theme.BearLoader"
66-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:50:9-48
67        android:usesCleartextTraffic="true" >
67-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:51:9-44
68
69        <!-- Splash Activity -->
70        <activity
70-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:9-66:20
71            android:name="com.bearmod.loader.ui.splash.SplashActivity"
71-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:13-53
72            android:exported="true"
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:13-36
73            android:screenOrientation="unspecified"
73-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:13-52
74            android:theme="@style/Theme.BearLoader.NoActionBar" >
74-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:13-64
75            <intent-filter>
75-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:62:13-65:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:63:17-69
76-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:63:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:64:17-77
78-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:64:27-74
79            </intent-filter>
80        </activity>
81
82        <!-- Login Activity -->
83        <activity
83-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:69:9-73:58
84            android:name="com.bearmod.loader.ui.auth.LoginActivity"
84-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:70:13-50
85            android:exported="false"
85-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:71:13-37
86            android:theme="@style/Theme.BearLoader.NoActionBar"
86-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:13-64
87            android:windowSoftInputMode="adjustResize" />
87-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-55
88
89        <!-- Tap to Unlock Activity -->
90        <activity
90-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:76:9-79:67
91            android:name="com.bearmod.loader.ui.auth.TapToUnlockActivity"
91-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:77:13-56
92            android:exported="false"
92-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:13-37
93            android:theme="@style/Theme.BearLoader.NoActionBar" />
93-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-64
94
95        <!-- Main Loader Activity -->
96        <activity
96-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:82:9-86:46
97            android:name="com.bearmod.loader.ui.main.MainLoaderActivity"
97-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:83:13-55
98            android:exported="false"
98-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:84:13-37
99            android:launchMode="singleTop"
99-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-43
100            android:theme="@style/Theme.BearLoader.NoActionBar" />
100-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:13-64
101
102        <!-- Modern Download Activity -->
103        <activity
103-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:89:9-93:46
104            android:name="com.bearmod.loader.ui.download.ModernDownloadActivity"
104-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:90:13-63
105            android:exported="false"
105-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:91:13-37
106            android:launchMode="singleTop"
106-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:93:13-43
107            android:theme="@style/Theme.BearLoader.NoActionBar" />
107-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:92:13-64
108
109        <!-- Settings Activity -->
110        <activity
110-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:9-103:45
111            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
111-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:99:13-57
112            android:exported="false"
112-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:100:13-37
113            android:screenOrientation="unspecified"
113-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:102:13-52
114            android:theme="@style/Theme.BearLoader.NoActionBar" />
114-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:101:13-64
115
116        <!-- FileProvider for APK installation -->
117        <provider
118            android:name="androidx.core.content.FileProvider"
118-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:107:13-62
119            android:authorities="com.bearmod.loader.fileprovider"
119-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:108:13-64
120            android:exported="false"
120-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:109:13-37
121            android:grantUriPermissions="true" >
121-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:110:13-47
122            <meta-data
122-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:111:13-113:54
123                android:name="android.support.FILE_PROVIDER_PATHS"
123-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:112:17-67
124                android:resource="@xml/file_paths" />
124-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:113:17-51
125        </provider>
126        <provider
126-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
127            android:name="androidx.startup.InitializationProvider"
127-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
128            android:authorities="com.bearmod.loader.androidx-startup"
128-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
129            android:exported="false" >
129-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
130            <meta-data
130-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
131                android:name="androidx.work.WorkManagerInitializer"
131-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
132                android:value="androidx.startup" />
132-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
133            <meta-data
133-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.emoji2.text.EmojiCompatInitializer"
134-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
135                android:value="androidx.startup" />
135-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
137-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
138                android:value="androidx.startup" />
138-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
139            <meta-data
139-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
140                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
140-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
141                android:value="androidx.startup" />
141-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
142        </provider>
143
144        <service
144-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
145            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
145-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
147            android:enabled="@bool/enable_system_alarm_service_default"
147-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
148            android:exported="false" />
148-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
149        <service
149-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
150            android:name="androidx.work.impl.background.systemjob.SystemJobService"
150-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
152            android:enabled="@bool/enable_system_job_service_default"
152-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
153            android:exported="true"
153-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
154            android:permission="android.permission.BIND_JOB_SERVICE" />
154-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
155        <service
155-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
156            android:name="androidx.work.impl.foreground.SystemForegroundService"
156-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
158            android:enabled="@bool/enable_system_foreground_service_default"
158-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
159            android:exported="false" />
159-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
160
161        <receiver
161-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
162            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
162-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
164            android:enabled="true"
164-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
165            android:exported="false" />
165-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
166        <receiver
166-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
167            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
167-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
169            android:enabled="false"
169-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
170            android:exported="false" >
170-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
171            <intent-filter>
171-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
172                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
172-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
172-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
173                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
173-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
173-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
177            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
177-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
182                <action android:name="android.intent.action.BATTERY_OKAY" />
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
183                <action android:name="android.intent.action.BATTERY_LOW" />
183-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
183-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
184            </intent-filter>
185        </receiver>
186        <receiver
186-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
187            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
187-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
188            android:directBootAware="false"
188-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
189            android:enabled="false"
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
190            android:exported="false" >
190-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
191            <intent-filter>
191-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
192                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
193                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
193-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
193-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
194            </intent-filter>
195        </receiver>
196        <receiver
196-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
197            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
197-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
198            android:directBootAware="false"
198-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
199            android:enabled="false"
199-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
200            android:exported="false" >
200-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
201            <intent-filter>
201-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
202                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
203            </intent-filter>
204        </receiver>
205        <receiver
205-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
206            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
206-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
208            android:enabled="false"
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
209            android:exported="false" >
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
210            <intent-filter>
210-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
211                <action android:name="android.intent.action.BOOT_COMPLETED" />
211-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
211-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
212                <action android:name="android.intent.action.TIME_SET" />
212-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
212-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
213                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
213-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
213-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
214            </intent-filter>
215        </receiver>
216        <receiver
216-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
217            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
217-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
218            android:directBootAware="false"
218-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
219            android:enabled="@bool/enable_system_alarm_service_default"
219-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
220            android:exported="false" >
220-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
221            <intent-filter>
221-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
222                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
223            </intent-filter>
224        </receiver>
225        <receiver
225-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
226            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
226-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
228            android:enabled="true"
228-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
229            android:exported="true"
229-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
230            android:permission="android.permission.DUMP" >
230-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
231            <intent-filter>
231-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
232                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
232-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
232-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
233            </intent-filter>
234        </receiver>
235
236        <uses-library
236-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
237            android:name="androidx.window.extensions"
237-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
238            android:required="false" />
238-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
239        <uses-library
239-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
240            android:name="androidx.window.sidecar"
240-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
241            android:required="false" />
241-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
242
243        <service
243-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
244            android:name="androidx.room.MultiInstanceInvalidationService"
244-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
245            android:directBootAware="true"
245-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
246            android:exported="false" />
246-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
247
248        <receiver
248-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
249            android:name="androidx.profileinstaller.ProfileInstallReceiver"
249-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
250            android:directBootAware="false"
250-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
251            android:enabled="true"
251-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
252            android:exported="true"
252-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
253            android:permission="android.permission.DUMP" >
253-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
255                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
255-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
255-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
258                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
258-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
258-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
261                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
261-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
261-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
264                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
264-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
264-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
265            </intent-filter>
266        </receiver>
267    </application>
268
269</manifest>
