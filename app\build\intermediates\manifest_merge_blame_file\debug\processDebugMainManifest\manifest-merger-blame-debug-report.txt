1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="3"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission
16-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
18        android:maxSdkVersion="32" />
18-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-13:38
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
21        android:maxSdkVersion="32" />
21-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- Media permissions for Android 13+ -->
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:16:5-76
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:16:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-75
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
27
28    <!-- Selected Photos Access for Android 14+ -->
29    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:5-90
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:22-87
30
31    <!-- Vibration permission -->
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:24:5-66
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:24:22-63
33
34    <!-- Additional permissions -->
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:27:5-68
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:27:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-77
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-74
37    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-80
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-77
38
39    <!-- APK installation permission -->
40    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:5-83
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:22-80
41    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-34:47
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-72
42    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
43
44    <permission
44-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:36:5-119:19
51        android:name="com.bearmod.loader.BearLoaderApplication"
51-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:9-46
52        android:allowBackup="true"
52-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-54
58        android:icon="@mipmap/ic_launcher"
58-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-43
59        android:label="@string/app_name"
59-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-41
60        android:roundIcon="@mipmap/ic_launcher_round"
60-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-54
61        android:supportsRtl="true"
61-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-35
62        android:theme="@style/Theme.BearLoader"
62-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-48
63        android:usesCleartextTraffic="true" >
63-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-44
64
65        <!-- Splash Activity -->
66        <activity
66-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:50:9-59:20
67            android:name="com.bearmod.loader.ui.splash.SplashActivity"
67-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:51:13-53
68            android:exported="true"
68-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:13-36
69            android:screenOrientation="unspecified"
69-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-52
70            android:theme="@style/Theme.BearLoader.NoActionBar" >
70-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-64
71            <intent-filter>
71-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-58:29
72                <action android:name="android.intent.action.MAIN" />
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:17-69
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:17-77
74-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:27-74
75            </intent-filter>
76        </activity>
77
78        <!-- Login Activity -->
79        <activity
79-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:62:9-66:58
80            android:name="com.bearmod.loader.ui.auth.LoginActivity"
80-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:63:13-50
81            android:exported="false"
81-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:64:13-37
82            android:theme="@style/Theme.BearLoader.NoActionBar"
82-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:13-64
83            android:windowSoftInputMode="adjustResize" />
83-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-55
84
85        <!-- Tap to Unlock Activity -->
86        <activity
86-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:69:9-72:67
87            android:name="com.bearmod.loader.ui.auth.TapToUnlockActivity"
87-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:70:13-56
88            android:exported="false"
88-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:71:13-37
89            android:theme="@style/Theme.BearLoader.NoActionBar" />
89-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:13-64
90
91        <!-- Main Activity -->
92        <activity
92-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:75:9-79:46
93            android:name="com.bearmod.loader.ui.main.MainActivity"
93-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:76:13-49
94            android:exported="false"
94-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:77:13-37
95            android:launchMode="singleTop"
95-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-43
96            android:theme="@style/Theme.BearLoader.NoActionBar" />
96-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:13-64
97
98        <!-- Main Loader Activity -->
99        <activity
99-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:82:9-86:46
100            android:name="com.bearmod.loader.ui.main.MainLoaderActivity"
100-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:83:13-55
101            android:exported="false"
101-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:84:13-37
102            android:launchMode="singleTop"
102-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-43
103            android:theme="@style/Theme.BearLoader.NoActionBar" />
103-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:13-64
104
105        <!-- Patch Execution Activity -->
106        <activity
106-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:89:9-92:67
107            android:name="com.bearmod.loader.ui.patch.PatchExecutionActivity"
107-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:90:13-60
108            android:exported="false"
108-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:91:13-37
109            android:theme="@style/Theme.BearLoader.NoActionBar" />
109-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:92:13-64
110
111        <!-- Download Activity -->
112        <activity
112-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:95:9-99:55
113            android:name="com.bearmod.loader.ui.download.DownloadActivity"
113-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:13-57
114            android:exported="false"
114-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:97:13-37
115            android:screenOrientation="unspecified"
115-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:99:13-52
116            android:theme="@style/Theme.BearLoader.NoActionBar" />
116-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:13-64
117
118        <!-- Settings Activity -->
119        <activity
119-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:102:9-106:55
120            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
120-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:13-57
121            android:exported="false"
121-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-37
122            android:screenOrientation="unspecified"
122-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:13-52
123            android:theme="@style/Theme.BearLoader.NoActionBar" />
123-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:13-64
124
125        <!-- FileProvider for APK installation -->
126        <provider
127            android:name="androidx.core.content.FileProvider"
127-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:110:13-62
128            android:authorities="com.bearmod.loader.fileprovider"
128-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:111:13-64
129            android:exported="false"
129-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:112:13-37
130            android:grantUriPermissions="true" >
130-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:113:13-47
131            <meta-data
131-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:114:13-116:54
132                android:name="android.support.FILE_PROVIDER_PATHS"
132-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:115:17-67
133                android:resource="@xml/file_paths" />
133-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:116:17-51
134        </provider>
135        <provider
135-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
136            android:name="androidx.startup.InitializationProvider"
136-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
137            android:authorities="com.bearmod.loader.androidx-startup"
137-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
138            android:exported="false" >
138-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
139            <meta-data
139-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
140                android:name="androidx.work.WorkManagerInitializer"
140-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
141                android:value="androidx.startup" />
141-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
142            <meta-data
142-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
143                android:name="androidx.emoji2.text.EmojiCompatInitializer"
143-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
144                android:value="androidx.startup" />
144-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
145            <meta-data
145-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
146                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
146-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
147                android:value="androidx.startup" />
147-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
148            <meta-data
148-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
149                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
149-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
150                android:value="androidx.startup" />
150-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
151        </provider>
152
153        <service
153-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
154            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
154-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
156            android:enabled="@bool/enable_system_alarm_service_default"
156-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
157            android:exported="false" />
157-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
158        <service
158-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
159            android:name="androidx.work.impl.background.systemjob.SystemJobService"
159-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
161            android:enabled="@bool/enable_system_job_service_default"
161-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
162            android:exported="true"
162-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
163            android:permission="android.permission.BIND_JOB_SERVICE" />
163-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
164        <service
164-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
165            android:name="androidx.work.impl.foreground.SystemForegroundService"
165-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
167            android:enabled="@bool/enable_system_foreground_service_default"
167-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
168            android:exported="false" />
168-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
169
170        <receiver
170-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
171            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
171-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
173            android:enabled="true"
173-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
174            android:exported="false" />
174-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
175        <receiver
175-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
176            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
176-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
177            android:directBootAware="false"
177-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
178            android:enabled="false"
178-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
179            android:exported="false" >
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
180            <intent-filter>
180-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
181                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
181-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
181-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
182                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
183            </intent-filter>
184        </receiver>
185        <receiver
185-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
186            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
186-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
187            android:directBootAware="false"
187-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
188            android:enabled="false"
188-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
190            <intent-filter>
190-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
191                <action android:name="android.intent.action.BATTERY_OKAY" />
191-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
191-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
192                <action android:name="android.intent.action.BATTERY_LOW" />
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
193            </intent-filter>
194        </receiver>
195        <receiver
195-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
196            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
196-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
197            android:directBootAware="false"
197-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
198            android:enabled="false"
198-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
199            android:exported="false" >
199-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
200            <intent-filter>
200-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
201                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
201-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
201-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
202                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
203            </intent-filter>
204        </receiver>
205        <receiver
205-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
206            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
206-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
207            android:directBootAware="false"
207-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
208            android:enabled="false"
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
209            android:exported="false" >
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
210            <intent-filter>
210-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
211                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
211-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
211-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
212            </intent-filter>
213        </receiver>
214        <receiver
214-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
215            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
215-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
216            android:directBootAware="false"
216-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
217            android:enabled="false"
217-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
218            android:exported="false" >
218-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
219            <intent-filter>
219-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
220                <action android:name="android.intent.action.BOOT_COMPLETED" />
220-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
220-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
221                <action android:name="android.intent.action.TIME_SET" />
221-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
221-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
222                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
223            </intent-filter>
224        </receiver>
225        <receiver
225-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
226            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
226-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
227            android:directBootAware="false"
227-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
228            android:enabled="@bool/enable_system_alarm_service_default"
228-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
229            android:exported="false" >
229-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
230            <intent-filter>
230-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
231                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
231-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
231-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
232            </intent-filter>
233        </receiver>
234        <receiver
234-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
235            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
235-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
236            android:directBootAware="false"
236-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
237            android:enabled="true"
237-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
238            android:exported="true"
238-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
239            android:permission="android.permission.DUMP" >
239-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
240            <intent-filter>
240-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
241                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
241-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
241-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
242            </intent-filter>
243        </receiver>
244
245        <uses-library
245-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
246            android:name="androidx.window.extensions"
246-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
247            android:required="false" />
247-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
248        <uses-library
248-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
249            android:name="androidx.window.sidecar"
249-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
250            android:required="false" />
250-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
251
252        <service
252-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
253            android:name="androidx.room.MultiInstanceInvalidationService"
253-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
254            android:directBootAware="true"
254-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
255            android:exported="false" />
255-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
256
257        <receiver
257-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
258            android:name="androidx.profileinstaller.ProfileInstallReceiver"
258-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
259            android:directBootAware="false"
259-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
260            android:enabled="true"
260-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
261            android:exported="true"
261-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
262            android:permission="android.permission.DUMP" >
262-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
264                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
264-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
264-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
267                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
267-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
267-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
268            </intent-filter>
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
270                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
270-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
270-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
271            </intent-filter>
272            <intent-filter>
272-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
273                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
273-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
273-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
274            </intent-filter>
275        </receiver>
276    </application>
277
278</manifest>
