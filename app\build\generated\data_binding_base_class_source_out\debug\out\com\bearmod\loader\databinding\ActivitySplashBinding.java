// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView appNameText;

  @NonNull
  public final TextView loadingText;

  @NonNull
  public final MaterialCardView logoContainer;

  @NonNull
  public final ImageView logoImage;

  @NonNull
  public final LinearLayout progressContainer;

  @NonNull
  public final ProgressBar progressLoading;

  @NonNull
  public final TextView subtitleText;

  @NonNull
  public final TextView versionText;

  private ActivitySplashBinding(@NonNull ConstraintLayout rootView, @NonNull TextView appNameText,
      @NonNull TextView loadingText, @NonNull MaterialCardView logoContainer,
      @NonNull ImageView logoImage, @NonNull LinearLayout progressContainer,
      @NonNull ProgressBar progressLoading, @NonNull TextView subtitleText,
      @NonNull TextView versionText) {
    this.rootView = rootView;
    this.appNameText = appNameText;
    this.loadingText = loadingText;
    this.logoContainer = logoContainer;
    this.logoImage = logoImage;
    this.progressContainer = progressContainer;
    this.progressLoading = progressLoading;
    this.subtitleText = subtitleText;
    this.versionText = versionText;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appNameText;
      TextView appNameText = ViewBindings.findChildViewById(rootView, id);
      if (appNameText == null) {
        break missingId;
      }

      id = R.id.loadingText;
      TextView loadingText = ViewBindings.findChildViewById(rootView, id);
      if (loadingText == null) {
        break missingId;
      }

      id = R.id.logoContainer;
      MaterialCardView logoContainer = ViewBindings.findChildViewById(rootView, id);
      if (logoContainer == null) {
        break missingId;
      }

      id = R.id.logoImage;
      ImageView logoImage = ViewBindings.findChildViewById(rootView, id);
      if (logoImage == null) {
        break missingId;
      }

      id = R.id.progressContainer;
      LinearLayout progressContainer = ViewBindings.findChildViewById(rootView, id);
      if (progressContainer == null) {
        break missingId;
      }

      id = R.id.progressLoading;
      ProgressBar progressLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressLoading == null) {
        break missingId;
      }

      id = R.id.subtitleText;
      TextView subtitleText = ViewBindings.findChildViewById(rootView, id);
      if (subtitleText == null) {
        break missingId;
      }

      id = R.id.versionText;
      TextView versionText = ViewBindings.findChildViewById(rootView, id);
      if (versionText == null) {
        break missingId;
      }

      return new ActivitySplashBinding((ConstraintLayout) rootView, appNameText, loadingText,
          logoContainer, logoImage, progressContainer, progressLoading, subtitleText, versionText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
