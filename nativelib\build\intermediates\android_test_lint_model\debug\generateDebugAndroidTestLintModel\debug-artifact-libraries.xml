<libraries>
  <library
      name=":@@:nativelib::debug"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c54ebc8070a4ffb7c6c997b81f2f8ec4\transformed\out\jars\classes.jar;D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c54ebc8070a4ffb7c6c997b81f2f8ec4\transformed\out\jars\libs\R.jar"
      resolved="Bear-Loader:nativelib:unspecified"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c54ebc8070a4ffb7c6c997b81f2f8ec4\transformed\out"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.ext:junit:1.2.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\35526896098c8239b2049ef1fe0073ca\transformed\junit-1.2.1\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.2.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\35526896098c8239b2049ef1fe0073ca\transformed\junit-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.6.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bc93ce1e6ae6cb91f9ba53c2c8c470b7\transformed\espresso-core-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.6.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bc93ce1e6ae6cb91f9ba53c2c8c470b7\transformed\espresso-core-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2603d40b3c9c385e35c260b649fe70a8\transformed\constraintlayout-2.0.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2603d40b3c9c385e35c260b649fe70a8\transformed\constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:runner:1.6.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0941add1ebbce72e6a7c9adc5dfc5ca9\transformed\runner-1.6.1\jars\classes.jar"
      resolved="androidx.test:runner:1.6.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0941add1ebbce72e6a7c9adc5dfc5ca9\transformed\runner-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d9957ae432b7de0c7a0bb13b9e2c871f\transformed\storage-1.5.0\jars\classes.jar"
      resolved="androidx.test.services:storage:1.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d9957ae432b7de0c7a0bb13b9e2c871f\transformed\storage-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.6.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\81faab6922861291870baa1af5fe6fda\transformed\core-1.6.1\jars\classes.jar"
      resolved="androidx.test:core:1.6.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\81faab6922861291870baa1af5fe6fda\transformed\core-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.7.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecf4883d18f36ca4b172573ccc39ce8\transformed\monitor-1.7.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.7.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecf4883d18f36ca4b172573ccc39ce8\transformed\monitor-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e792e9729eb8da465948790231713cef\transformed\viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e792e9729eb8da465948790231713cef\transformed\viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.5.4@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f5e7eab56fd52f2053cf5c1a194beef\transformed\fragment-1.5.4\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.5.4"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f5e7eab56fd52f2053cf5c1a194beef\transformed\fragment-1.5.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12efa208c672129cf1d6e83e98f0dfed\transformed\activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12efa208c672129cf1d6e83e98f0dfed\transformed\activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cbdd3edb951e8d0bd746dadd58024d15\transformed\recyclerview-1.1.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cbdd3edb951e8d0bd746dadd58024d15\transformed\recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\773013f03f54f0d0fe2023692ecfa65c\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\773013f03f54f0d0fe2023692ecfa65c\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6e2516a43fb991f82ceb2c9ad22b3733\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6e2516a43fb991f82ceb2c9ad22b3733\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfb8eafbb335b25c27e80717bb712784\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfb8eafbb335b25c27e80717bb712784\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7e69f9e5a15436535f3f863e063145f0\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7e69f9e5a15436535f3f863e063145f0\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d575d5c917413d100cc4872bc5a2dd1\transformed\lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d575d5c917413d100cc4872bc5a2dd1\transformed\lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4b4d356b33ef9e62224532cc5b8de279\transformed\core-ktx-1.13.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4b4d356b33ef9e62224532cc5b8de279\transformed\core-ktx-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\141f6dbfc150b6f4eb438c27266d6ed3\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\141f6dbfc150b6f4eb438c27266d6ed3\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\024a2172cc6ae80aaeb39bb4bfcb877b\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\024a2172cc6ae80aaeb39bb4bfcb877b\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0-beta01@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.7.0-beta01\920472d40adcdef5e18708976b3e314f9a636fcd\annotation-jvm-1.7.0-beta01.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0-beta01"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\844879eef8bb10817f85420738d29084\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\844879eef8bb10817f85420738d29084\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.1\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.1\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.22\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.22\4dabb8248310d833bb6a8b516024a91fd3d275c\kotlin-stdlib-jdk7-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.8.22@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.8.22\636bf8b320e7627482771bbac9ed7246773c02bd\kotlin-stdlib-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.8.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.8.22\1a8e3601703ae14bb58757ea6b2d8e8e5935a586\kotlin-stdlib-common-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ddffd8aaff582cef85112c5c25e09cd\transformed\tracing-1.1.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ddffd8aaff582cef85112c5c25e09cd\transformed\tracing-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.6.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6040b1de5379b049a392c60d1eb3baa5\transformed\espresso-idling-resource-3.6.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.6.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6040b1de5379b049a392c60d1eb3baa5\transformed\espresso-idling-resource-3.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.18.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.18.0\89b684257096f548fa39a7df9fdaa409d4d4df91\error_prone_annotations-2.18.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.18.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-solver\2.0.1\30988fe2d77f3fe3bf7551bb8a8b795fad7e7226\constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\jars\classes.jar;D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1d257034113a68fdddddebfe1948015\transformed\lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1d257034113a68fdddddebfe1948015\transformed\lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd9f20be4202a539e8cbba13100f67b\transformed\profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd9f20be4202a539e8cbba13100f67b\transformed\profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
