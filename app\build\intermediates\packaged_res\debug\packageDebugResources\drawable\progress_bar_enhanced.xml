<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Background -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#404040"
                android:endColor="#505050"
                android:angle="90" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Secondary Progress (if needed) -->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape android:shape="rectangle">
                <gradient
                    android:startColor="#606060"
                    android:endColor="#707070"
                    android:angle="90" />
                <corners android:radius="8dp" />
            </shape>
        </clip>
    </item>
    
    <!-- Primary Progress -->
    <item android:id="@android:id/progress">
        <clip>
            <shape android:shape="rectangle">
                <!-- Beautiful gradient progress -->
                <gradient
                    android:startColor="#00D084"
                    android:centerColor="#58A6FF"
                    android:endColor="#00D084"
                    android:angle="0" />
                <corners android:radius="8dp" />
            </shape>
        </clip>
    </item>
    
</layer-list>
