package com.bearmod.loader.database.dao;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeleteOrUpdateAdapter;
import androidx.room.EntityInsertAdapter;
import androidx.room.RoomDatabase;
import androidx.room.util.DBUtil;
import androidx.room.util.SQLiteStatementUtil;
import androidx.sqlite.SQLiteStatement;
import com.bearmod.loader.database.entity.PatchEntity;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation", "removal"})
public final class PatchDao_Impl implements PatchDao {
  private final RoomDatabase __db;

  private final EntityInsertAdapter<PatchEntity> __insertAdapterOfPatchEntity;

  private final EntityDeleteOrUpdateAdapter<PatchEntity> __deleteAdapterOfPatchEntity;

  private final EntityDeleteOrUpdateAdapter<PatchEntity> __updateAdapterOfPatchEntity;

  public PatchDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertAdapterOfPatchEntity = new EntityInsertAdapter<PatchEntity>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `patches` (`id`,`name`,`description`,`gameVersion`,`updateDate`,`status`,`downloadUrl`,`localPath`,`isInstalled`,`lastUpdated`) VALUES (?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final PatchEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindText(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindText(3, entity.getDescription());
        }
        if (entity.getGameVersion() == null) {
          statement.bindNull(4);
        } else {
          statement.bindText(4, entity.getGameVersion());
        }
        if (entity.getUpdateDate() == null) {
          statement.bindNull(5);
        } else {
          statement.bindText(5, entity.getUpdateDate());
        }
        if (entity.getStatus() == null) {
          statement.bindNull(6);
        } else {
          statement.bindText(6, entity.getStatus());
        }
        if (entity.getDownloadUrl() == null) {
          statement.bindNull(7);
        } else {
          statement.bindText(7, entity.getDownloadUrl());
        }
        if (entity.getLocalPath() == null) {
          statement.bindNull(8);
        } else {
          statement.bindText(8, entity.getLocalPath());
        }
        final int _tmp = entity.isInstalled() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getLastUpdated());
      }
    };
    this.__deleteAdapterOfPatchEntity = new EntityDeleteOrUpdateAdapter<PatchEntity>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `patches` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final PatchEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindText(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfPatchEntity = new EntityDeleteOrUpdateAdapter<PatchEntity>() {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `patches` SET `id` = ?,`name` = ?,`description` = ?,`gameVersion` = ?,`updateDate` = ?,`status` = ?,`downloadUrl` = ?,`localPath` = ?,`isInstalled` = ?,`lastUpdated` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SQLiteStatement statement, final PatchEntity entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindText(1, entity.getId());
        }
        if (entity.getName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindText(2, entity.getName());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindText(3, entity.getDescription());
        }
        if (entity.getGameVersion() == null) {
          statement.bindNull(4);
        } else {
          statement.bindText(4, entity.getGameVersion());
        }
        if (entity.getUpdateDate() == null) {
          statement.bindNull(5);
        } else {
          statement.bindText(5, entity.getUpdateDate());
        }
        if (entity.getStatus() == null) {
          statement.bindNull(6);
        } else {
          statement.bindText(6, entity.getStatus());
        }
        if (entity.getDownloadUrl() == null) {
          statement.bindNull(7);
        } else {
          statement.bindText(7, entity.getDownloadUrl());
        }
        if (entity.getLocalPath() == null) {
          statement.bindNull(8);
        } else {
          statement.bindText(8, entity.getLocalPath());
        }
        final int _tmp = entity.isInstalled() ? 1 : 0;
        statement.bindLong(9, _tmp);
        statement.bindLong(10, entity.getLastUpdated());
        if (entity.getId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindText(11, entity.getId());
        }
      }
    };
  }

  @Override
  public void insert(final PatchEntity patch) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __insertAdapterOfPatchEntity.insert(_connection, patch);
      return null;
    });
  }

  @Override
  public void insertAll(final List<PatchEntity> patches) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __insertAdapterOfPatchEntity.insert(_connection, patches);
      return null;
    });
  }

  @Override
  public void delete(final PatchEntity patch) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __deleteAdapterOfPatchEntity.handle(_connection, patch);
      return null;
    });
  }

  @Override
  public void update(final PatchEntity patch) {
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      __updateAdapterOfPatchEntity.handle(_connection, patch);
      return null;
    });
  }

  @Override
  public List<PatchEntity> getAllPatches() {
    final String _sql = "SELECT * FROM patches ORDER BY name ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public LiveData<List<PatchEntity>> getAllPatchesLive() {
    final String _sql = "SELECT * FROM patches ORDER BY name ASC";
    return __db.getInvalidationTracker().createLiveData(new String[] {"patches"}, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public PatchEntity getPatchById(final String id) {
    final String _sql = "SELECT * FROM patches WHERE id = ?";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, id);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final PatchEntity _result;
        if (_stmt.step()) {
          _result = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _result.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _result.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _result.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _result.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _result.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _result.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _result.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _result.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _result.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _result.setLastUpdated(_tmpLastUpdated);
        } else {
          _result = null;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public LiveData<PatchEntity> getPatchByIdLive(final String id) {
    final String _sql = "SELECT * FROM patches WHERE id = ?";
    return __db.getInvalidationTracker().createLiveData(new String[] {"patches"}, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, id);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final PatchEntity _result;
        if (_stmt.step()) {
          _result = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _result.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _result.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _result.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _result.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _result.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _result.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _result.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _result.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _result.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _result.setLastUpdated(_tmpLastUpdated);
        } else {
          _result = null;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<PatchEntity> getPatchesByGameVersion(final String gameVersion) {
    final String _sql = "SELECT * FROM patches WHERE gameVersion = ? ORDER BY name ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (gameVersion == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, gameVersion);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public LiveData<List<PatchEntity>> getPatchesByGameVersionLive(final String gameVersion) {
    final String _sql = "SELECT * FROM patches WHERE gameVersion = ? ORDER BY name ASC";
    return __db.getInvalidationTracker().createLiveData(new String[] {"patches"}, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (gameVersion == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, gameVersion);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<PatchEntity> getInstalledPatches() {
    final String _sql = "SELECT * FROM patches WHERE isInstalled = 1 ORDER BY name ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public LiveData<List<PatchEntity>> getInstalledPatchesLive() {
    final String _sql = "SELECT * FROM patches WHERE isInstalled = 1 ORDER BY name ASC";
    return __db.getInvalidationTracker().createLiveData(new String[] {"patches"}, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<PatchEntity> getPatchesByStatus(final String status) {
    final String _sql = "SELECT * FROM patches WHERE status = ? ORDER BY name ASC";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (status == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, status);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public LiveData<List<PatchEntity>> getPatchesByStatusLive(final String status) {
    final String _sql = "SELECT * FROM patches WHERE status = ? ORDER BY name ASC";
    return __db.getInvalidationTracker().createLiveData(new String[] {"patches"}, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (status == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, status);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public List<PatchEntity> getLatestPatches(final String gameVersion) {
    final String _sql = "SELECT * FROM patches WHERE gameVersion = ? ORDER BY lastUpdated DESC LIMIT 10";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        int _argIndex = 1;
        if (gameVersion == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindText(_argIndex, gameVersion);
        }
        final int _columnIndexOfId = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "id");
        final int _columnIndexOfName = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "name");
        final int _columnIndexOfDescription = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "description");
        final int _columnIndexOfGameVersion = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "gameVersion");
        final int _columnIndexOfUpdateDate = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "updateDate");
        final int _columnIndexOfStatus = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "status");
        final int _columnIndexOfDownloadUrl = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "downloadUrl");
        final int _columnIndexOfLocalPath = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "localPath");
        final int _columnIndexOfIsInstalled = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "isInstalled");
        final int _columnIndexOfLastUpdated = SQLiteStatementUtil.getColumnIndexOrThrow(_stmt, "lastUpdated");
        final List<PatchEntity> _result = new ArrayList<PatchEntity>();
        while (_stmt.step()) {
          final PatchEntity _item;
          _item = new PatchEntity();
          final String _tmpId;
          if (_stmt.isNull(_columnIndexOfId)) {
            _tmpId = null;
          } else {
            _tmpId = _stmt.getText(_columnIndexOfId);
          }
          _item.setId(_tmpId);
          final String _tmpName;
          if (_stmt.isNull(_columnIndexOfName)) {
            _tmpName = null;
          } else {
            _tmpName = _stmt.getText(_columnIndexOfName);
          }
          _item.setName(_tmpName);
          final String _tmpDescription;
          if (_stmt.isNull(_columnIndexOfDescription)) {
            _tmpDescription = null;
          } else {
            _tmpDescription = _stmt.getText(_columnIndexOfDescription);
          }
          _item.setDescription(_tmpDescription);
          final String _tmpGameVersion;
          if (_stmt.isNull(_columnIndexOfGameVersion)) {
            _tmpGameVersion = null;
          } else {
            _tmpGameVersion = _stmt.getText(_columnIndexOfGameVersion);
          }
          _item.setGameVersion(_tmpGameVersion);
          final String _tmpUpdateDate;
          if (_stmt.isNull(_columnIndexOfUpdateDate)) {
            _tmpUpdateDate = null;
          } else {
            _tmpUpdateDate = _stmt.getText(_columnIndexOfUpdateDate);
          }
          _item.setUpdateDate(_tmpUpdateDate);
          final String _tmpStatus;
          if (_stmt.isNull(_columnIndexOfStatus)) {
            _tmpStatus = null;
          } else {
            _tmpStatus = _stmt.getText(_columnIndexOfStatus);
          }
          _item.setStatus(_tmpStatus);
          final String _tmpDownloadUrl;
          if (_stmt.isNull(_columnIndexOfDownloadUrl)) {
            _tmpDownloadUrl = null;
          } else {
            _tmpDownloadUrl = _stmt.getText(_columnIndexOfDownloadUrl);
          }
          _item.setDownloadUrl(_tmpDownloadUrl);
          final String _tmpLocalPath;
          if (_stmt.isNull(_columnIndexOfLocalPath)) {
            _tmpLocalPath = null;
          } else {
            _tmpLocalPath = _stmt.getText(_columnIndexOfLocalPath);
          }
          _item.setLocalPath(_tmpLocalPath);
          final boolean _tmpIsInstalled;
          final int _tmp;
          _tmp = (int) (_stmt.getLong(_columnIndexOfIsInstalled));
          _tmpIsInstalled = _tmp != 0;
          _item.setInstalled(_tmpIsInstalled);
          final long _tmpLastUpdated;
          _tmpLastUpdated = _stmt.getLong(_columnIndexOfLastUpdated);
          _item.setLastUpdated(_tmpLastUpdated);
          _result.add(_item);
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public int count() {
    final String _sql = "SELECT COUNT(*) FROM patches";
    return DBUtil.performBlocking(__db, true, false, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        final int _result;
        if (_stmt.step()) {
          _result = (int) (_stmt.getLong(0));
        } else {
          _result = 0;
        }
        return _result;
      } finally {
        _stmt.close();
      }
    });
  }

  @Override
  public void deleteAll() {
    final String _sql = "DELETE FROM patches";
    DBUtil.performBlocking(__db, false, true, (_connection) -> {
      final SQLiteStatement _stmt = _connection.prepare(_sql);
      try {
        _stmt.step();
        return null;
      } finally {
        _stmt.close();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
