// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.checkbox.MaterialCheckBox;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLoginBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextView appNameText;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final MaterialCheckBox checkboxAutoLogin;

  @NonNull
  public final MaterialCheckBox checkboxRemember;

  @NonNull
  public final TextInputEditText editLicenseKey;

  @NonNull
  public final MaterialCardView logoContainer;

  @NonNull
  public final ImageView logoImage;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextInputLayout textInputLayout;

  @NonNull
  public final TextView versionText;

  private ActivityLoginBinding(@NonNull CoordinatorLayout rootView, @NonNull TextView appNameText,
      @NonNull MaterialButton btnLogin, @NonNull MaterialCheckBox checkboxAutoLogin,
      @NonNull MaterialCheckBox checkboxRemember, @NonNull TextInputEditText editLicenseKey,
      @NonNull MaterialCardView logoContainer, @NonNull ImageView logoImage,
      @NonNull ProgressBar progressBar, @NonNull TextInputLayout textInputLayout,
      @NonNull TextView versionText) {
    this.rootView = rootView;
    this.appNameText = appNameText;
    this.btnLogin = btnLogin;
    this.checkboxAutoLogin = checkboxAutoLogin;
    this.checkboxRemember = checkboxRemember;
    this.editLicenseKey = editLicenseKey;
    this.logoContainer = logoContainer;
    this.logoImage = logoImage;
    this.progressBar = progressBar;
    this.textInputLayout = textInputLayout;
    this.versionText = versionText;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appNameText;
      TextView appNameText = ViewBindings.findChildViewById(rootView, id);
      if (appNameText == null) {
        break missingId;
      }

      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.checkboxAutoLogin;
      MaterialCheckBox checkboxAutoLogin = ViewBindings.findChildViewById(rootView, id);
      if (checkboxAutoLogin == null) {
        break missingId;
      }

      id = R.id.checkboxRemember;
      MaterialCheckBox checkboxRemember = ViewBindings.findChildViewById(rootView, id);
      if (checkboxRemember == null) {
        break missingId;
      }

      id = R.id.editLicenseKey;
      TextInputEditText editLicenseKey = ViewBindings.findChildViewById(rootView, id);
      if (editLicenseKey == null) {
        break missingId;
      }

      id = R.id.logoContainer;
      MaterialCardView logoContainer = ViewBindings.findChildViewById(rootView, id);
      if (logoContainer == null) {
        break missingId;
      }

      id = R.id.logoImage;
      ImageView logoImage = ViewBindings.findChildViewById(rootView, id);
      if (logoImage == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.textInputLayout;
      TextInputLayout textInputLayout = ViewBindings.findChildViewById(rootView, id);
      if (textInputLayout == null) {
        break missingId;
      }

      id = R.id.versionText;
      TextView versionText = ViewBindings.findChildViewById(rootView, id);
      if (versionText == null) {
        break missingId;
      }

      return new ActivityLoginBinding((CoordinatorLayout) rootView, appNameText, btnLogin,
          checkboxAutoLogin, checkboxRemember, editLicenseKey, logoContainer, logoImage,
          progressBar, textInputLayout, versionText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
