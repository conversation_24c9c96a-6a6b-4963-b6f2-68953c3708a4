package com.bearmod.targetapp;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.util.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Enhanced signature verifier with BearTrust integration
 * Now uses native verification for better security against bypasses
 * Integrated into nativelib module for shared use
 */
public class SignatureVerifier {
    private static final String TAG = "SignatureVerifier";

    /**
     * Verifies if the application signature is valid
     * Now uses BearTrust native verification for enhanced security
     * 
     * @param context Application context
     * @return true if signature is valid, false otherwise
     */
    public static boolean isSignatureValid(Context context) {
        // Use BearTrust native verification instead of Java-based verification
        // This provides better protection against Frida bypasses
        return BearTrust.nativeVerify(context);
    }
    
    /**
     * Enhanced signature verification with detailed result
     * 
     * @param context Application context
     * @return VerificationResult with detailed information
     */
    public static VerificationResult verifySignatureDetailed(Context context) {
        try {
            // Initialize BearTrust if not already done
            if (!BearTrust.isInitialized()) {
                BearTrust.initialize(context);
            }
            
            // Perform detailed verification
            BearTrust.VerificationResult result = BearTrust.performDetailedVerification(context);
            
            return new VerificationResult(
                result.isVerified,
                result.resultCode,
                result.message,
                result.details
            );
            
        } catch (Exception e) {
            Log.e(TAG, "Detailed signature verification failed", e);
            return new VerificationResult(false, "ERROR", 
                "Verification error: " + e.getMessage(), null);
        }
    }
    
    /**
     * Check if app was launched by trusted BearMod Loader
     * This is the main function to call for authentication
     * 
     * @param context Application context
     * @return true if launched by trusted loader, false otherwise
     */
    public static boolean isLaunchedByTrustedLoader(Context context) {
        try {
            // Initialize BearTrust
            BearTrust.initialize(context);
            
            // Perform verification
            boolean result = BearTrust.nativeVerify(context);
            
            if (result) {
                Log.d(TAG, "App launched by trusted BearMod Loader - authentication successful");
            } else {
                Log.w(TAG, "App not launched by trusted BearMod Loader - fallback authentication required");
            }
            
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "Trusted loader verification failed", e);
            return false;
        }
    }
    
    /**
     * Comprehensive authentication check
     * Combines BearTrust verification with fallback options
     * 
     * @param context Application context
     * @return AuthenticationResult with method used and success status
     */
    public static AuthenticationResult performAuthentication(Context context) {
        Log.d(TAG, "Performing comprehensive authentication");
        
        try {
            // 1. Try BearTrust native verification first
            if (BearTrust.isNativeLibraryAvailable()) {
                BearTrust.initialize(context);
                
                if (BearTrust.nativeVerify(context)) {
                    return new AuthenticationResult(true, "BEAR_TRUST", 
                        "Authenticated via BearTrust native verification", 
                        "App launched by trusted BearMod Loader");
                } else {
                    Log.d(TAG, "BearTrust verification failed - trying fallback methods");
                }
            } else {
                Log.w(TAG, "BearTrust native library not available - using fallback");
            }
            
            // 2. Fallback to legacy signature verification
            if (isSignatureValidLegacy(context)) {
                return new AuthenticationResult(true, "LEGACY", 
                    "Authenticated via legacy signature verification", 
                    "Basic signature verification passed");
            }
            
            // 3. All methods failed
            return new AuthenticationResult(false, "FAILED", 
                "All authentication methods failed", 
                "Neither BearTrust nor legacy verification succeeded");
            
        } catch (Exception e) {
            Log.e(TAG, "Authentication error", e);
            return new AuthenticationResult(false, "ERROR", 
                "Authentication error: " + e.getMessage(), null);
        }
    }
    
    /**
     * Legacy signature verification (kept for fallback)
     * Only use this if BearTrust native verification is not available
     * 
     * @param context Application context
     * @return true if signature is valid, false otherwise
     */
    public static boolean isSignatureValidLegacy(Context context) {
        try {
            Log.w(TAG, "Using legacy signature verification - consider upgrading to BearTrust");
            
            // Get package info with signatures
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            // Check if signatures exist
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                Log.e(TAG, "No signatures found");
                return false;
            }
            
            // Get the first signature
            Signature signature = packageInfo.signatures[0];
            
            // Get signature hash
            String signatureHash = getSignatureHash(signature);
            Log.d(TAG, "Legacy signature hash: " + signatureHash);
            
            // In a real app, you would compare this hash with a hardcoded expected hash
            // For this example, we'll just check if the signature exists
            return signature != null && signature.toByteArray().length > 0;
            
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found", e);
            return false;
        }
    }
    
    /**
     * Gets the SHA-256 hash of the signature
     * 
     * @param signature Application signature
     * @return SHA-256 hash of the signature
     */
    public static String getSignatureHash(Signature signature) {
        try {
            // Get signature bytes
            byte[] signatureBytes = signature.toByteArray();
            
            // Create SHA-256 digest
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] digest = md.digest(signatureBytes);
            
            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "SHA-256 algorithm not found", e);
            return "";
        }
    }
    
    /**
     * Gets the signature as a hex string
     * 
     * @param context Application context
     * @return Signature as hex string
     */
    public static String getSignatureHex(Context context) {
        try {
            // Try BearTrust first
            if (BearTrust.isNativeLibraryAvailable()) {
                String hash = BearTrust.getSignatureHash(context, context.getPackageName());
                if (hash != null) {
                    return hash;
                }
            }
            
            // Fallback to legacy method
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            // Check if signatures exist
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                return "No signatures found";
            }
            
            // Get the first signature
            Signature signature = packageInfo.signatures[0];
            byte[] signatureBytes = signature.toByteArray();
            
            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : signatureBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found", e);
            return "Error: " + e.getMessage();
        }
    }
    
    /**
     * Log signature information for debugging
     * 
     * @param context Application context
     */
    public static void logSignatureInformation(Context context) {
        Log.d(TAG, "=== Signature Information ===");
        
        // Use BearTrust if available
        if (BearTrust.isNativeLibraryAvailable()) {
            BearTrust.logSignatureInformation(context);
        } else {
            // Fallback to legacy logging
            String signatureHex = getSignatureHex(context);
            Log.d(TAG, "Legacy signature hex: " + signatureHex);
        }
        
        Log.d(TAG, "=== End Signature Information ===");
    }
    
    /**
     * Verification result class
     */
    public static class VerificationResult {
        public final boolean isValid;
        public final String resultCode;
        public final String message;
        public final String details;
        
        public VerificationResult(boolean isValid, String resultCode, String message, String details) {
            this.isValid = isValid;
            this.resultCode = resultCode;
            this.message = message;
            this.details = details;
        }
        
        @Override
        public String toString() {
            return "VerificationResult{isValid=" + isValid + ", resultCode='" + resultCode + "'}";
        }
    }
    
    /**
     * Authentication result class
     */
    public static class AuthenticationResult {
        public final boolean isAuthenticated;
        public final String method;
        public final String message;
        public final String details;
        
        public AuthenticationResult(boolean isAuthenticated, String method, String message, String details) {
            this.isAuthenticated = isAuthenticated;
            this.method = method;
            this.message = message;
            this.details = details;
        }
        
        public boolean isTrustedLaunch() {
            return isAuthenticated && "BEAR_TRUST".equals(method);
        }
        
        @Override
        public String toString() {
            return "AuthenticationResult{isAuthenticated=" + isAuthenticated + ", method='" + method + "'}";
        }
    }
}
