[{"level_": 0, "message_": "Start JSON generation. Platform version: 30 min SDK version: arm64-v8a", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\Augment_Code\\BearMod-Loader\\app\\build\\.cxx\\Debug\\2129i685\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\Augment_Code\\BearMod-Loader\\app\\build\\.cxx\\Debug\\2129i685\\arm64-v8a'", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\Augment_Code\\BearMod-Loader\\app\\build\\.cxx\\Debug\\2129i685\\arm64-v8a'", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing ndkBuild @echo off\n\"D:\\\\AndroidBuildEnv\\\\SDK\\\\ndk\\\\27.1.12297006\\\\ndk-build.cmd\" ^\n  \"NDK_PROJECT_PATH=null\" ^\n  \"APP_BUILD_SCRIPT=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\src\\\\main\\\\jni\\\\Android.mk\" ^\n  \"NDK_APPLICATION_MK=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\src\\\\main\\\\jni\\\\Application.mk\" ^\n  \"APP_ABI=arm64-v8a\" ^\n  \"NDK_ALL_ABIS=arm64-v8a\" ^\n  \"NDK_DEBUG=1\" ^\n  \"APP_PLATFORM=android-30\" ^\n  \"NDK_OUT=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2129i685/obj\" ^\n  \"NDK_LIBS_OUT=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2129i685/lib\" ^\n  \"APP_SHORT_COMMANDS=false\" ^\n  \"LOCAL_SHORT_COMMANDS=false\" ^\n  -B ^\n  -n\n", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"D:\\\\AndroidBuildEnv\\\\SDK\\\\ndk\\\\27.1.12297006\\\\ndk-build.cmd\" ^\n  \"NDK_PROJECT_PATH=null\" ^\n  \"APP_BUILD_SCRIPT=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\src\\\\main\\\\jni\\\\Android.mk\" ^\n  \"NDK_APPLICATION_MK=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\src\\\\main\\\\jni\\\\Application.mk\" ^\n  \"APP_ABI=arm64-v8a\" ^\n  \"NDK_ALL_ABIS=arm64-v8a\" ^\n  \"NDK_DEBUG=1\" ^\n  \"APP_PLATFORM=android-30\" ^\n  \"NDK_OUT=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2129i685/obj\" ^\n  \"NDK_LIBS_OUT=D:\\\\Augment_Code\\\\BearMod-Loader\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2129i685/lib\" ^\n  \"APP_SHORT_COMMANDS=false\" ^\n  \"LOCAL_SHORT_COMMANDS=false\" ^\n  -B ^\n  -n\n", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "parse and convert ndk-build output to build configuration JSON", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "found application make file D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Application.mk", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing ndkBuild", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]