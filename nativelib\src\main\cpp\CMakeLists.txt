# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html.
# For more examples on how to use CMake, see https://github.com/android/ndk-samples.

# Sets the minimum CMake version required for this project.
cmake_minimum_required(VERSION 3.22.1)

project("nativelib")


add_library(${CMAKE_PROJECT_NAME} SHARED
        # List C/C++ source files with relative paths to this CMakeLists.txt.
        nativelib.cpp
        bear_trust_verifier.cpp
        bear_loader.cpp
        memory_patcher.cpp
        jni_bridge.cpp
        anti_debug.cpp
        utils/string_obfuscator.cpp
        utils/memory_utils.cpp)

# Find OpenSSL for signature verification
find_library(crypto-lib crypto)
find_library(ssl-lib ssl)

target_link_libraries(${CMAKE_PROJECT_NAME}
        # List libraries link to the target library
        android
        log
        ${crypto-lib}
        ${ssl-lib})