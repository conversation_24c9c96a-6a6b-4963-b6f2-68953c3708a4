// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.facebook.shimmer.ShimmerFrameLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.navigation.NavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final Button btnScanOffsets;

  @NonNull
  public final MaterialCardView cardTargetApp;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final LinearLayout layoutEmpty;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final RecyclerView rvPatches;

  @NonNull
  public final ShimmerFrameLayout shimmerLayout;

  @NonNull
  public final Spinner spinnerTargetApp;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAvailablePatches;

  private ActivityMainBinding(@NonNull DrawerLayout rootView,
      @NonNull BottomNavigationView bottomNavigation, @NonNull Button btnScanOffsets,
      @NonNull MaterialCardView cardTargetApp, @NonNull DrawerLayout drawerLayout,
      @NonNull LinearLayout layoutEmpty, @NonNull NavigationView navView,
      @NonNull RecyclerView rvPatches, @NonNull ShimmerFrameLayout shimmerLayout,
      @NonNull Spinner spinnerTargetApp, @NonNull SwipeRefreshLayout swipeRefresh,
      @NonNull Toolbar toolbar, @NonNull TextView tvAvailablePatches) {
    this.rootView = rootView;
    this.bottomNavigation = bottomNavigation;
    this.btnScanOffsets = btnScanOffsets;
    this.cardTargetApp = cardTargetApp;
    this.drawerLayout = drawerLayout;
    this.layoutEmpty = layoutEmpty;
    this.navView = navView;
    this.rvPatches = rvPatches;
    this.shimmerLayout = shimmerLayout;
    this.spinnerTargetApp = spinnerTargetApp;
    this.swipeRefresh = swipeRefresh;
    this.toolbar = toolbar;
    this.tvAvailablePatches = tvAvailablePatches;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.bottom_navigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btn_scan_offsets;
      Button btnScanOffsets = ViewBindings.findChildViewById(rootView, id);
      if (btnScanOffsets == null) {
        break missingId;
      }

      id = R.id.card_target_app;
      MaterialCardView cardTargetApp = ViewBindings.findChildViewById(rootView, id);
      if (cardTargetApp == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.layout_empty;
      LinearLayout layoutEmpty = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmpty == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.rv_patches;
      RecyclerView rvPatches = ViewBindings.findChildViewById(rootView, id);
      if (rvPatches == null) {
        break missingId;
      }

      id = R.id.shimmer_layout;
      ShimmerFrameLayout shimmerLayout = ViewBindings.findChildViewById(rootView, id);
      if (shimmerLayout == null) {
        break missingId;
      }

      id = R.id.spinner_target_app;
      Spinner spinnerTargetApp = ViewBindings.findChildViewById(rootView, id);
      if (spinnerTargetApp == null) {
        break missingId;
      }

      id = R.id.swipe_refresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_available_patches;
      TextView tvAvailablePatches = ViewBindings.findChildViewById(rootView, id);
      if (tvAvailablePatches == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, bottomNavigation, btnScanOffsets,
          cardTargetApp, drawerLayout, layoutEmpty, navView, rvPatches, shimmerLayout,
          spinnerTargetApp, swipeRefresh, toolbar, tvAvailablePatches);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
