1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bearmod.loader"
4    android:versionCode="3"
5    android:versionName="1.3" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- Internet permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Storage permissions -->
16    <uses-permission
16-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
18        android:maxSdkVersion="32" />
18-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
19    <uses-permission
19-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-14:40
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
21        android:maxSdkVersion="32" />
21-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
22
23    <!-- Media permissions for Android 13+ -->
24    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-76
24-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-73
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
25-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
26    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:5-75
26-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:22-72
27
28    <!-- Selected Photos Access for Android 14+ -->
29    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:5-90
29-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:22-87
30
31    <!-- Vibration permission -->
32    <uses-permission android:name="android.permission.VIBRATE" />
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:5-66
32-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:22-63
33
34    <!-- Additional permissions -->
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-68
35-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-65
36    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-77
36-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-74
37    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:5-80
37-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:22-77
38
39    <!-- APK installation permission -->
40    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-83
40-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-80
41    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:5-35:47
41-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:22-72
42    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
42-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
43
44    <permission
44-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-112:19
51        android:name="com.bearmod.loader.BearLoaderApplication"
51-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-46
52        android:allowBackup="true"
52-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-65
55        android:extractNativeLibs="false"
56        android:fullBackupContent="@xml/backup_rules"
56-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-54
57        android:icon="@mipmap/ic_launcher"
57-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-43
58        android:label="@string/app_name"
58-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-41
59        android:networkSecurityConfig="@xml/network_security_config"
59-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:48:9-69
60        android:roundIcon="@mipmap/ic_launcher_round"
60-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-54
61        android:supportsRtl="true"
61-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-35
62        android:theme="@style/Theme.BearLoader"
62-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-48
63        android:usesCleartextTraffic="true" >
63-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-44
64
65        <!-- Splash Activity -->
66        <activity
66-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:9-62:20
67            android:name="com.bearmod.loader.ui.splash.SplashActivity"
67-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-53
68            android:exported="true"
68-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-36
69            android:screenOrientation="unspecified"
69-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:13-52
70            android:theme="@style/Theme.BearLoader.NoActionBar" >
70-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-64
71            <intent-filter>
71-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:13-61:29
72                <action android:name="android.intent.action.MAIN" />
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:17-69
72-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:25-66
73
74                <category android:name="android.intent.category.LAUNCHER" />
74-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:17-77
74-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:27-74
75            </intent-filter>
76        </activity>
77
78        <!-- Login Activity -->
79        <activity
79-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:9-69:58
80            android:name="com.bearmod.loader.ui.auth.LoginActivity"
80-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-50
81            android:exported="false"
81-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:67:13-37
82            android:theme="@style/Theme.BearLoader.NoActionBar"
82-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:68:13-64
83            android:windowSoftInputMode="adjustResize" />
83-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:69:13-55
84
85        <!-- Tap to Unlock Activity -->
86        <activity
86-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:9-75:67
87            android:name="com.bearmod.loader.ui.auth.TapToUnlockActivity"
87-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-56
88            android:exported="false"
88-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:74:13-37
89            android:theme="@style/Theme.BearLoader.NoActionBar" />
89-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:75:13-64
90
91        <!-- Main Loader Activity -->
92        <activity
92-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:9-82:46
93            android:name="com.bearmod.loader.ui.main.MainLoaderActivity"
93-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-55
94            android:exported="false"
94-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:80:13-37
95            android:launchMode="singleTop"
95-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:82:13-43
96            android:theme="@style/Theme.BearLoader.NoActionBar" />
96-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:81:13-64
97
98        <!-- Modern Download Activity -->
99        <activity
99-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:9-89:46
100            android:name="com.bearmod.loader.ui.download.ModernDownloadActivity"
100-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-63
101            android:exported="false"
101-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:87:13-37
102            android:launchMode="singleTop"
102-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:89:13-43
103            android:theme="@style/Theme.BearLoader.NoActionBar" />
103-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:88:13-64
104
105        <!-- Settings Activity -->
106        <activity
106-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:94:9-99:45
107            android:name="com.bearmod.loader.ui.settings.SettingsActivity"
107-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:95:13-57
108            android:exported="false"
108-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:13-37
109            android:screenOrientation="unspecified"
109-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:13-52
110            android:theme="@style/Theme.BearLoader.NoActionBar" />
110-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:97:13-64
111
112        <!-- FileProvider for APK installation -->
113        <provider
114            android:name="androidx.core.content.FileProvider"
114-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:13-62
115            android:authorities="com.bearmod.loader.fileprovider"
115-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-64
116            android:exported="false"
116-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:13-37
117            android:grantUriPermissions="true" >
117-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:13-47
118            <meta-data
118-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:107:13-109:54
119                android:name="android.support.FILE_PROVIDER_PATHS"
119-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:108:17-67
120                android:resource="@xml/file_paths" />
120-->D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:109:17-51
121        </provider>
122        <provider
122-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
124            android:authorities="com.bearmod.loader.androidx-startup"
124-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
125            android:exported="false" >
125-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
126            <meta-data
126-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
127                android:name="androidx.work.WorkManagerInitializer"
127-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
128                android:value="androidx.startup" />
128-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
129            <meta-data
129-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.emoji2.text.EmojiCompatInitializer"
130-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
131                android:value="androidx.startup" />
131-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <service
140-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
141            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
141-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
142            android:directBootAware="false"
142-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
143            android:enabled="@bool/enable_system_alarm_service_default"
143-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
144            android:exported="false" />
144-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
145        <service
145-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
146            android:name="androidx.work.impl.background.systemjob.SystemJobService"
146-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
147            android:directBootAware="false"
147-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
148            android:enabled="@bool/enable_system_job_service_default"
148-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
149            android:exported="true"
149-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
150            android:permission="android.permission.BIND_JOB_SERVICE" />
150-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
151        <service
151-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
152            android:name="androidx.work.impl.foreground.SystemForegroundService"
152-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
153            android:directBootAware="false"
153-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
154            android:enabled="@bool/enable_system_foreground_service_default"
154-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
155            android:exported="false" />
155-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
156
157        <receiver
157-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
158            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
158-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
159            android:directBootAware="false"
159-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
160            android:enabled="true"
160-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
161            android:exported="false" />
161-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
162        <receiver
162-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
163            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
163-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
164            android:directBootAware="false"
164-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
165            android:enabled="false"
165-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
166            android:exported="false" >
166-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
167            <intent-filter>
167-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
168                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
168-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
168-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
169                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
169-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
169-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
173-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
178                <action android:name="android.intent.action.BATTERY_OKAY" />
178-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
178-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
179                <action android:name="android.intent.action.BATTERY_LOW" />
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
179-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
180            </intent-filter>
181        </receiver>
182        <receiver
182-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
183            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
183-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
185            android:enabled="false"
185-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
186            android:exported="false" >
186-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
187            <intent-filter>
187-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
188                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
188-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
188-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
189                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
189-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
193            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
193-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
195            android:enabled="false"
195-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
198                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
198-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
198-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
199            </intent-filter>
200        </receiver>
201        <receiver
201-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
202            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
202-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
204            android:enabled="false"
204-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
205            android:exported="false" >
205-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
206            <intent-filter>
206-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
207                <action android:name="android.intent.action.BOOT_COMPLETED" />
207-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
207-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
208                <action android:name="android.intent.action.TIME_SET" />
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
208-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
209                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
209-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
213            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
213-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
215            android:enabled="@bool/enable_system_alarm_service_default"
215-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
216            android:exported="false" >
216-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
217            <intent-filter>
217-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
218                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
218-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
218-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
219            </intent-filter>
220        </receiver>
221        <receiver
221-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
222            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
222-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
223            android:directBootAware="false"
223-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
224            android:enabled="true"
224-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
225            android:exported="true"
225-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
226            android:permission="android.permission.DUMP" >
226-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
227            <intent-filter>
227-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
228                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
228-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
228-->[androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
229            </intent-filter>
230        </receiver>
231
232        <uses-library
232-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
233            android:name="androidx.window.extensions"
233-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
234            android:required="false" />
234-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
235        <uses-library
235-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
236            android:name="androidx.window.sidecar"
236-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
237            android:required="false" />
237-->[androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
238
239        <service
239-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
240            android:name="androidx.room.MultiInstanceInvalidationService"
240-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
241            android:directBootAware="true"
241-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
242            android:exported="false" />
242-->[androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
243
244        <receiver
244-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
245            android:name="androidx.profileinstaller.ProfileInstallReceiver"
245-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
246            android:directBootAware="false"
246-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
247            android:enabled="true"
247-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
248            android:exported="true"
248-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
249            android:permission="android.permission.DUMP" >
249-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
251                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
251-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
251-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
254                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
254-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
254-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
257                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
257-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
257-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
258            </intent-filter>
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
260                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
260-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
260-->[androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
261            </intent-filter>
262        </receiver>
263    </application>
264
265</manifest>
