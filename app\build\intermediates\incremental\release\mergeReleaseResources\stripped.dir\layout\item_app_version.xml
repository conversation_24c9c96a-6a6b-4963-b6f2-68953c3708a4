<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/margin_medium"
    android:layout_marginVertical="@dimen/margin_small"
    app:cardBackgroundColor="@color/surface_container_high"
    app:cardCornerRadius="@dimen/corner_radius_large"
    app:cardElevation="@dimen/elevation_small">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_medium">

        <ImageView
            android:id="@+id/app_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="@null"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_pubg_global_vector" />

        <TextView
            android:id="@+id/app_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_medium"
            android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
            android:textColor="@color/text_primary"
            app:layout_constraintEnd_toStartOf="@+id/status"
            app:layout_constraintStart_toEndOf="@+id/app_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="PUBG MOBILE GL" />

        <TextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_medium"
            android:background="@drawable/bg_status_chip"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
            android:textColor="@color/surface_container_highest"
            app:layout_constraintEnd_toStartOf="@+id/btn_action"
            app:layout_constraintTop_toTopOf="parent"
            tools:backgroundTint="@color/success"
            tools:text="@string/up_to_date" />

        <TextView
            android:id="@+id/app_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="@color/text_secondary"
            app:layout_constraintStart_toEndOf="@+id/app_icon"
            app:layout_constraintTop_toBottomOf="@+id/app_name"
            tools:text="Version 3.8.0" />

        <TextView
            android:id="@+id/bullet_point"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="2dp"
            android:text="•"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="@color/text_secondary"
            app:layout_constraintStart_toEndOf="@+id/app_version"
            app:layout_constraintTop_toBottomOf="@+id/app_name" />

        <TextView
            android:id="@+id/app_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="2dp"
            android:textAppearance="@style/TextAppearance.Material3.BodySmall"
            android:textColor="@color/text_secondary"
            app:layout_constraintStart_toEndOf="@+id/bullet_point"
            app:layout_constraintTop_toBottomOf="@+id/app_name"
            tools:text="Global version patch" />

        <!-- Download Progress Section (Initially Hidden) -->
        <LinearLayout
            android:id="@+id/download_progress_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="@dimen/margin_medium"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/app_icon"
            app:layout_constraintTop_toBottomOf="@+id/app_description">

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar_inline"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginBottom="4dp"
                android:max="100"
                android:progress="0"
                android:progressTint="@color/accent"
                android:progressBackgroundTint="@color/background_light" />

            <!-- Progress Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_progress_inline"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="0%"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/tv_speed_inline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="0 MB/s"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="@color/text_secondary" />

                <TextView
                    android:id="@+id/tv_eta_inline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="--"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_action"
            style="@style/Widget.Material3.Button.TonalButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:insetLeft="0dp"
            android:insetTop="0dp"
            android:insetRight="0dp"
            android:insetBottom="0dp"
            android:padding="12dp"
            app:backgroundTint="@color/teal_200"
            app:cornerRadius="24dp"
            app:icon="@drawable/ic_download"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconTint="@color/surface_container_highest"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 