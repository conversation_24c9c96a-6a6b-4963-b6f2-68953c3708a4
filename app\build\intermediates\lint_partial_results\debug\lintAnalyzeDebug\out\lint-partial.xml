<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.11.1/transforms/550c9b8c1d2b0a348b84b516683c5512/transformed/exoplayer-common-2.19.1/jars/classes.jar"/>
        <entry
            name="className"
            string="com/google/android/exoplayer2/util/NotificationUtil"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
                            line="51"
                            column="36"
                            startOffset="1708"
                            endLine="51"
                            endColumn="66"
                            endOffset="1738"/>
                    </map>
                    <map id="android.settings.MANAGE_UNKNOWN_APP_SOURCES (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
                            line="87"
                            column="33"
                            startOffset="3272"
                            endLine="87"
                            endColumn="87"
                            endOffset="3326"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.bearmod.loader.ui.splash.SplashActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.anim.slide_in_left"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_left.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="7"
            endOffset="372"/>
        <location id="R.anim.slide_out_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_right.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="7"
            endOffset="371"/>
        <location id="R.color.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="524"
            endLine="16"
            endColumn="24"
            endOffset="536"/>
        <location id="R.color.on_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="49"
            column="12"
            startOffset="1788"
            endLine="49"
            endColumn="32"
            endOffset="1808"/>
        <location id="R.color.on_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="784"
            endLine="23"
            endColumn="29"
            endOffset="801"/>
        <location id="R.color.on_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="30"
            column="12"
            startOffset="1057"
            endLine="30"
            endColumn="31"
            endOffset="1076"/>
        <location id="R.color.on_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="44"
            column="12"
            startOffset="1615"
            endLine="44"
            endColumn="29"
            endOffset="1632"/>
        <location id="R.color.primary_container"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="732"
            endLine="22"
            endColumn="36"
            endOffset="756"/>
        <location id="R.color.progress_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="71"
            column="12"
            startOffset="2483"
            endLine="71"
            endColumn="38"
            endOffset="2509"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="115"
            endLine="4"
            endColumn="29"
            endOffset="132"/>
        <location id="R.color.purple_500"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="162"
            endLine="5"
            endColumn="29"
            endOffset="179"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="209"
            endLine="6"
            endColumn="29"
            endOffset="226"/>
        <location id="R.color.secondary_container"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1003"
            endLine="29"
            endColumn="38"
            endOffset="1029"/>
        <location id="R.color.secondary_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="953"
            endLine="28"
            endColumn="34"
            endOffset="975"/>
        <location id="R.color.teal_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="8"
            column="12"
            startOffset="301"
            endLine="8"
            endColumn="27"
            endOffset="316"/>
        <location id="R.color.text_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="58"
            column="12"
            startOffset="2078"
            endLine="58"
            endColumn="28"
            endOffset="2094"/>
        <location id="R.color.transparent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="61"
            column="12"
            startOffset="2152"
            endLine="61"
            endColumn="30"
            endOffset="2170"/>
        <location id="R.dimen.corner_radius_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="19"
            column="12"
            startOffset="656"
            endLine="19"
            endColumn="39"
            endOffset="683"/>
        <location id="R.dimen.corner_radius_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="20"
            column="12"
            startOffset="708"
            endLine="20"
            endColumn="38"
            endOffset="734"/>
        <location id="R.dimen.elevation_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="24"
            column="12"
            startOffset="834"
            endLine="24"
            endColumn="34"
            endOffset="856"/>
        <location id="R.dimen.elevation_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="25"
            column="12"
            startOffset="880"
            endLine="25"
            endColumn="35"
            endOffset="903"/>
        <location id="R.dimen.icon_size_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="29"
            column="12"
            startOffset="998"
            endLine="29"
            endColumn="34"
            endOffset="1020"/>
        <location id="R.dimen.icon_size_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="30"
            column="12"
            startOffset="1045"
            endLine="30"
            endColumn="35"
            endOffset="1068"/>
        <location id="R.dimen.icon_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="31"
            column="12"
            startOffset="1093"
            endLine="31"
            endColumn="34"
            endOffset="1115"/>
        <location id="R.dimen.margin_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="10"
            column="12"
            startOffset="311"
            endLine="10"
            endColumn="31"
            endOffset="330"/>
        <location id="R.dimen.padding_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="13"
            column="12"
            startOffset="443"
            endLine="13"
            endColumn="32"
            endOffset="463"/>
        <location id="R.dimen.padding_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="15"
            column="12"
            startOffset="534"
            endLine="15"
            endColumn="32"
            endOffset="554"/>
        <location id="R.dimen.text_size_caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="12"
            startOffset="228"
            endLine="7"
            endColumn="36"
            endOffset="252"/>
        <location id="R.dimen.text_size_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="86"
            endLine="4"
            endColumn="34"
            endOffset="108"/>
        <location id="R.dimen.text_size_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="5"
            column="12"
            startOffset="133"
            endLine="5"
            endColumn="35"
            endOffset="156"/>
        <location id="R.dimen.text_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="12"
            startOffset="181"
            endLine="6"
            endColumn="34"
            endOffset="203"/>
        <location id="R.drawable.app_logo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/app_logo.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="10"
            endOffset="577"/>
        <location id="R.drawable.circle_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="190"/>
        <location id="R.drawable.ic_check_circle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="429"/>
        <location id="R.drawable.ic_close"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="415"/>
        <location id="R.drawable.ic_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_error.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="410"/>
        <location id="R.drawable.ic_key"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_key.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="478"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_nav_cloud"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_cloud.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="502"/>
        <location id="R.drawable.ic_nav_home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_home.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="350"/>
        <location id="R.drawable.ic_nav_profile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_profile.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="440"/>
        <location id="R.drawable.ic_nav_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_settings.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="1219"/>
        <location id="R.drawable.ic_paste"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_paste.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="592"/>
        <location id="R.drawable.ic_pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="353"/>
        <location id="R.drawable.ic_pubg_gl"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_gl.png"/>
        <location id="R.drawable.ic_pubg_kr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_kr.png"/>
        <location id="R.drawable.ic_pubg_tw"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_tw.png"/>
        <location id="R.drawable.ic_pubg_vn"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_vn.png"/>
        <location id="R.drawable.ic_search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="562"/>
        <location id="R.drawable.ic_sort"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_sort.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="370"/>
        <location id="R.drawable.splash_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="14"
            endOffset="294"/>
        <location id="R.drawable.splash_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_gradient.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="9"
            endOffset="298"/>
        <location id="R.layout.activity_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="413"
            endColumn="55"
            endOffset="18286"/>
        <location id="R.layout.activity_download_modern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="341"
            endColumn="55"
            endOffset="14855"/>
        <location id="R.layout.item_release"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="164"
            endColumn="53"
            endOffset="7266"/>
        <location id="R.layout.layout_download_progress_modern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_download_progress_modern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="234"
            endColumn="53"
            endOffset="9579"/>
        <location id="R.raw.bear_intro_mobile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/bear_intro_mobile.mp4"/>
        <location id="R.raw.download_complete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/download_complete.json"/>
        <location id="R.raw.success_animation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/success_animation.json"/>
        <location id="R.string.all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="119"
            column="13"
            startOffset="6691"
            endLine="119"
            endColumn="23"
            endOffset="6701"/>
        <location id="R.string.apk_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="3073"
            endLine="58"
            endColumn="28"
            endOffset="3088"/>
        <location id="R.string.auto_update"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="169"
            column="13"
            startOffset="8903"
            endLine="169"
            endColumn="31"
            endOffset="8921"/>
        <location id="R.string.available"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="71"
            column="13"
            startOffset="3944"
            endLine="71"
            endColumn="29"
            endOffset="3960"/>
        <location id="R.string.available_releases"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="2804"
            endLine="54"
            endColumn="38"
            endOffset="2829"/>
        <location id="R.string.cancel_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="3884"
            endLine="70"
            endColumn="35"
            endOffset="3906"/>
        <location id="R.string.cancelled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="180"
            column="13"
            startOffset="9379"
            endLine="180"
            endColumn="29"
            endOffset="9395"/>
        <location id="R.string.clear_filters"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="80"
            column="13"
            startOffset="4436"
            endLine="80"
            endColumn="33"
            endOffset="4456"/>
        <location id="R.string.completed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="73"
            column="13"
            startOffset="4051"
            endLine="73"
            endColumn="29"
            endOffset="4067"/>
        <location id="R.string.confirm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="188"
            column="13"
            startOffset="9663"
            endLine="188"
            endColumn="27"
            endOffset="9677"/>
        <location id="R.string.continue_anyway"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="112"
            column="13"
            startOffset="6228"
            endLine="112"
            endColumn="35"
            endOffset="6250"/>
        <location id="R.string.continue_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="3820"
            endLine="69"
            endColumn="37"
            endOffset="3844"/>
        <location id="R.string.dark_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="91"
            column="13"
            startOffset="4984"
            endLine="91"
            endColumn="29"
            endOffset="5000"/>
        <location id="R.string.delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="178"
            column="13"
            startOffset="9295"
            endLine="178"
            endColumn="26"
            endOffset="9308"/>
        <location id="R.string.delete_all_downloads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="190"
            column="13"
            startOffset="9768"
            endLine="190"
            endColumn="40"
            endOffset="9795"/>
        <location id="R.string.delete_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="189"
            column="13"
            startOffset="9707"
            endLine="189"
            endColumn="35"
            endOffset="9729"/>
        <location id="R.string.download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="125"
            column="13"
            startOffset="7015"
            endLine="125"
            endColumn="28"
            endOffset="7030"/>
        <location id="R.string.download_cancelled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="67"
            column="13"
            startOffset="3660"
            endLine="67"
            endColumn="38"
            endOffset="3685"/>
        <location id="R.string.download_complete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="64"
            column="13"
            startOffset="3455"
            endLine="64"
            endColumn="37"
            endOffset="3479"/>
        <location id="R.string.download_complete_path"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="65"
            column="13"
            startOffset="3519"
            endLine="65"
            endColumn="42"
            endOffset="3548"/>
        <location id="R.string.download_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="66"
            column="13"
            startOffset="3594"
            endLine="66"
            endColumn="35"
            endOffset="3616"/>
        <location id="R.string.download_in_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="68"
            column="13"
            startOffset="3726"
            endLine="68"
            endColumn="40"
            endOffset="3753"/>
        <location id="R.string.download_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="3233"
            endLine="61"
            endColumn="37"
            endOffset="3257"/>
        <location id="R.string.downloaded"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="122"
            column="13"
            startOffset="6889"
            endLine="122"
            endColumn="30"
            endOffset="6906"/>
        <location id="R.string.downloading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="62"
            column="13"
            startOffset="3297"
            endLine="62"
            endColumn="31"
            endOffset="3315"/>
        <location id="R.string.downloading_detailed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="3362"
            endLine="63"
            endColumn="40"
            endOffset="3389"/>
        <location id="R.string.downloading_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3992"
            endLine="72"
            endColumn="38"
            endOffset="4017"/>
        <location id="R.string.downloads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="176"
            column="13"
            startOffset="9189"
            endLine="176"
            endColumn="29"
            endOffset="9205"/>
        <location id="R.string.enter_license_key"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="394"
            endLine="11"
            endColumn="37"
            endOffset="418"/>
        <location id="R.string.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="107"
            column="13"
            startOffset="6017"
            endLine="107"
            endColumn="25"
            endOffset="6029"/>
        <location id="R.string.eta"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="123"
            column="13"
            startOffset="6939"
            endLine="123"
            endColumn="23"
            endOffset="6949"/>
        <location id="R.string.exit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="111"
            column="13"
            startOffset="6190"
            endLine="111"
            endColumn="24"
            endOffset="6201"/>
        <location id="R.string.failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="74"
            column="13"
            startOffset="4099"
            endLine="74"
            endColumn="26"
            endOffset="4112"/>
        <location id="R.string.file_size_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="140"
            column="13"
            startOffset="7787"
            endLine="140"
            endColumn="36"
            endOffset="7810"/>
        <location id="R.string.filter_by_version"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="76"
            column="13"
            startOffset="4202"
            endLine="76"
            endColumn="37"
            endOffset="4226"/>
        <location id="R.string.forgot_password"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="156"
            column="13"
            startOffset="8279"
            endLine="156"
            endColumn="35"
            endOffset="8301"/>
        <location id="R.string.game_version_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="138"
            column="13"
            startOffset="7663"
            endLine="138"
            endColumn="39"
            endOffset="7689"/>
        <location id="R.string.global_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="133"
            column="13"
            startOffset="7350"
            endLine="133"
            endColumn="39"
            endOffset="7376"/>
        <location id="R.string.icon_filter"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="196"
            column="13"
            startOffset="9976"
            endLine="196"
            endColumn="31"
            endOffset="9994"/>
        <location id="R.string.icon_search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="195"
            column="13"
            startOffset="9929"
            endLine="195"
            endColumn="31"
            endOffset="9947"/>
        <location id="R.string.icon_sort"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="197"
            column="13"
            startOffset="10023"
            endLine="197"
            endColumn="29"
            endOffset="10039"/>
        <location id="R.string.info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="110"
            column="13"
            startOffset="6145"
            endLine="110"
            endColumn="24"
            endOffset="6156"/>
        <location id="R.string.invalid_credentials"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="159"
            column="13"
            startOffset="8440"
            endLine="159"
            endColumn="39"
            endOffset="8466"/>
        <location id="R.string.invalid_license_key_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="463"
            endLine="12"
            endColumn="46"
            endOffset="496"/>
        <location id="R.string.keyauth_init_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1076"
            endLine="21"
            endColumn="39"
            endOffset="1102"/>
        <location id="R.string.keyauth_init_warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="962"
            endLine="20"
            endColumn="40"
            endOffset="989"/>
        <location id="R.string.kr_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="134"
            column="13"
            startOffset="7419"
            endLine="134"
            endColumn="35"
            endOffset="7441"/>
        <location id="R.string.language"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="92"
            column="13"
            startOffset="5032"
            endLine="92"
            endColumn="28"
            endOffset="5047"/>
        <location id="R.string.latest"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="6727"
            endLine="120"
            endColumn="26"
            endOffset="6740"/>
        <location id="R.string.license_key"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="342"
            endLine="10"
            endColumn="31"
            endOffset="360"/>
        <location id="R.string.licenses"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="171"
            column="13"
            startOffset="8999"
            endLine="171"
            endColumn="28"
            endOffset="9014"/>
        <location id="R.string.loading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="106"
            column="13"
            startOffset="5972"
            endLine="106"
            endColumn="27"
            endOffset="5986"/>
        <location id="R.string.login"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="597"
            endLine="14"
            endColumn="25"
            endOffset="609"/>
        <location id="R.string.login_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="696"
            endLine="16"
            endColumn="31"
            endOffset="714"/>
        <location id="R.string.login_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="158"
            column="13"
            startOffset="8386"
            endLine="158"
            endColumn="32"
            endOffset="8405"/>
        <location id="R.string.login_subtitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="264"
            endLine="9"
            endColumn="34"
            endOffset="285"/>
        <location id="R.string.login_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="637"
            endLine="15"
            endColumn="33"
            endOffset="657"/>
        <location id="R.string.network_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="160"
            column="13"
            startOffset="8517"
            endLine="160"
            endColumn="33"
            endOffset="8537"/>
        <location id="R.string.no"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="192"
            column="13"
            startOffset="9875"
            endLine="192"
            endColumn="22"
            endOffset="9884"/>
        <location id="R.string.no_downloads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="177"
            column="13"
            startOffset="9237"
            endLine="177"
            endColumn="32"
            endOffset="9256"/>
        <location id="R.string.no_releases_available"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2870"
            endLine="55"
            endColumn="41"
            endOffset="2898"/>
        <location id="R.string.no_results_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="4374"
            endLine="79"
            endColumn="36"
            endOffset="4397"/>
        <location id="R.string.notification_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="89"
            column="13"
            startOffset="4862"
            endLine="89"
            endColumn="41"
            endOffset="4890"/>
        <location id="R.string.notifications"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="168"
            column="13"
            startOffset="8847"
            endLine="168"
            endColumn="33"
            endOffset="8867"/>
        <location id="R.string.obb_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="3125"
            endLine="59"
            endColumn="28"
            endOffset="3140"/>
        <location id="R.string.password"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="155"
            column="13"
            startOffset="8233"
            endLine="155"
            endColumn="28"
            endOffset="8248"/>
        <location id="R.string.patch_applied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="183"
            column="13"
            startOffset="9447"
            endLine="183"
            endColumn="33"
            endOffset="9467"/>
        <location id="R.string.patch_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="184"
            column="13"
            startOffset="9516"
            endLine="184"
            endColumn="32"
            endOffset="9535"/>
        <location id="R.string.patch_in_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="185"
            column="13"
            startOffset="9579"
            endLine="185"
            endColumn="37"
            endOffset="9603"/>
        <location id="R.string.patch_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1622"
            endLine="31"
            endColumn="32"
            endOffset="1641"/>
        <location id="R.string.pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="124"
            column="13"
            startOffset="6975"
            endLine="124"
            endColumn="25"
            endOffset="6987"/>
        <location id="R.string.paused"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="179"
            column="13"
            startOffset="9337"
            endLine="179"
            endColumn="26"
            endOffset="9350"/>
        <location id="R.string.privacy_policy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="172"
            column="13"
            startOffset="9045"
            endLine="172"
            endColumn="34"
            endOffset="9066"/>
        <location id="R.string.pubg_global"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="128"
            column="13"
            startOffset="7088"
            endLine="128"
            endColumn="31"
            endOffset="7106"/>
        <location id="R.string.pubg_kr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="129"
            column="13"
            startOffset="7143"
            endLine="129"
            endColumn="27"
            endOffset="7157"/>
        <location id="R.string.pubg_tw"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="130"
            column="13"
            startOffset="7194"
            endLine="130"
            endColumn="27"
            endOffset="7208"/>
        <location id="R.string.pubg_vn"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="131"
            column="13"
            startOffset="7245"
            endLine="131"
            endColumn="27"
            endOffset="7259"/>
        <location id="R.string.pull_down_to_refresh_or_check_your_connection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="121"
            column="13"
            startOffset="6769"
            endLine="121"
            endColumn="65"
            endOffset="6821"/>
        <location id="R.string.register"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="157"
            column="13"
            startOffset="8340"
            endLine="157"
            endColumn="28"
            endOffset="8355"/>
        <location id="R.string.registration_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="829"
            endLine="18"
            endColumn="37"
            endOffset="853"/>
        <location id="R.string.release_date_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="139"
            column="13"
            startOffset="7724"
            endLine="139"
            endColumn="39"
            endOffset="7750"/>
        <location id="R.string.released"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="3021"
            endLine="57"
            endColumn="28"
            endOffset="3036"/>
        <location id="R.string.remember_me"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="545"
            endLine="13"
            endColumn="31"
            endOffset="563"/>
        <location id="R.string.resume"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="151"
            column="13"
            startOffset="8126"
            endLine="151"
            endColumn="26"
            endOffset="8139"/>
        <location id="R.string.retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="105"
            column="13"
            startOffset="5932"
            endLine="105"
            endColumn="25"
            endOffset="5944"/>
        <location id="R.string.search_releases"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="4141"
            endLine="75"
            endColumn="35"
            endOffset="4163"/>
        <location id="R.string.select_release_first"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="2942"
            endLine="56"
            endColumn="40"
            endOffset="2969"/>
        <location id="R.string.select_target"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1460"
            endLine="28"
            endColumn="33"
            endOffset="1480"/>
        <location id="R.string.sort_by_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="77"
            column="13"
            startOffset="4266"
            endLine="77"
            endColumn="32"
            endOffset="4285"/>
        <location id="R.string.sort_by_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="4320"
            endLine="78"
            endColumn="32"
            endOffset="4339"/>
        <location id="R.string.success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="108"
            column="13"
            startOffset="6057"
            endLine="108"
            endColumn="27"
            endOffset="6071"/>
        <location id="R.string.terms_of_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="173"
            column="13"
            startOffset="9103"
            endLine="173"
            endColumn="36"
            endOffset="9126"/>
        <location id="R.string.theme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="164"
            column="13"
            startOffset="8661"
            endLine="164"
            endColumn="25"
            endOffset="8673"/>
        <location id="R.string.theme_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="166"
            column="13"
            startOffset="8747"
            endLine="166"
            endColumn="30"
            endOffset="8764"/>
        <location id="R.string.theme_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="165"
            column="13"
            startOffset="8701"
            endLine="165"
            endColumn="31"
            endOffset="8719"/>
        <location id="R.string.theme_system"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="167"
            column="13"
            startOffset="8791"
            endLine="167"
            endColumn="32"
            endOffset="8810"/>
        <location id="R.string.total_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="3177"
            endLine="60"
            endColumn="30"
            endOffset="3194"/>
        <location id="R.string.tw_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="135"
            column="13"
            startOffset="7484"
            endLine="135"
            endColumn="35"
            endOffset="7506"/>
        <location id="R.string.unknown_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="161"
            column="13"
            startOffset="8573"
            endLine="161"
            endColumn="33"
            endOffset="8593"/>
        <location id="R.string.username"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="154"
            column="13"
            startOffset="8187"
            endLine="154"
            endColumn="28"
            endOffset="8202"/>
        <location id="R.string.version"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="170"
            column="13"
            startOffset="8955"
            endLine="170"
            endColumn="27"
            endOffset="8969"/>
        <location id="R.string.version_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="132"
            column="13"
            startOffset="7296"
            endLine="132"
            endColumn="34"
            endOffset="7317"/>
        <location id="R.string.vn_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="136"
            column="13"
            startOffset="7549"
            endLine="136"
            endColumn="35"
            endOffset="7571"/>
        <location id="R.string.yes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="191"
            column="13"
            startOffset="9839"
            endLine="191"
            endColumn="23"
            endOffset="9849"/>
        <location id="R.style.Animation_BearLoader_Activity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="84"
            column="12"
            startOffset="4233"
            endLine="84"
            endColumn="48"
            endOffset="4269"/>
        <location id="R.style.TextAppearance_BearLoader_Body1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="220"
            column="12"
            startOffset="10282"
            endLine="220"
            endColumn="50"
            endOffset="10320"/>
        <location id="R.style.TextAppearance_BearLoader_Body2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="226"
            column="12"
            startOffset="10577"
            endLine="226"
            endColumn="50"
            endOffset="10615"/>
        <location id="R.style.TextAppearance_BearLoader_Caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="231"
            column="12"
            startOffset="10823"
            endLine="231"
            endColumn="52"
            endOffset="10863"/>
        <location id="R.style.TextAppearance_BearLoader_Headline6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="214"
            column="12"
            startOffset="9979"
            endLine="214"
            endColumn="54"
            endOffset="10021"/>
        <location id="R.style.Theme_BearLoader_AppBarOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="237"
            column="12"
            startOffset="11109"
            endLine="237"
            endColumn="49"
            endOffset="11146"/>
        <location id="R.style.Theme_BearLoader_Dialog"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="92"
            column="12"
            startOffset="4701"
            endLine="92"
            endColumn="42"
            endOffset="4731"/>
        <location id="R.style.Theme_BearLoader_PopupOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="239"
            column="12"
            startOffset="11209"
            endLine="239"
            endColumn="48"
            endOffset="11245"/>
        <location id="R.style.Theme_BearLoader_Splash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="61"
            column="12"
            startOffset="3216"
            endLine="61"
            endColumn="42"
            endOffset="3246"/>
        <location id="R.style.Theme_BearMod"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="241"
            column="12"
            startOffset="11299"
            endLine="241"
            endColumn="32"
            endOffset="11319"/>
        <location id="R.style.Widget_BearLoader_Button_Icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="206"
            column="12"
            startOffset="9612"
            endLine="206"
            endColumn="48"
            endOffset="9648"/>
        <location id="R.style.Widget_BearLoader_Button_Small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="182"
            column="12"
            startOffset="8589"
            endLine="182"
            endColumn="49"
            endOffset="8626"/>
        <location id="R.style.Widget_BearLoader_Button_Tonal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="154"
            column="12"
            startOffset="7387"
            endLine="154"
            endColumn="49"
            endOffset="7424"/>
        <entry
            name="model"
            string="anim[fade_slide_up(U),pulse(U),slide_in_left(D),slide_in_right(U),slide_out_left(U),slide_out_right(D)],attr[colorOnSurface(E),colorControlNormal(E),actionBarSize(R),selectableItemBackground(R),selectableItemBackgroundBorderless(E)],color[nav_item_color(U),primary(U),text_secondary(U),surface_container_high(U),success(U),primary_light(U),white(U),background(U),text_primary(U),surface_container(U),surface_variant(U),card_background(U),surface(U),background_light(U),ripple(U),surface_container_lowest(U),black(U),divider(U),accent(U),surface_container_highest(U),teal_200(U),card_bg(U),accent_dark(U),shimmer_color(U),purple_200(D),purple_500(D),purple_700(D),teal_700(D),error(D),primary_dark(U),primary_container(D),on_primary(D),secondary(U),secondary_dark(U),secondary_light(D),secondary_container(D),on_secondary(D),accent_light(U),on_surface(D),on_background(D),text_hint(D),transparent(D),warning(U),info(U),progress_background(D)],dimen[margin_medium(U),margin_small(U),corner_radius_large(U),elevation_small(U),padding_medium(U),text_size_large(D),text_size_medium(D),text_size_small(D),text_size_caption(D),margin_large(D),padding_large(D),padding_small(D),corner_radius_medium(D),corner_radius_small(D),glyph_corner_radius(U),elevation_large(D),elevation_medium(D),icon_size_large(D),icon_size_medium(D),icon_size_small(D)],drawable[app_logo(D),bg_bottom_nav(U),bg_main_gradient(U),bg_status(U),bg_status_chip(U),circle_background(D),ic_app_icon(U),logo(U),ic_back(U),ic_check_circle(D),ic_clear_cache(U),ic_close(D),ic_dashboard(U),ic_download(U),ic_empty(U),ic_error(D),ic_glyph_lock(U),ic_home(U),ic_key(D),ic_launcher_background(D),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_logout(U),ic_nav_cloud(D),ic_nav_home(D),ic_nav_profile(D),ic_nav_settings(D),ic_paste(D),ic_pause(D),ic_pubg_gl(D),ic_pubg_global_vector(U),ic_pubg_kr(D),ic_pubg_kr_vector(U),ic_pubg_tw(D),ic_pubg_tw_vector(U),ic_pubg_vn(D),ic_pubg_vn_vector(U),ic_reset(U),ic_search(D),ic_settings(U),ic_sort(D),logo_splash(U),rounded_image_bg(U),splash_background(D),splash_branding(U),splash_gradient(D),splash_gradient_modern(U)],id[app_bar_layout(D),toolbar(U),search_layout(D),et_search(D),chip_group_filters(D),chip_filter_all(D),chip_filter_latest(D),chip_sort_date(D),chip_sort_size(D),swipe_refresh(D),progressLoading(D),layout_empty_state(D),rvReleases(D),cardDownloadInfo(D),tvApkSize(D),tvObbSize(D),tvTotalSize(D),btnDownload(U),cardDownloadProgress(D),tv_download_status(D),progressDownload(D),tv_download_percentage(D),animationDownloadComplete(D),btnCancelDownload(D),tvNoReleases(D),progress_loading(D),rv_releases(D),card_download_info(D),tv_apk_size(D),tv_obb_size(D),tv_total_size(D),btn_download(U),logoContainer(U),logoImage(D),appNameText(U),textInputLayout(U),editLicenseKey(U),checkboxRemember(U),checkboxAutoLogin(U),btnLogin(U),progressBar(U),versionText(U),drawer_layout(D),card_target_app(U),spinner_target_app(D),btn_scan_offsets(D),tv_available_patches(U),bottom_navigation(U),rv_patches(D),shimmer_layout(D),layout_empty(D),btn_download_patches(D),nav_view(D),appBarLayout(D),header_title(D),header_subtitle(D),rv_app_versions(D),tv_patch_name(U),card_mode(U),radio_group_mode(D),radio_root(D),radio_non_root(D),tv_logs_title(U),card_logs(D),btn_start_patching(U),tv_logs(D),progress_patching(D),view_overlay(D),layout_clear_cache(D),layout_reset_config(D),switch_stealth(D),switch_auto_login(D),btn_logout(D),subtitleText(U),progressContainer(U),loadingText(U),glyphView(U),tapText(U),tv_download_title(U),tv_file_name(U),progress_bar(U),tv_progress(U),tv_speed(U),tv_file_size(U),tv_eta(U),btn_cancel(U),app_icon(U),app_name(U),status(U),btn_action(U),app_version(U),bullet_point(U),app_description(U),tv_patch_description(U),tv_game_version(U),tv_update_date(D),tv_status(D),btn_apply_patch(U),ivGameImage(U),tvGameName(U),tvGameDetails(U),shimmer_title(U),shimmer_status(D),shimmer_description(U),shimmer_version(U),shimmer_date(D),shimmer_button(D),card_release(D),iv_release_icon(D),tv_release_name(D),chip_status(D),tv_release_version(D),tv_release_description(D),chip_group_info(D),chip_game_version(D),chip_release_date(D),chip_file_size(D),ripple_effect(D),card_download_progress(D),progress_circular(D),chip_download_status(D),progress_download(D),tv_download_speed(D),tv_downloaded_size(D),btn_pause_resume(D),btn_cancel_download(D),tv_license_info(U),nav_home(U),nav_downloads(U),nav_settings(U),nav_dashboard(U),nav_download(U),nav_logout(U)],layout[activity_download(D),item_release(D),activity_download_modern(D),activity_login(U),activity_main(U),item_patch(U),item_patch_shimmer(U),nav_header(U),activity_main_loader(U),activity_patch_execution(U),activity_settings(U),activity_splash(U),activity_tap_unlock(U),dialog_download_progress(U),item_app_version(U),item_patch_release(U),layout_download_progress_modern(D)],menu[bottom_nav_menu(U),drawer_menu(U),bottom_navigation_menu(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],raw[bear_intro_mobile(D),download_complete(D),success_animation(D)],string[app_name(U),download_title(U),search_releases(D),all(D),latest(D),sort_by_date(D),sort_by_size(D),appbar_scrolling_view_behavior(R),no_releases_available(D),pull_down_to_refresh_or_check_your_connection(D),download_patches(U),icon_search(D),downloading(D),cancel_download(D),app_version(U),dashboard_title(U),target_app(U),scan_offsets(U),available_patches(U),no_patches_available(U),select_version(U),patch_execution(U),execution_mode(U),root_mode(U),non_root_mode(U),execution_logs(U),start_patching(U),settings_title(U),app_settings(U),clear_cache(U),reset_config(U),patch_settings(U),toggle_stealth(U),auto_login(U),about(U),version_info(U),about_description(U),logout(U),up_to_date(U),apply_patch(U),game_icon(U),game_version_format(D),available(D),download_progress(D),downloading_status(D),downloaded(D),eta(D),pause(D),app_logo(U),license_valid_until(U),nav_home(U),nav_downloads(U),nav_settings(U),login_subtitle(D),license_key(D),enter_license_key(D),invalid_license_key_format(D),remember_me(D),login(D),login_success(D),login_error(D),registration_date(D),days_remaining(U),keyauth_init_warning(D),keyauth_init_failed(D),select_target(D),patch_status(D),update_available(U),not_installed(U),game_version(U),updated(U),stop_patching(U),patching_in_progress(U),patching_complete(U),patching_failed(U),available_releases(D),select_release_first(D),released(D),apk_size(D),obb_size(D),total_size(D),downloading_detailed(D),download_complete(D),download_complete_path(D),download_failed(D),download_cancelled(D),download_in_progress(D),continue_download(D),completed(D),failed(D),filter_by_version(D),no_results_found(D),clear_filters(D),notification_settings(D),dark_mode(D),language(D),cache_cleared(U),config_reset(U),clear_cache_confirm(U),reset_config_confirm(U),logout_confirm(U),ok(U),cancel(U),retry(D),loading(D),error(D),success(D),warning(U),info(D),exit(D),continue_anyway(D),navigation_drawer_open(U),navigation_drawer_close(U),patches_updated(U),sync_error(U),scanning_offsets(U),scanning_offsets_for(U),download(D),pubg_global(D),pubg_kr(D),pubg_tw(D),pubg_vn(D),version_format(D),global_version_desc(D),kr_version_desc(D),tw_version_desc(D),vn_version_desc(D),release_date_format(D),file_size_format(D),resume(D),username(D),password(D),forgot_password(D),register(D),login_failed(D),invalid_credentials(D),network_error(D),unknown_error(D),theme(D),theme_light(D),theme_dark(D),theme_system(D),notifications(D),auto_update(D),version(D),licenses(D),privacy_policy(D),terms_of_service(D),downloads(D),no_downloads(D),delete(D),paused(D),cancelled(D),patch_applied(D),patch_failed(D),patch_in_progress(D),confirm(D),delete_download(D),delete_all_downloads(D),yes(D),no(D),icon_filter(D),icon_sort(D)],style[Theme_BearLoader(U),Theme_BearLoader_NoActionBar(U),TextAppearance_Material3_HeadlineMedium(E),Widget_Material3_TextInputLayout_OutlinedBox(R),Widget_Material3_Chip_Filter(E),TextAppearance_Material3_HeadlineSmall(E),TextAppearance_Material3_BodyMedium(E),Widget_Material3_CardView_Elevated(E),TextAppearance_Material3_TitleMedium(R),Widget_BearLoader_Button(U),TextAppearance_Material3_BodyLarge(E),TextAppearance_Material3_LabelLarge(E),Widget_BearLoader_Button_Secondary(U),TextAppearance_Material3_LabelSmall(R),TextAppearance_Material3_BodySmall(R),Widget_Material3_Button_TonalButton(R),TextAppearance_Material3_LabelMedium(E),Widget_Material3_Chip_Assist(E),Widget_BearLoader_Button_Tonal(D),ThemeOverlay_AppCompat_Dark(R),TextAppearance_AppCompat_Body1(R),Theme_Material3_DayNight_NoActionBar(R),ShapeAppearance_BearLoader_SmallComponent(U),ShapeAppearance_BearLoader_MediumComponent(U),ShapeAppearance_BearLoader_LargeComponent(U),Theme(R),Theme_BearLoader_Splash(D),ShapeAppearance_Material3_SmallComponent(R),ShapeAppearance_Material3_MediumComponent(R),ShapeAppearance_Material3_LargeComponent(R),Animation_BearLoader_Activity(D),Theme_BearLoader_Dialog(D),ThemeOverlay_Material3_Dialog_Alert(E),Widget_Material3_Button(R),Widget_Material3_Button_OutlinedButton(R),Widget_BearLoader_Button_Small(D),Widget_BearLoader_Button_Icon(D),Widget_Material3_Button_IconButton(E),TextAppearance_BearLoader_Headline6(D),TextAppearance_BearLoader_Body1(D),TextAppearance_BearLoader_Body2(D),TextAppearance_BearLoader_Caption(D),Theme_BearLoader_AppBarOverlay(D),ThemeOverlay_Material3_Dark_ActionBar(E),Theme_BearLoader_PopupOverlay(D),ThemeOverlay_Material3_Light(E),Theme_BearMod(D),Theme_Material3_DayNight(R)],xml[data_extraction_rules(U),backup_rules(U),file_paths(U)];b^c^d,4d^e,4f^f,50^f,51^10,52^11^53,54^11,56^c,57^6,58^c,5a^d,60^61,62^c,67^7,68^6,71^c,72^6,74^6,75^53,76^e,77^12^53,78^12^114,100^12^8^54^11a^1be^13^11b^14^e^72^1bf^d^11c^10^1c0^11d^11e^74^11f^120^c^59^121^1c1^122^1c2^101^1c3^123^124^1c4^1c5^125^1c6^1c7^126^1c8,101^14^e^1c3^4f^123^59^1b^1c1^13^e9^e7^1cc^12^e8^142^143^18^1cd^1c2^eb^1c0^d^21^a,102^12^8^54^11a^1be^13^11b^14^e^72^1bf^d^11c^10^1c0^11d^11e^74^11f^120^c^59^121^1c1^122^1c2^101^95^1c3^123^124^1c4^1c5,103^7a^15^53^11^1bf^c^127,104^1bd^c^8^128^11^12^120^16^129^13^12a^1c5^12b^a6^aa^a9^105^106^12c^5a^d^123^1c8^17^111^18^107^112,105^18^13^9a^12^b4^d8^1d^59^11^4f^f^140^d9^1c5^dc,106^16^22^e0^e2^e3,107^18^1cf^149^75^119^1d0^14a,108^4e^12b^13^12d^d^120^4d^b^19^113,109^12^c^8^54^12e^11^13^7c^16^b4^12f^130^131^132^b5^bb^b9^133,10a^12^1a^8^54^134^7c^135^1b^18^9^136^56^13^1c^137^71^138^139^13a^13b^119^13c^13d^13e^1c5,10b^7a^17^9d^119^53^11^c4^9b^c5^a4^127,10c^1b^5c^c7,10d^13^d^18^1d^1c5,10e^38^39^e^3a^3b^3c^6a^1c4^13^d3^d1^50^1c9^1e^d4^f^13f^1ca^d^d2^d5^d6^1f^59^1cb,10f^20^76^141^13^8c^dd^d^de^21^59^11^1d,110^14^e^1c3^18^144^1c1^13^145^f^59^1cd^1c2^12^1cc^1d^1c4^146^1c9^21^147^d^148^68^1ce^126^57^1c8,111^5d^14b^59^14c^73^14d,112^58^128^59^11a^73^134^62^13e,113^5d^14b^59^14c^73^14d,114^11^52,115^11^52,1bc^1d1^c^28^11^10^12^2b^2c^1b^30^14^13^15^d^60^78^1d2^1d3^1d4^1eb^1d^16^18,1bd^1bc^1d5,1c5^1dd^11^c^10,1c8^1de^c^10,1ce^1cb^c^1e^10,1d2^1d7,1d3^1d8,1d4^1d9,1d6^1bd^12,1da^3^4^2^5,1db^1dc^c^1d^12^13,1df^1dd^11^c^10,1e0^1e1^c^10,1e2^1be^3d^13,1e3^1c6^3e^13,1e4^1c2^3f^d,1e5^1c9^40^d,1e6^1e7,1e8^1e9,1ea^1d1^c^29^2a^2b^2e^2f^17^15^31^12;;;"/>
    </map>

</incidents>
