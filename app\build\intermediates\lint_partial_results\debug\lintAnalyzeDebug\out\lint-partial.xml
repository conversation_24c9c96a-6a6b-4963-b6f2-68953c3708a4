<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.11.1/transforms/573843d77d4a88fcda0ded0a8cbbf042/transformed/exoplayer-common-2.19.1/jars/classes.jar"/>
        <entry
            name="className"
            string="com/google/android/exoplayer2/util/NotificationUtil"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
                            line="51"
                            column="36"
                            startOffset="1708"
                            endLine="51"
                            endColumn="66"
                            endOffset="1738"/>
                    </map>
                    <map id="android.settings.MANAGE_UNKNOWN_APP_SOURCES (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
                            line="87"
                            column="33"
                            startOffset="3272"
                            endLine="87"
                            endColumn="87"
                            endOffset="3326"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.bearmod.loader.ui.splash.SplashActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.anim.slide_in_left"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_left.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="7"
            endOffset="372"/>
        <location id="R.anim.slide_out_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_right.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="7"
            endOffset="371"/>
        <location id="R.color.accent_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="924"
            endLine="28"
            endColumn="30"
            endOffset="942"/>
        <location id="R.color.card_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="26"
            endOffset="170"/>
        <location id="R.color.on_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="808"
            endLine="24"
            endColumn="31"
            endOffset="827"/>
        <location id="R.color.primary_container"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="483"
            endLine="16"
            endColumn="36"
            endOffset="507"/>
        <location id="R.color.secondary_container"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="754"
            endLine="23"
            endColumn="38"
            endOffset="780"/>
        <location id="R.color.secondary_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="704"
            endLine="22"
            endColumn="34"
            endOffset="726"/>
        <location id="R.dimen.corner_radius_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="19"
            column="12"
            startOffset="656"
            endLine="19"
            endColumn="39"
            endOffset="683"/>
        <location id="R.dimen.corner_radius_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="20"
            column="12"
            startOffset="708"
            endLine="20"
            endColumn="38"
            endOffset="734"/>
        <location id="R.dimen.elevation_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="24"
            column="12"
            startOffset="834"
            endLine="24"
            endColumn="34"
            endOffset="856"/>
        <location id="R.dimen.elevation_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="25"
            column="12"
            startOffset="880"
            endLine="25"
            endColumn="35"
            endOffset="903"/>
        <location id="R.dimen.icon_size_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="29"
            column="12"
            startOffset="998"
            endLine="29"
            endColumn="34"
            endOffset="1020"/>
        <location id="R.dimen.icon_size_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="30"
            column="12"
            startOffset="1045"
            endLine="30"
            endColumn="35"
            endOffset="1068"/>
        <location id="R.dimen.icon_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="31"
            column="12"
            startOffset="1093"
            endLine="31"
            endColumn="34"
            endOffset="1115"/>
        <location id="R.dimen.margin_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="10"
            column="12"
            startOffset="311"
            endLine="10"
            endColumn="31"
            endOffset="330"/>
        <location id="R.dimen.padding_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="13"
            column="12"
            startOffset="443"
            endLine="13"
            endColumn="32"
            endOffset="463"/>
        <location id="R.dimen.padding_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="15"
            column="12"
            startOffset="534"
            endLine="15"
            endColumn="32"
            endOffset="554"/>
        <location id="R.dimen.text_size_caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="7"
            column="12"
            startOffset="228"
            endLine="7"
            endColumn="36"
            endOffset="252"/>
        <location id="R.dimen.text_size_large"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="86"
            endLine="4"
            endColumn="34"
            endOffset="108"/>
        <location id="R.dimen.text_size_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="5"
            column="12"
            startOffset="133"
            endLine="5"
            endColumn="35"
            endOffset="156"/>
        <location id="R.dimen.text_size_small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="6"
            column="12"
            startOffset="181"
            endLine="6"
            endColumn="34"
            endOffset="203"/>
        <location id="R.drawable.download_card_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/download_card_bg.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="295"/>
        <location id="R.drawable.ic_check_circle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="429"/>
        <location id="R.drawable.ic_close"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="415"/>
        <location id="R.drawable.ic_dashboard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="391"/>
        <location id="R.drawable.ic_empty"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="509"/>
        <location id="R.drawable.ic_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_error.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="410"/>
        <location id="R.drawable.ic_key"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_key.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="478"/>
        <location id="R.drawable.ic_launcher_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="170"
            endColumn="10"
            endOffset="5605"/>
        <location id="R.drawable.ic_logout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_logout.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="430"/>
        <location id="R.drawable.ic_nav_cloud"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_cloud.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="502"/>
        <location id="R.drawable.ic_nav_home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_home.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="350"/>
        <location id="R.drawable.ic_nav_profile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_profile.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="440"/>
        <location id="R.drawable.ic_nav_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_settings.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="10"
            endColumn="10"
            endOffset="1219"/>
        <location id="R.drawable.ic_paste"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_paste.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="10"
            endOffset="592"/>
        <location id="R.drawable.ic_pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="353"/>
        <location id="R.drawable.logo_splash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo_splash.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="14"
            endOffset="244"/>
        <location id="R.drawable.pubg_status_brutal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/pubg_status_brutal.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="10"
            endOffset="766"/>
        <location id="R.drawable.splash_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="14"
            endOffset="294"/>
        <location id="R.drawable.splash_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/splash_gradient.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="9"
            endOffset="298"/>
        <location id="R.layout.activity_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="413"
            endColumn="55"
            endOffset="18294"/>
        <location id="R.layout.activity_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="217"
            endColumn="45"
            endOffset="9426"/>
        <location id="R.layout.layout_download_progress_modern"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_download_progress_modern.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="234"
            endColumn="53"
            endOffset="9527"/>
        <location id="R.layout.nav_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="34"
            endColumn="16"
            endOffset="1181"/>
        <location id="R.menu.drawer_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/drawer_menu.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="26"
            endColumn="8"
            endOffset="891"/>
        <location id="R.raw.download_complete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/download_complete.json"/>
        <location id="R.raw.success_animation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/success_animation.json"/>
        <location id="R.string.all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="99"
            column="13"
            startOffset="5440"
            endLine="99"
            endColumn="23"
            endOffset="5450"/>
        <location id="R.string.app_logo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="218"
            endLine="8"
            endColumn="28"
            endOffset="233"/>
        <location id="R.string.auto_update"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="149"
            column="13"
            startOffset="7652"
            endLine="149"
            endColumn="31"
            endOffset="7670"/>
        <location id="R.string.available"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="55"
            column="13"
            startOffset="2950"
            endLine="55"
            endColumn="29"
            endOffset="2966"/>
        <location id="R.string.available_releases"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="1708"
            endLine="36"
            endColumn="38"
            endOffset="1733"/>
        <location id="R.string.cancel_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="54"
            column="13"
            startOffset="2890"
            endLine="54"
            endColumn="35"
            endOffset="2912"/>
        <location id="R.string.cancelled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="160"
            column="13"
            startOffset="8128"
            endLine="160"
            endColumn="29"
            endOffset="8144"/>
        <location id="R.string.clear_filters"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="64"
            column="13"
            startOffset="3442"
            endLine="64"
            endColumn="33"
            endOffset="3462"/>
        <location id="R.string.completed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="3057"
            endLine="57"
            endColumn="29"
            endOffset="3073"/>
        <location id="R.string.confirm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="165"
            column="13"
            startOffset="8200"
            endLine="165"
            endColumn="27"
            endOffset="8214"/>
        <location id="R.string.continue_anyway"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="95"
            column="13"
            startOffset="5174"
            endLine="95"
            endColumn="35"
            endOffset="5196"/>
        <location id="R.string.continue_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2826"
            endLine="53"
            endColumn="37"
            endOffset="2850"/>
        <location id="R.string.dark_mode"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="74"
            column="13"
            startOffset="3932"
            endLine="74"
            endColumn="29"
            endOffset="3948"/>
        <location id="R.string.dashboard_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1292"
            endLine="25"
            endColumn="35"
            endOffset="1314"/>
        <location id="R.string.days_remaining"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="899"
            endLine="19"
            endColumn="34"
            endOffset="920"/>
        <location id="R.string.delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="158"
            column="13"
            startOffset="8044"
            endLine="158"
            endColumn="26"
            endOffset="8057"/>
        <location id="R.string.delete_all_downloads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="167"
            column="13"
            startOffset="8305"
            endLine="167"
            endColumn="40"
            endOffset="8332"/>
        <location id="R.string.delete_download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="166"
            column="13"
            startOffset="8244"
            endLine="166"
            endColumn="35"
            endOffset="8266"/>
        <location id="R.string.download"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="105"
            column="13"
            startOffset="5764"
            endLine="105"
            endColumn="28"
            endOffset="5779"/>
        <location id="R.string.download_cancelled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2666"
            endLine="51"
            endColumn="38"
            endOffset="2691"/>
        <location id="R.string.download_complete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="48"
            column="13"
            startOffset="2461"
            endLine="48"
            endColumn="37"
            endOffset="2485"/>
        <location id="R.string.download_complete_path"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="2525"
            endLine="49"
            endColumn="42"
            endOffset="2554"/>
        <location id="R.string.download_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="2600"
            endLine="50"
            endColumn="35"
            endOffset="2622"/>
        <location id="R.string.download_in_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="2732"
            endLine="52"
            endColumn="40"
            endOffset="2759"/>
        <location id="R.string.download_patches"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="1846"
            endLine="38"
            endColumn="36"
            endOffset="1869"/>
        <location id="R.string.download_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="2239"
            endLine="45"
            endColumn="37"
            endOffset="2263"/>
        <location id="R.string.download_title"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="1656"
            endLine="35"
            endColumn="34"
            endOffset="1677"/>
        <location id="R.string.downloaded"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="102"
            column="13"
            startOffset="5638"
            endLine="102"
            endColumn="30"
            endOffset="5655"/>
        <location id="R.string.downloading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="2303"
            endLine="46"
            endColumn="31"
            endOffset="2321"/>
        <location id="R.string.downloading_detailed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="2368"
            endLine="47"
            endColumn="40"
            endOffset="2395"/>
        <location id="R.string.downloading_status"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="2998"
            endLine="56"
            endColumn="38"
            endOffset="3023"/>
        <location id="R.string.downloads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="156"
            column="13"
            startOffset="7938"
            endLine="156"
            endColumn="29"
            endOffset="7954"/>
        <location id="R.string.enter_license_key"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="11"
            column="13"
            startOffset="394"
            endLine="11"
            endColumn="37"
            endOffset="418"/>
        <location id="R.string.error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="90"
            column="13"
            startOffset="4963"
            endLine="90"
            endColumn="25"
            endOffset="4975"/>
        <location id="R.string.eta"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="103"
            column="13"
            startOffset="5688"
            endLine="103"
            endColumn="23"
            endOffset="5698"/>
        <location id="R.string.exit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="94"
            column="13"
            startOffset="5136"
            endLine="94"
            endColumn="24"
            endOffset="5147"/>
        <location id="R.string.failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="3105"
            endLine="58"
            endColumn="26"
            endOffset="3118"/>
        <location id="R.string.file_size_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="6536"
            endLine="120"
            endColumn="36"
            endOffset="6559"/>
        <location id="R.string.filter_by_version"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="3208"
            endLine="60"
            endColumn="37"
            endOffset="3232"/>
        <location id="R.string.forgot_password"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="136"
            column="13"
            startOffset="7028"
            endLine="136"
            endColumn="35"
            endOffset="7050"/>
        <location id="R.string.game_icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="117"
            column="13"
            startOffset="6364"
            endLine="117"
            endColumn="29"
            endOffset="6380"/>
        <location id="R.string.game_version"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1514"
            endLine="29"
            endColumn="32"
            endOffset="1533"/>
        <location id="R.string.game_version_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="118"
            column="13"
            startOffset="6412"
            endLine="118"
            endColumn="39"
            endOffset="6438"/>
        <location id="R.string.global_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="113"
            column="13"
            startOffset="6099"
            endLine="113"
            endColumn="39"
            endOffset="6125"/>
        <location id="R.string.icon_filter"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="173"
            column="13"
            startOffset="8513"
            endLine="173"
            endColumn="31"
            endOffset="8531"/>
        <location id="R.string.icon_search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="172"
            column="13"
            startOffset="8466"
            endLine="172"
            endColumn="31"
            endOffset="8484"/>
        <location id="R.string.icon_sort"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="174"
            column="13"
            startOffset="8560"
            endLine="174"
            endColumn="29"
            endOffset="8576"/>
        <location id="R.string.info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="93"
            column="13"
            startOffset="5091"
            endLine="93"
            endColumn="24"
            endOffset="5102"/>
        <location id="R.string.invalid_credentials"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="139"
            column="13"
            startOffset="7189"
            endLine="139"
            endColumn="39"
            endOffset="7215"/>
        <location id="R.string.invalid_license_key_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="463"
            endLine="12"
            endColumn="46"
            endOffset="496"/>
        <location id="R.string.keyauth_init_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1076"
            endLine="21"
            endColumn="39"
            endOffset="1102"/>
        <location id="R.string.keyauth_init_warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="962"
            endLine="20"
            endColumn="40"
            endOffset="989"/>
        <location id="R.string.kr_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="114"
            column="13"
            startOffset="6168"
            endLine="114"
            endColumn="35"
            endOffset="6190"/>
        <location id="R.string.language"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="75"
            column="13"
            startOffset="3980"
            endLine="75"
            endColumn="28"
            endOffset="3995"/>
        <location id="R.string.latest"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="5476"
            endLine="100"
            endColumn="26"
            endOffset="5489"/>
        <location id="R.string.license_key"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="10"
            column="13"
            startOffset="342"
            endLine="10"
            endColumn="31"
            endOffset="360"/>
        <location id="R.string.license_valid_until"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="755"
            endLine="17"
            endColumn="39"
            endOffset="781"/>
        <location id="R.string.licenses"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="151"
            column="13"
            startOffset="7748"
            endLine="151"
            endColumn="28"
            endOffset="7763"/>
        <location id="R.string.loading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="89"
            column="13"
            startOffset="4918"
            endLine="89"
            endColumn="27"
            endOffset="4932"/>
        <location id="R.string.login"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="597"
            endLine="14"
            endColumn="25"
            endOffset="609"/>
        <location id="R.string.login_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="696"
            endLine="16"
            endColumn="31"
            endOffset="714"/>
        <location id="R.string.login_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="138"
            column="13"
            startOffset="7135"
            endLine="138"
            endColumn="32"
            endOffset="7154"/>
        <location id="R.string.login_subtitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="264"
            endLine="9"
            endColumn="34"
            endOffset="285"/>
        <location id="R.string.login_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="637"
            endLine="15"
            endColumn="33"
            endOffset="657"/>
        <location id="R.string.navigation_drawer_close"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="97"
            column="13"
            startOffset="5308"
            endLine="97"
            endColumn="43"
            endOffset="5338"/>
        <location id="R.string.navigation_drawer_open"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="96"
            column="13"
            startOffset="5234"
            endLine="96"
            endColumn="42"
            endOffset="5263"/>
        <location id="R.string.network_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="140"
            column="13"
            startOffset="7266"
            endLine="140"
            endColumn="33"
            endOffset="7286"/>
        <location id="R.string.no"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="169"
            column="13"
            startOffset="8412"
            endLine="169"
            endColumn="22"
            endOffset="8421"/>
        <location id="R.string.no_downloads"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="157"
            column="13"
            startOffset="7986"
            endLine="157"
            endColumn="32"
            endOffset="8005"/>
        <location id="R.string.no_releases_available"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1774"
            endLine="37"
            endColumn="41"
            endOffset="1802"/>
        <location id="R.string.no_results_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="3380"
            endLine="63"
            endColumn="36"
            endOffset="3403"/>
        <location id="R.string.not_installed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1458"
            endLine="28"
            endColumn="33"
            endOffset="1478"/>
        <location id="R.string.notification_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3810"
            endLine="72"
            endColumn="41"
            endOffset="3838"/>
        <location id="R.string.notifications"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="148"
            column="13"
            startOffset="7596"
            endLine="148"
            endColumn="33"
            endOffset="7616"/>
        <location id="R.string.obb_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2131"
            endLine="43"
            endColumn="28"
            endOffset="2146"/>
        <location id="R.string.password"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="135"
            column="13"
            startOffset="6982"
            endLine="135"
            endColumn="28"
            endOffset="6997"/>
        <location id="R.string.pause"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="104"
            column="13"
            startOffset="5724"
            endLine="104"
            endColumn="25"
            endOffset="5736"/>
        <location id="R.string.paused"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="159"
            column="13"
            startOffset="8086"
            endLine="159"
            endColumn="26"
            endOffset="8099"/>
        <location id="R.string.privacy_policy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="152"
            column="13"
            startOffset="7794"
            endLine="152"
            endColumn="34"
            endOffset="7815"/>
        <location id="R.string.pubg_global"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="108"
            column="13"
            startOffset="5837"
            endLine="108"
            endColumn="31"
            endOffset="5855"/>
        <location id="R.string.pubg_kr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="109"
            column="13"
            startOffset="5892"
            endLine="109"
            endColumn="27"
            endOffset="5906"/>
        <location id="R.string.pubg_tw"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="110"
            column="13"
            startOffset="5943"
            endLine="110"
            endColumn="27"
            endOffset="5957"/>
        <location id="R.string.pubg_vn"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="111"
            column="13"
            startOffset="5994"
            endLine="111"
            endColumn="27"
            endOffset="6008"/>
        <location id="R.string.pull_down_to_refresh_or_check_your_connection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="5518"
            endLine="101"
            endColumn="65"
            endOffset="5570"/>
        <location id="R.string.register"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="137"
            column="13"
            startOffset="7089"
            endLine="137"
            endColumn="28"
            endOffset="7104"/>
        <location id="R.string.registration_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="829"
            endLine="18"
            endColumn="37"
            endOffset="853"/>
        <location id="R.string.release_date_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="119"
            column="13"
            startOffset="6473"
            endLine="119"
            endColumn="39"
            endOffset="6499"/>
        <location id="R.string.remember_me"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="545"
            endLine="13"
            endColumn="31"
            endOffset="563"/>
        <location id="R.string.resume"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="131"
            column="13"
            startOffset="6875"
            endLine="131"
            endColumn="26"
            endOffset="6888"/>
        <location id="R.string.retry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="88"
            column="13"
            startOffset="4878"
            endLine="88"
            endColumn="25"
            endOffset="4890"/>
        <location id="R.string.scan_offsets"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="1950"
            endLine="40"
            endColumn="32"
            endOffset="1969"/>
        <location id="R.string.search_releases"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="3147"
            endLine="59"
            endColumn="35"
            endOffset="3169"/>
        <location id="R.string.sort_by_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="3272"
            endLine="61"
            endColumn="32"
            endOffset="3291"/>
        <location id="R.string.sort_by_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="62"
            column="13"
            startOffset="3326"
            endLine="62"
            endColumn="32"
            endOffset="3345"/>
        <location id="R.string.success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="91"
            column="13"
            startOffset="5003"
            endLine="91"
            endColumn="27"
            endOffset="5017"/>
        <location id="R.string.sync_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="98"
            column="13"
            startOffset="5384"
            endLine="98"
            endColumn="30"
            endOffset="5401"/>
        <location id="R.string.target_app"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="1900"
            endLine="39"
            endColumn="30"
            endOffset="1917"/>
        <location id="R.string.terms_of_service"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="153"
            column="13"
            startOffset="7852"
            endLine="153"
            endColumn="36"
            endOffset="7875"/>
        <location id="R.string.theme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="144"
            column="13"
            startOffset="7410"
            endLine="144"
            endColumn="25"
            endOffset="7422"/>
        <location id="R.string.theme_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="146"
            column="13"
            startOffset="7496"
            endLine="146"
            endColumn="30"
            endOffset="7513"/>
        <location id="R.string.theme_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="145"
            column="13"
            startOffset="7450"
            endLine="145"
            endColumn="31"
            endOffset="7468"/>
        <location id="R.string.theme_system"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="147"
            column="13"
            startOffset="7540"
            endLine="147"
            endColumn="32"
            endOffset="7559"/>
        <location id="R.string.total_size"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="2183"
            endLine="44"
            endColumn="30"
            endOffset="2200"/>
        <location id="R.string.tw_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="115"
            column="13"
            startOffset="6233"
            endLine="115"
            endColumn="35"
            endOffset="6255"/>
        <location id="R.string.unknown_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="141"
            column="13"
            startOffset="7322"
            endLine="141"
            endColumn="33"
            endOffset="7342"/>
        <location id="R.string.update_available"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1346"
            endLine="26"
            endColumn="36"
            endOffset="1369"/>
        <location id="R.string.updated"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1574"
            endLine="30"
            endColumn="27"
            endOffset="1588"/>
        <location id="R.string.username"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="134"
            column="13"
            startOffset="6936"
            endLine="134"
            endColumn="28"
            endOffset="6951"/>
        <location id="R.string.version"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="150"
            column="13"
            startOffset="7704"
            endLine="150"
            endColumn="27"
            endOffset="7718"/>
        <location id="R.string.version_format"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="112"
            column="13"
            startOffset="6045"
            endLine="112"
            endColumn="34"
            endOffset="6066"/>
        <location id="R.string.vn_version_desc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="116"
            column="13"
            startOffset="6298"
            endLine="116"
            endColumn="35"
            endOffset="6320"/>
        <location id="R.string.warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="92"
            column="13"
            startOffset="5047"
            endLine="92"
            endColumn="27"
            endOffset="5061"/>
        <location id="R.string.yes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="168"
            column="13"
            startOffset="8376"
            endLine="168"
            endColumn="23"
            endOffset="8386"/>
        <location id="R.style.Animation_BearLoader_Activity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="84"
            column="12"
            startOffset="4233"
            endLine="84"
            endColumn="48"
            endOffset="4269"/>
        <location id="R.style.TextAppearance_BearLoader_Body1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="220"
            column="12"
            startOffset="10282"
            endLine="220"
            endColumn="50"
            endOffset="10320"/>
        <location id="R.style.TextAppearance_BearLoader_Body2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="226"
            column="12"
            startOffset="10577"
            endLine="226"
            endColumn="50"
            endOffset="10615"/>
        <location id="R.style.TextAppearance_BearLoader_Caption"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="231"
            column="12"
            startOffset="10823"
            endLine="231"
            endColumn="52"
            endOffset="10863"/>
        <location id="R.style.TextAppearance_BearLoader_Headline6"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="214"
            column="12"
            startOffset="9979"
            endLine="214"
            endColumn="54"
            endOffset="10021"/>
        <location id="R.style.Theme_BearLoader_AppBarOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="237"
            column="12"
            startOffset="11109"
            endLine="237"
            endColumn="49"
            endOffset="11146"/>
        <location id="R.style.Theme_BearLoader_Dialog"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="92"
            column="12"
            startOffset="4701"
            endLine="92"
            endColumn="42"
            endOffset="4731"/>
        <location id="R.style.Theme_BearLoader_Splash"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="61"
            column="12"
            startOffset="3216"
            endLine="61"
            endColumn="42"
            endOffset="3246"/>
        <location id="R.style.Theme_BearMod"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="241"
            column="12"
            startOffset="11211"
            endLine="241"
            endColumn="32"
            endOffset="11231"/>
        <location id="R.style.Widget_BearLoader_Button_Icon"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="206"
            column="12"
            startOffset="9612"
            endLine="206"
            endColumn="48"
            endOffset="9648"/>
        <location id="R.style.Widget_BearLoader_Button_Secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="127"
            column="12"
            startOffset="6217"
            endLine="127"
            endColumn="53"
            endOffset="6258"/>
        <location id="R.style.Widget_BearLoader_Button_Small"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="182"
            column="12"
            startOffset="8589"
            endLine="182"
            endColumn="49"
            endOffset="8626"/>
        <location id="R.style.Widget_BearLoader_Button_Tonal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="154"
            column="12"
            startOffset="7387"
            endLine="154"
            endColumn="49"
            endOffset="7424"/>
        <entry
            name="model"
            string="anim[fade_slide_up(U),pulse(U),slide_in_left(D),slide_in_right(U),slide_out_left(U),slide_out_right(D)],attr[colorOnSurface(R),colorControlNormal(E),actionBarSize(R),selectableItemBackgroundBorderless(R),selectableItemBackground(R)],color[nav_item_color(U),primary(U),text_secondary(U),surface_container_high(U),outline_variant(U),surface_container(U),surface_container_low(U),success(U),white(U),background(U),text_primary(U),primary_light(U),surface_variant(U),card_background(U),surface(U),background_light(U),ripple(U),surface_container_lowest(U),on_surface(U),on_primary(U),divider(U),black(U),accent(U),surface_container_highest(U),teal_200(U),warning(U),accent_dark(D),primary_dark(U),primary_container(D),secondary(U),secondary_dark(U),secondary_light(D),secondary_container(D),on_secondary(D),accent_light(U),info(U),card_bg(D)],dimen[margin_medium(U),margin_small(U),corner_radius_large(U),elevation_small(U),padding_medium(U),text_size_large(D),text_size_medium(D),text_size_small(D),text_size_caption(D),margin_large(D),padding_large(D),padding_small(D),corner_radius_medium(D),corner_radius_small(D),glyph_corner_radius(U),elevation_large(D),elevation_medium(D),icon_size_large(D),icon_size_medium(D),icon_size_small(D)],drawable[bg_bottom_nav(U),bg_dialog_rounded(U),bg_main_gradient(U),bg_progress_container(U),bg_stat_card(U),bg_status_chip(U),btn_download_bg(U),btn_update_bg(U),download_card_bg(D),game_card_bg(U),ic_app_icon(U),logo(U),ic_back(U),ic_cancel(U),ic_check_circle(D),ic_clear_cache(U),ic_close(D),ic_dashboard(D),ic_download(U),ic_empty(D),ic_error(D),ic_glyph_lock(U),ic_home(U),ic_key(D),ic_launcher_background(D),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_logout(D),ic_nav_cloud(D),ic_nav_home(D),ic_nav_profile(D),ic_nav_settings(D),ic_paste(D),ic_pause(D),ic_pubg_back(U),ic_pubg_clear_data(U),ic_pubg_download(U),ic_pubg_help(U),ic_pubg_logout(U),ic_pubg_mobile_global_logo(U),ic_pubg_mobile_kr_logo(U),ic_pubg_mobile_tw_logo(U),ic_pubg_mobile_vn_logo(U),ic_pubg_profile(U),ic_pubg_settings(U),ic_reset(U),ic_settings(U),logo_splash(D),pubg_status_brutal(D),pubg_status_safe(U),splash_background(D),splash_branding(U),splash_gradient(D),splash_gradient_modern(U),tab_selected_bg(U),tab_unselected_bg(U),ic_pubg_global_vector(R)],id[app_bar_layout(D),toolbar(U),search_layout(D),et_search(D),chip_group_filters(D),chip_filter_all(D),chip_filter_latest(D),chip_sort_date(D),chip_sort_size(D),swipe_refresh(D),progressLoading(D),layout_empty_state(D),rvReleases(D),cardDownloadInfo(D),tvApkSize(D),tvObbSize(D),tvTotalSize(D),btnDownload(D),cardDownloadProgress(D),tv_download_status(D),progressDownload(D),tv_download_percentage(D),animationDownloadComplete(D),btnCancelDownload(D),tvNoReleases(D),btn_back(U),tv_status(U),progress_bar(U),tv_progress(U),tv_storage_info(U),btn_download_global(U),btn_download_kr(U),btn_download_tw(U),btn_download_vn(U),btn_test_storage(U),btn_cleanup(U),tab_global(D),tab_kr(D),tab_tw(D),tab_vn(D),card_global(D),btn_update_global(D),card_kr(D),btn_update_kr(D),card_tw(D),btn_update_tw(D),card_vn(D),btn_update_vn(D),action_logout(D),action_clear_data(D),action_help(D),logoContainer(U),logoImage(D),appNameText(U),textInputLayout(U),editLicenseKey(U),checkboxRemember(U),checkboxAutoLogin(U),btnLogin(U),progressBar(U),versionText(U),drawer_layout(D),card_target_app(D),spinner_target_app(D),btn_scan_offsets(D),tv_available_patches(D),rv_patches(D),shimmer_layout(D),layout_empty(D),bottom_navigation(D),nav_view(D),appBarLayout(D),header_title(D),header_subtitle(D),rv_app_versions(D),layout_clear_cache(D),layout_reset_config(D),switch_stealth(D),switch_auto_login(D),btn_logout(D),subtitleText(U),progressContainer(U),loadingText(U),glyphView(U),tapText(U),iv_game_icon(U),tv_download_title(U),tv_file_name(U),tv_speed(U),tv_file_size(U),tv_eta(U),btn_cancel(U),btn_pause(U),app_icon(U),app_name(U),status(U),btn_action(U),app_version(U),bullet_point(U),app_description(U),download_progress_container(U),progress_bar_inline(U),tv_progress_inline(U),tv_speed_inline(U),tv_eta_inline(U),card_download_progress(D),progress_circular(D),chip_download_status(D),progress_download(D),tv_download_speed(D),tv_downloaded_size(D),btn_pause_resume(D),btn_cancel_download(D),tv_license_info(D),nav_home(U),nav_downloads(U),nav_settings(U),nav_dashboard(D),nav_download(D),nav_logout(D)],layout[activity_download(D),item_release(E),activity_download_test(U),activity_fresh_download(U),activity_login(U),activity_main(D),nav_header(D),activity_main_loader(U),activity_settings(U),activity_splash(U),activity_tap_unlock(U),dialog_download_progress(U),item_app_version(U),layout_download_progress_modern(D)],menu[bottom_navigation_menu(U),drawer_menu(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],raw[download_complete(D),success_animation(D)],string[app_name(U),download_title(D),search_releases(D),all(D),latest(D),sort_by_date(D),sort_by_size(D),appbar_scrolling_view_behavior(R),no_releases_available(D),pull_down_to_refresh_or_check_your_connection(D),download_patches(D),icon_search(D),downloading(D),cancel_download(D),bearmod_loader(U),global(U),kr(U),tw(U),vn(U),pubg_mobile_global(U),version_3_8_0_64bit(U),safe(U),download_size_1_08_gb(U),update(U),pubg_mobile_kr(U),pubg_mobile_tw(U),pubg_mobile_vn(U),clear_data(U),support(U),app_version(U),dashboard_title(D),target_app(D),scan_offsets(D),no_results_found(D),available_patches(U),select_version(U),settings_title(U),app_settings(U),clear_cache(U),reset_config(U),patch_settings(U),toggle_stealth(U),auto_login(U),about(U),version_info(U),about_description(U),logout(U),up_to_date(U),download_progress(D),downloading_status(D),downloaded(D),eta(D),pause(D),app_logo(D),license_valid_until(D),nav_home(U),nav_downloads(U),nav_settings(U),login_subtitle(D),license_key(D),enter_license_key(D),invalid_license_key_format(D),remember_me(D),login(D),login_success(D),login_error(D),registration_date(D),days_remaining(D),keyauth_init_warning(D),keyauth_init_failed(D),update_available(D),not_installed(D),game_version(D),updated(D),available_releases(D),obb_size(D),total_size(D),downloading_detailed(D),download_complete(D),download_complete_path(D),download_failed(D),download_cancelled(D),download_in_progress(D),continue_download(D),available(D),completed(D),failed(D),filter_by_version(D),clear_filters(D),notification_settings(D),dark_mode(D),language(D),cache_cleared(U),config_reset(U),clear_cache_confirm(U),reset_config_confirm(U),logout_confirm(U),ok(U),cancel(U),retry(D),loading(D),error(D),success(D),warning(D),info(D),exit(D),continue_anyway(D),navigation_drawer_open(D),navigation_drawer_close(D),sync_error(D),download(D),pubg_global(D),pubg_kr(D),pubg_tw(D),pubg_vn(D),version_format(D),global_version_desc(D),kr_version_desc(D),tw_version_desc(D),vn_version_desc(D),game_icon(D),game_version_format(D),release_date_format(D),file_size_format(D),resume(D),username(D),password(D),forgot_password(D),register(D),login_failed(D),invalid_credentials(D),network_error(D),unknown_error(D),theme(D),theme_light(D),theme_dark(D),theme_system(D),notifications(D),auto_update(D),version(D),licenses(D),privacy_policy(D),terms_of_service(D),downloads(D),no_downloads(D),delete(D),paused(D),cancelled(D),confirm(D),delete_download(D),delete_all_downloads(D),yes(D),no(D),icon_filter(D),icon_sort(D)],style[Theme_BearLoader(U),Theme_BearLoader_NoActionBar(U),TextAppearance_Material3_HeadlineMedium(E),Widget_Material3_TextInputLayout_OutlinedBox(R),Widget_Material3_Chip_Filter(E),TextAppearance_Material3_HeadlineSmall(E),TextAppearance_Material3_BodyMedium(E),Widget_Material3_CardView_Elevated(E),TextAppearance_Material3_TitleMedium(R),Widget_BearLoader_Button(U),TextAppearance_Material3_BodyLarge(E),TextAppearance_Material3_LabelLarge(E),Widget_BearLoader_Button_Secondary(D),Widget_Material3_Button_OutlinedButton(R),Widget_Material3_Button_TonalButton(R),TextAppearance_Material3_LabelSmall(R),TextAppearance_Material3_BodySmall(R),Widget_Material3_Chip_Assist(E),TextAppearance_Material3_LabelMedium(E),Widget_BearLoader_Button_Tonal(D),ThemeOverlay_AppCompat_Dark(E),TextAppearance_AppCompat_Body1(E),Theme_Material3_DayNight_NoActionBar(R),ShapeAppearance_BearLoader_SmallComponent(U),ShapeAppearance_BearLoader_MediumComponent(U),ShapeAppearance_BearLoader_LargeComponent(U),Theme(R),Theme_BearLoader_Splash(D),ShapeAppearance_Material3_SmallComponent(R),ShapeAppearance_Material3_MediumComponent(R),ShapeAppearance_Material3_LargeComponent(R),Animation_BearLoader_Activity(D),Theme_BearLoader_Dialog(D),ThemeOverlay_Material3_Dialog_Alert(E),Widget_Material3_Button(R),Widget_BearLoader_Button_Small(D),Widget_BearLoader_Button_Icon(D),Widget_Material3_Button_IconButton(E),TextAppearance_BearLoader_Headline6(D),TextAppearance_BearLoader_Body1(D),TextAppearance_BearLoader_Body2(D),TextAppearance_BearLoader_Caption(D),Theme_BearLoader_AppBarOverlay(D),ThemeOverlay_Material3_Dark_ActionBar(E),Theme_BearMod(D),Theme_Material3_DayNight(R)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U),file_paths(U)];b^c^d,44^e,45^e^f,47^10^f,48^11^f,49^12,4e^13^4f,50^13,51^6,53^c,54^6,55^c,57^d,5d^5e,5f^c,64^7,65^6,71^c,73^4f,76^14^4f,77^14^105,f5^14^8^50^10a^1a6^15^10b^10^e^56^1a7^d^10c^16^1a8^10d^10e^72^10f^110^c^111^1a9^112^1aa^f6^1ab^113^114^1ac^1ad^115^1ae^1af^116^1b0,f7^9^50^4d^4a^4b,f8^9^66^117^70^68^49^6f^7a^6b^118^7b^6c^119^6d^11a^6e^11b^4d^11c^11d^75^11e^11f^4a^4b^120^121^122^123^a^6a^67^124^69^125,f9^79^17^4f^13^1a7^c^126,fa^1a5^c^8^127^13^14^110^18^128^15^129^1ad^bb^be^17^12a^57^d^19^103^1a^fb^104,fb^1a^1b8^13e^73^109^1b9^13f,fc^46^12b^15^12c^d^110^44^b^1b^103,fd^14^1c^8^50^12d^1d^7e^12e^1e^1a^a^12f^53^15^1f^130^71^131^132^133^134^109^135^136^137^1ad,fe^79^19^b2^109^4f^13^cd^b0^ce^b9^126,ff^20^59^d0,100^45^6b^15^d^47^1a^21^48^1b1^1b2,101^30^31^e^32^33^34^7c^1ac^15^dc^da^49^1b3^22^dd^12^138^1b4^d^db^de^df^e0^1a^21^23^56^1b2,102^1a^12^1ab^139^1a9^15^13a^56^1b5^1aa^1e^24^e^1b6^1f^21^14^1ac^25^13b^1b3^20^13c^d^13d^65^1b7^116^54^1b0,103^5a^140^56^141^72^142,104^55^127^56^10a^72^12d^5f^137,105^13^4e,106^13^4e,1a4^1ba^c^26^13^16^14^28^29^20^2d^10^15^17^d^5d^77^1bb^1bc^1bd^1d1^21^18^1a,1a5^1a4^1be,1ad^1c6^13^c^16,1b0^1b1^c^16,1b7^1b2^c^22^16,1bb^1c0,1bc^1c1,1bd^1c2,1bf^1a5^14,1c3^3^4^2^5,1c4^1c5^c^21^14^15,1c7^1c6^13^c^16,1c8^1c9^c^16,1ca^1a6^35^15,1cb^1ae^36^15,1cc^1aa^37^d,1cd^1b3^38^d,1ce^1cf,1d0^1ba^c^27^1e^28^2b^2c^19^17^1d^14;;;"/>
    </map>

</incidents>
