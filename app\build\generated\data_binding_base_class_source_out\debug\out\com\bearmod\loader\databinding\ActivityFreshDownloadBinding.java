// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFreshDownloadBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout downloadApk;

  @NonNull
  public final TextView downloadCancel;

  @NonNull
  public final LinearLayout downloadFull;

  @NonNull
  public final LinearLayout downloadObb;

  @NonNull
  public final TextView downloadStatus;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final LinearLayout progressSection;

  @NonNull
  public final TextView progressSize;

  @NonNull
  public final TextView progressText;

  private ActivityFreshDownloadBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout downloadApk, @NonNull TextView downloadCancel,
      @NonNull LinearLayout downloadFull, @NonNull LinearLayout downloadObb,
      @NonNull TextView downloadStatus, @NonNull ProgressBar progressBar,
      @NonNull LinearLayout progressSection, @NonNull TextView progressSize,
      @NonNull TextView progressText) {
    this.rootView = rootView;
    this.downloadApk = downloadApk;
    this.downloadCancel = downloadCancel;
    this.downloadFull = downloadFull;
    this.downloadObb = downloadObb;
    this.downloadStatus = downloadStatus;
    this.progressBar = progressBar;
    this.progressSection = progressSection;
    this.progressSize = progressSize;
    this.progressText = progressText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFreshDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFreshDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_fresh_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFreshDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.download_apk;
      LinearLayout downloadApk = ViewBindings.findChildViewById(rootView, id);
      if (downloadApk == null) {
        break missingId;
      }

      id = R.id.download_cancel;
      TextView downloadCancel = ViewBindings.findChildViewById(rootView, id);
      if (downloadCancel == null) {
        break missingId;
      }

      id = R.id.download_full;
      LinearLayout downloadFull = ViewBindings.findChildViewById(rootView, id);
      if (downloadFull == null) {
        break missingId;
      }

      id = R.id.download_obb;
      LinearLayout downloadObb = ViewBindings.findChildViewById(rootView, id);
      if (downloadObb == null) {
        break missingId;
      }

      id = R.id.download_status;
      TextView downloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (downloadStatus == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_section;
      LinearLayout progressSection = ViewBindings.findChildViewById(rootView, id);
      if (progressSection == null) {
        break missingId;
      }

      id = R.id.progress_size;
      TextView progressSize = ViewBindings.findChildViewById(rootView, id);
      if (progressSize == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      return new ActivityFreshDownloadBinding((LinearLayout) rootView, downloadApk, downloadCancel,
          downloadFull, downloadObb, downloadStatus, progressBar, progressSection, progressSize,
          progressText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
