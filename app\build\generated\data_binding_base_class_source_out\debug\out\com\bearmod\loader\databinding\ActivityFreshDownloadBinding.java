// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFreshDownloadBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout actionClearData;

  @NonNull
  public final LinearLayout actionHelp;

  @NonNull
  public final LinearLayout actionLogout;

  @NonNull
  public final ImageView btnBack;

  @NonNull
  public final Button btnDownloadGlobal;

  @NonNull
  public final Button btnDownloadKr;

  @NonNull
  public final Button btnDownloadTw;

  @NonNull
  public final Button btnDownloadVn;

  @NonNull
  public final Button btnUpdateGlobal;

  @NonNull
  public final Button btnUpdateKr;

  @NonNull
  public final Button btnUpdateTw;

  @NonNull
  public final Button btnUpdateVn;

  @NonNull
  public final LinearLayout cardGlobal;

  @NonNull
  public final LinearLayout cardKr;

  @NonNull
  public final LinearLayout cardTw;

  @NonNull
  public final LinearLayout cardVn;

  @NonNull
  public final LinearLayout tabGlobal;

  @NonNull
  public final LinearLayout tabKr;

  @NonNull
  public final LinearLayout tabTw;

  @NonNull
  public final LinearLayout tabVn;

  private ActivityFreshDownloadBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout actionClearData, @NonNull LinearLayout actionHelp,
      @NonNull LinearLayout actionLogout, @NonNull ImageView btnBack,
      @NonNull Button btnDownloadGlobal, @NonNull Button btnDownloadKr,
      @NonNull Button btnDownloadTw, @NonNull Button btnDownloadVn, @NonNull Button btnUpdateGlobal,
      @NonNull Button btnUpdateKr, @NonNull Button btnUpdateTw, @NonNull Button btnUpdateVn,
      @NonNull LinearLayout cardGlobal, @NonNull LinearLayout cardKr, @NonNull LinearLayout cardTw,
      @NonNull LinearLayout cardVn, @NonNull LinearLayout tabGlobal, @NonNull LinearLayout tabKr,
      @NonNull LinearLayout tabTw, @NonNull LinearLayout tabVn) {
    this.rootView = rootView;
    this.actionClearData = actionClearData;
    this.actionHelp = actionHelp;
    this.actionLogout = actionLogout;
    this.btnBack = btnBack;
    this.btnDownloadGlobal = btnDownloadGlobal;
    this.btnDownloadKr = btnDownloadKr;
    this.btnDownloadTw = btnDownloadTw;
    this.btnDownloadVn = btnDownloadVn;
    this.btnUpdateGlobal = btnUpdateGlobal;
    this.btnUpdateKr = btnUpdateKr;
    this.btnUpdateTw = btnUpdateTw;
    this.btnUpdateVn = btnUpdateVn;
    this.cardGlobal = cardGlobal;
    this.cardKr = cardKr;
    this.cardTw = cardTw;
    this.cardVn = cardVn;
    this.tabGlobal = tabGlobal;
    this.tabKr = tabKr;
    this.tabTw = tabTw;
    this.tabVn = tabVn;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFreshDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFreshDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_fresh_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFreshDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.action_clear_data;
      LinearLayout actionClearData = ViewBindings.findChildViewById(rootView, id);
      if (actionClearData == null) {
        break missingId;
      }

      id = R.id.action_help;
      LinearLayout actionHelp = ViewBindings.findChildViewById(rootView, id);
      if (actionHelp == null) {
        break missingId;
      }

      id = R.id.action_logout;
      LinearLayout actionLogout = ViewBindings.findChildViewById(rootView, id);
      if (actionLogout == null) {
        break missingId;
      }

      id = R.id.btn_back;
      ImageView btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_download_global;
      Button btnDownloadGlobal = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadGlobal == null) {
        break missingId;
      }

      id = R.id.btn_download_kr;
      Button btnDownloadKr = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadKr == null) {
        break missingId;
      }

      id = R.id.btn_download_tw;
      Button btnDownloadTw = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadTw == null) {
        break missingId;
      }

      id = R.id.btn_download_vn;
      Button btnDownloadVn = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadVn == null) {
        break missingId;
      }

      id = R.id.btn_update_global;
      Button btnUpdateGlobal = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdateGlobal == null) {
        break missingId;
      }

      id = R.id.btn_update_kr;
      Button btnUpdateKr = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdateKr == null) {
        break missingId;
      }

      id = R.id.btn_update_tw;
      Button btnUpdateTw = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdateTw == null) {
        break missingId;
      }

      id = R.id.btn_update_vn;
      Button btnUpdateVn = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdateVn == null) {
        break missingId;
      }

      id = R.id.card_global;
      LinearLayout cardGlobal = ViewBindings.findChildViewById(rootView, id);
      if (cardGlobal == null) {
        break missingId;
      }

      id = R.id.card_kr;
      LinearLayout cardKr = ViewBindings.findChildViewById(rootView, id);
      if (cardKr == null) {
        break missingId;
      }

      id = R.id.card_tw;
      LinearLayout cardTw = ViewBindings.findChildViewById(rootView, id);
      if (cardTw == null) {
        break missingId;
      }

      id = R.id.card_vn;
      LinearLayout cardVn = ViewBindings.findChildViewById(rootView, id);
      if (cardVn == null) {
        break missingId;
      }

      id = R.id.tab_global;
      LinearLayout tabGlobal = ViewBindings.findChildViewById(rootView, id);
      if (tabGlobal == null) {
        break missingId;
      }

      id = R.id.tab_kr;
      LinearLayout tabKr = ViewBindings.findChildViewById(rootView, id);
      if (tabKr == null) {
        break missingId;
      }

      id = R.id.tab_tw;
      LinearLayout tabTw = ViewBindings.findChildViewById(rootView, id);
      if (tabTw == null) {
        break missingId;
      }

      id = R.id.tab_vn;
      LinearLayout tabVn = ViewBindings.findChildViewById(rootView, id);
      if (tabVn == null) {
        break missingId;
      }

      return new ActivityFreshDownloadBinding((LinearLayout) rootView, actionClearData, actionHelp,
          actionLogout, btnBack, btnDownloadGlobal, btnDownloadKr, btnDownloadTw, btnDownloadVn,
          btnUpdateGlobal, btnUpdateKr, btnUpdateTw, btnUpdateVn, cardGlobal, cardKr, cardTw,
          cardVn, tabGlobal, tabKr, tabTw, tabVn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
