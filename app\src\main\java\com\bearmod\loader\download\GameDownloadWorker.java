package com.bearmod.loader.download;

import android.content.Context;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.work.Data;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Worker class for downloading game files (APK/OBB) in the background
 */
public class GameDownloadWorker extends Worker {
    
    private static final String TAG = "GameDownloadWorker";
    private static final int BUFFER_SIZE = 8192;
    private static final int PROGRESS_UPDATE_INTERVAL = 1000; // Update every 1 second
    
    private static DownloadProgressListener progressListener;
    private final AtomicBoolean isCancelled = new AtomicBoolean(false);
    
    public GameDownloadWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }
    
    /**
     * Set progress listener for download updates
     * @param listener Progress listener
     */
    public static void setProgressListener(DownloadProgressListener listener) {
        progressListener = listener;
    }
    
    @NonNull
    @Override
    public Result doWork() {
        // Get input data
        String downloadUrl = getInputData().getString("downloadUrl");
        String fileName = getInputData().getString("fileName");
        double totalSizeMB = getInputData().getDouble("totalSizeMB", 0);
        String downloadType = getInputData().getString("downloadType");
        
        if (downloadUrl == null || fileName == null) {
            return Result.failure(new Data.Builder()
                    .putString("error", "Missing download URL or filename")
                    .build());
        }
        
        Log.d(TAG, "Starting download: " + fileName + " (" + downloadType + ")");
        
        try {
            // Determine download directory based on file type
            File downloadDir = getDownloadDirectory(downloadType);
            if (!downloadDir.exists()) {
                if (!downloadDir.mkdirs()) {
                    Log.e(TAG, "Failed to create download directory: " + downloadDir.getAbsolutePath());
                    return Result.failure(new Data.Builder()
                            .putString("error", "Failed to create download directory")
                            .build());
                }
            }
            
            // Create output file
            File outputFile = new File(downloadDir, fileName);
            
            // Download the file
            boolean success = downloadFile(downloadUrl, outputFile, totalSizeMB, downloadType);
            
            if (isCancelled.get()) {
                // Delete partial file if download was cancelled
                if (outputFile.exists()) {
                    outputFile.delete();
                }
                return Result.failure(new Data.Builder()
                        .putString("error", "Download cancelled")
                        .build());
            }
            
            if (success) {
                Log.d(TAG, "Download completed: " + outputFile.getAbsolutePath());
                return Result.success(new Data.Builder()
                        .putString("filePath", outputFile.getAbsolutePath())
                        .build());
            } else {
                return Result.failure(new Data.Builder()
                        .putString("error", "Download failed")
                        .build());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Download error", e);
            return Result.failure(new Data.Builder()
                    .putString("error", "Download error: " + e.getMessage())
                    .build());
        }
    }
    
    /**
     * Get appropriate download directory based on download type
     * @param downloadType Type of download
     * @return Download directory
     */
    private File getDownloadDirectory(String downloadType) {
        switch (downloadType) {
            case "APK_ONLY":
            case "APK_AND_OBB":
                // APK files go to Downloads folder
                return new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "BearMod");
            case "OBB_ONLY":
                // OBB files go to Android/obb directory
                return new File(Environment.getExternalStorageDirectory(), "Android/obb/com.pubg.krmobile");
            default:
                // Default to app's external files directory
                return new File(getApplicationContext().getExternalFilesDir(null), "downloads");
        }
    }
    
    /**
     * Download file from URL with progress tracking
     * @param downloadUrl URL to download from
     * @param outputFile Output file
     * @param expectedSizeMB Expected file size in MB
     * @param downloadType Type of download
     * @return true if successful, false otherwise
     */
    private boolean downloadFile(String downloadUrl, File outputFile, double expectedSizeMB, String downloadType) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        FileOutputStream outputStream = null;
        
        try {
            // Create connection
            URL url = new URL(downloadUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(30000); // 30 seconds
            connection.connect();
            
            // Check response code
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                Log.e(TAG, "HTTP error: " + responseCode);
                return false;
            }
            
            // Get file size
            long fileSize = connection.getContentLength();
            if (fileSize <= 0) {
                fileSize = (long) (expectedSizeMB * 1024 * 1024); // Convert MB to bytes
                Log.w(TAG, "Server didn't provide content length, using expected size: " + expectedSizeMB + " MB");
            } else {
                Log.d(TAG, "Actual file size from server: " + (fileSize / (1024.0 * 1024.0)) + " MB");
            }
            
            // Create streams
            inputStream = connection.getInputStream();
            outputStream = new FileOutputStream(outputFile);
            
            // Download with progress tracking
            byte[] buffer = new byte[BUFFER_SIZE];
            long totalBytesRead = 0;
            int bytesRead;
            long lastProgressUpdate = 0;
            long startTime = System.currentTimeMillis();
            
            while ((bytesRead = inputStream.read(buffer)) != -1 && !isCancelled.get()) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
                
                // Update progress periodically
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastProgressUpdate >= PROGRESS_UPDATE_INTERVAL) {
                    updateProgress(totalBytesRead, fileSize, startTime, currentTime);
                    lastProgressUpdate = currentTime;
                }
            }
            
            // Final progress update
            if (!isCancelled.get()) {
                updateProgress(totalBytesRead, fileSize, startTime, System.currentTimeMillis());
            }
            
            return !isCancelled.get() && totalBytesRead > 0;
            
        } catch (IOException e) {
            Log.e(TAG, "Download failed", e);
            return false;
        } finally {
            // Close streams and connection
            try {
                if (outputStream != null) outputStream.close();
                if (inputStream != null) inputStream.close();
                if (connection != null) connection.disconnect();
            } catch (IOException e) {
                Log.e(TAG, "Error closing streams", e);
            }
        }
    }
    
    /**
     * Update download progress
     * @param bytesDownloaded Bytes downloaded so far
     * @param totalBytes Total bytes to download
     * @param startTime Download start time
     * @param currentTime Current time
     */
    private void updateProgress(long bytesDownloaded, long totalBytes, long startTime, long currentTime) {
        if (progressListener == null) return;
        
        // Calculate progress percentage
        int progress = totalBytes > 0 ? (int) ((bytesDownloaded * 100) / totalBytes) : 0;
        
        // Calculate sizes in MB
        double downloadedMB = bytesDownloaded / (1024.0 * 1024.0);
        double totalSizeMB = totalBytes / (1024.0 * 1024.0);
        
        // Calculate speed in MB/s
        long elapsedTime = currentTime - startTime;
        double speedMBps = elapsedTime > 0 ? downloadedMB / (elapsedTime / 1000.0) : 0;
        
        // Calculate ETA
        int etaMinutes = 0;
        int etaSeconds = 0;
        if (speedMBps > 0 && totalSizeMB > downloadedMB) {
            double remainingMB = totalSizeMB - downloadedMB;
            double etaTotalSeconds = remainingMB / speedMBps;
            etaMinutes = (int) (etaTotalSeconds / 60);
            etaSeconds = (int) (etaTotalSeconds % 60);
        }
        
        // Update progress
        progressListener.onProgressUpdate(progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds);
    }
    
    @Override
    public void onStopped() {
        super.onStopped();
        isCancelled.set(true);
        Log.d(TAG, "Download worker stopped");
    }
    
    /**
     * Progress listener interface
     */
    public interface DownloadProgressListener {
        void onProgressUpdate(int progress, double downloadedMB, double totalSizeMB,
                             double speedMBps, int etaMinutes, int etaSeconds);
    }
}
