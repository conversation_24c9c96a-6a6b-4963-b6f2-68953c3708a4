<libraries>
  <library
      name="androidx.databinding:viewbinding:8.10.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\be28ce63bac2cd01f6d1c391dfa6fe9d\transformed\viewbinding-8.10.1\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.10.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\be28ce63bac2cd01f6d1c391dfa6fe9d\transformed\viewbinding-8.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\425d4b0539cae6d21a1eeed5036978e4\transformed\navigation-runtime-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\425d4b0539cae6d21a1eeed5036978e4\transformed\navigation-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\318ade96510d95484c2030e5b9d0a298\transformed\navigation-common-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\318ade96510d95484c2030e5b9d0a298\transformed\navigation-common-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\023225165cd912e87d52c63a0aa23f5d\transformed\navigation-fragment-2.9.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\023225165cd912e87d52c63a0aa23f5d\transformed\navigation-fragment-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8d1253ec0fc93acd53c5fd46d4f60417\transformed\navigation-ui-2.9.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8d1253ec0fc93acd53c5fd46d4f60417\transformed\navigation-ui-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.6.6@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.6.6"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\10e9d34d0303215234c3e3878d1883b0\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\10e9d34d0303215234c3e3878d1883b0\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c331959512d9a33669ba0caa36c41fa5\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c331959512d9a33669ba0caa36c41fa5\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.10.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c613a16b68334527942b1cc594b4b5c7\transformed\work-runtime-ktx-2.10.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.10.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c613a16b68334527942b1cc594b4b5c7\transformed\work-runtime-ktx-2.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.10.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.10.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3955b6ccf628507c33f20f62c1c095e\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3955b6ccf628507c33f20f62c1c095e\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9e80fb7ee9d1ca535c9b4dbf890423b5\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9e80fb7ee9d1ca535c9b4dbf890423b5\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\80d596ff6c7c808027613582bf3daad8\transformed\viewpager2-1.1.0-beta02\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0-beta02"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\80d596ff6c7c808027613582bf3daad8\transformed\viewpager2-1.1.0-beta02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.8.7@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\404707ec20de0d8cde58d9d9cd0236bc\transformed\fragment-ktx-1.8.7\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.8.7"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\404707ec20de0d8cde58d9d9cd0236bc\transformed\fragment-ktx-1.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.8.7@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aeb794549544d5c7523f1b0f200eabbb\transformed\fragment-1.8.7\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.8.7"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aeb794549544d5c7523f1b0f200eabbb\transformed\fragment-1.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\316e9950fbf2ca26b95f45c9ba2d689e\transformed\activity-ktx-1.8.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\316e9950fbf2ca26b95f45c9ba2d689e\transformed\activity-ktx-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\15f42126263e7a97448eae59481a2719\transformed\activity-1.8.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\15f42126263e7a97448eae59481a2719\transformed\activity-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83a97e62e224813103a23de5ca3b904e\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83a97e62e224813103a23de5ca3b904e\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.4.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c5a298e53c344c649a9b9b36873b545d\transformed\recyclerview-1.4.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.4.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c5a298e53c344c649a9b9b36873b545d\transformed\recyclerview-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96e810d4b42cd7e0b024ad1e271cdd1b\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96e810d4b42cd7e0b024ad1e271cdd1b\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec2fd796047ad88361eadb9f5f0cd9a4\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec2fd796047ad88361eadb9f5f0cd9a4\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b10f86cf90f289cc114b9070b751b65\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b10f86cf90f289cc114b9070b751b65\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ef72eb30f31ff399b2d506cdbbf7d88\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ef72eb30f31ff399b2d506cdbbf7d88\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ebb0eddcd9530b78e5e381e120ee35c8\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ebb0eddcd9530b78e5e381e120ee35c8\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fcb99e4be41ac499d148d39068a0b83\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fcb99e4be41ac499d148d39068a0b83\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\76a00043e874c48a1dc77c287bc3408b\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\76a00043e874c48a1dc77c287bc3408b\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e6caa8a463050f6cc448924f7e871fb4\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e6caa8a463050f6cc448924f7e871fb4\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\49e128596586890b5e80ac1c291cda1a\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\49e128596586890b5e80ac1c291cda1a\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a3e5a162c52ed8d94cf025e7305c0638\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a3e5a162c52ed8d94cf025e7305c0638\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4eca2ce34e099cac84914b9887a72d0f\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4eca2ce34e099cac84914b9887a72d0f\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f23d3013b7dd70b3efd40d31fe472652\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f23d3013b7dd70b3efd40d31fe472652\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\65cce4ad8d326a359666f63f17960852\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\65cce4ad8d326a359666f63f17960852\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\87facd601e715e44da759569aba39b55\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\87facd601e715e44da759569aba39b55\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\badf863738da4cec4ec347369b8fed6d\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\badf863738da4cec4ec347369b8fed6d\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d79b6b71ed43a1fb3436d66eab5af176\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d79b6b71ed43a1fb3436d66eab5af176\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fda4875abbd57cf0e40ab62624686879\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fda4875abbd57cf0e40ab62624686879\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7eec7d8268129de76a0be6a0757a7ef5\transformed\exoplayer-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7eec7d8268129de76a0be6a0757a7ef5\transformed\exoplayer-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a4fb1c2e1fb11023fe27b3d0a699c4d7\transformed\exoplayer-ui-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a4fb1c2e1fb11023fe27b3d0a699c4d7\transformed\exoplayer-ui-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.6.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f47e9a9ad0b993acc6a49b69c0511798\transformed\media-1.6.0\jars\classes.jar"
      resolved="androidx.media:media:1.6.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f47e9a9ad0b993acc6a49b69c0511798\transformed\media-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd377c14ba1b94a88b1b8ef27d698d2\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd377c14ba1b94a88b1b8ef27d698d2\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6839d992cb28d16b266ea057f71823c3\transformed\lifecycle-livedata-core-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6839d992cb28d16b266ea057f71823c3\transformed\lifecycle-livedata-core-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b760527796b8626827aa3edd74d0c222\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b760527796b8626827aa3edd74d0c222\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12c8e7255809dc3614e072d49b125866\transformed\lifecycle-livedata-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12c8e7255809dc3614e072d49b125866\transformed\lifecycle-livedata-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\59078c7ce5438c1d077427388eebc0f5\transformed\lifecycle-viewmodel-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\59078c7ce5438c1d077427388eebc0f5\transformed\lifecycle-viewmodel-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:3.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\3.0.0\f267e39336e822e2abb835818606986a96b4d5aa\converter-gson-3.0.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:3.0.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:3.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\3.0.0\c0cdf6d243c5187732134129fda05a74f9197874\retrofit-3.0.0.jar"
      resolved="com.squareup.retrofit2:retrofit:3.0.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.google.code.gson:gson:2.13.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.13.1\853ce06c11316b33a8eae5e9095da096a9528b8f\gson-2.13.1.jar"
      resolved="com.google.code.gson:gson:2.13.1"/>
  <library
      name="androidx.room:room-runtime-android:2.7.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\jars\classes.jar"
      resolved="androidx.room:room-runtime-android:2.7.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common-jvm:2.7.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.room\room-common-jvm\2.7.1\46a9845444d0b849131baeb85bfe4051828261ad\room-common-jvm-2.7.1.jar"
      resolved="androidx.room:room-common-jvm:2.7.1"/>
  <library
      name="androidx.room:room-ktx:2.7.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b32fefe702eea1aedd00bcc3c93a8df0\transformed\room-ktx-2.7.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.7.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b32fefe702eea1aedd00bcc3c93a8df0\transformed\room-ktx-2.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.shimmer:shimmer:0.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d1a7c107f2d1734124d9e52c4ba2a69b\transformed\shimmer-0.5.0\jars\classes.jar"
      resolved="com.facebook.shimmer:shimmer:0.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d1a7c107f2d1734124d9e52c4ba2a69b\transformed\shimmer-0.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.json:json:20250517@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.json\json\20250517\d67181bbd819ccceb929b580a4e2fcb0c8b17cd8\json-20250517.jar"
      resolved="org.json:json:20250517"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\444dd2219c4910d2620f2f5771ca00ff\transformed\security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\444dd2219c4910d2620f2f5771ca00ff\transformed\security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.2.0\6a1dfd69226e148898410721f4b14315343d1429\concurrent-futures-1.2.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.2.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:33.4.8-android@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.4.8-android\cb7510a733595f02fe633ffff62d40cfc3056e9\guava-33.4.8-android.jar"
      resolved="com.google.guava:guava:33.4.8-android"/>
  <library
      name="org.java-websocket:Java-WebSocket:1.6.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.java-websocket\Java-WebSocket\1.6.0\52d7f6dc1b06607d4d12e438c840c25f1a0bb854\Java-WebSocket-1.6.0.jar"
      resolved="org.java-websocket:Java-WebSocket:1.6.0"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9a35b2465b3db4d554dff592a91a2e2d\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9a35b2465b3db4d554dff592a91a2e2d\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6a6713bc90828ded954d9ff58d670318\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6a6713bc90828ded954d9ff58d670318\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2b553a0a6797c3149764651761d5c1ff\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2b553a0a6797c3149764651761d5c1ff\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\40503dca37aff8c7683d2e8fe94d5a34\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\40503dca37aff8c7683d2e8fe94d5a34\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5741c001d0effa857fe45a66a8490590\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5741c001d0effa857fe45a66a8490590\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-ktx:1.4.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.2.jar"
      resolved="androidx.collection:collection-ktx:1.4.2"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\830ea6a84521a856ce8413b11e7f8c7f\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\830ea6a84521a856ce8413b11e7f8c7f\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\abcc1de915a86fc02ac40bf72d886657\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\abcc1de915a86fc02ac40bf72d886657\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dc89b11354378e8afc26a40b32acfa4\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dc89b11354378e8afc26a40b32acfa4\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\861b36ff44440a93b4355f1b6f46b88f\transformed\sqlite-framework-release\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework-android:2.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\861b36ff44440a93b4355f1b6f46b88f\transformed\sqlite-framework-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-android:2.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\647611225c4245b1ce9c8ab5245e6ae0\transformed\sqlite-release\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-android:2.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\647611225c4245b1ce9c8ab5245e6ae0\transformed\sqlite-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a9ea16a098136264d5d7f84e19d5f4e\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a9ea16a098136264d5d7f84e19d5f4e\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.21\97a0975aa19d925e109537af60eb46902920015c\kotlin-stdlib-2.1.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71633e66afbd02b6778236104ce9c5d5\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71633e66afbd02b6778236104ce9c5d5\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.38.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.38.0\fc0ae991433e8590ba51cd558421478318a74c8c\error_prone_annotations-2.38.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.38.0"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f02c364c67b6c43bc136e348f92531b7\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f02c364c67b6c43bc136e348f92531b7\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.3\aeaffd00d57023a2c947393ed251f0354f0985fc\failureaccess-1.0.3.jar"
      resolved="com.google.guava:failureaccess:1.0.3"/>
  <library
      name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\3.0.0\7399e65dd7e9ff3404f4535b2f017093bdb134c7\j2objc-annotations-3.0.0.jar"
      resolved="com.google.j2objc:j2objc-annotations:3.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\feb8895d496fe59f08aac8280ecac2b6\transformed\exoplayer-database-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\feb8895d496fe59f08aac8280ecac2b6\transformed\exoplayer-database-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ad2fb29818d2947c5dc114316131624\transformed\exoplayer-datasource-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ad2fb29818d2947c5dc114316131624\transformed\exoplayer-datasource-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e8236a4be1fa10a5b0da3dbe3dd15a76\transformed\exoplayer-decoder-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e8236a4be1fa10a5b0da3dbe3dd15a76\transformed\exoplayer-decoder-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ea2740499c19c3787ddc8e653559a738\transformed\exoplayer-extractor-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ea2740499c19c3787ddc8e653559a738\transformed\exoplayer-extractor-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc66b50e9957a4098b76f50ce9caf901\transformed\exoplayer-container-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-container:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc66b50e9957a4098b76f50ce9caf901\transformed\exoplayer-container-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\571379312175ad99b7875cc45ec3fd3c\transformed\exoplayer-dash-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\571379312175ad99b7875cc45ec3fd3c\transformed\exoplayer-dash-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0a72a8d5710ad5df6fd141087e41aaa4\transformed\exoplayer-hls-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0a72a8d5710ad5df6fd141087e41aaa4\transformed\exoplayer-hls-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4e33c84bc290cbdec91d52594f99f91b\transformed\exoplayer-rtsp-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4e33c84bc290cbdec91d52594f99f91b\transformed\exoplayer-rtsp-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f2f3e22b3f6dd62e1208b0ff6abdd97\transformed\exoplayer-smoothstreaming-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f2f3e22b3f6dd62e1208b0ff6abdd97\transformed\exoplayer-smoothstreaming-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.slf4j:slf4j-api:2.0.13@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.13\80229737f704b121a318bba5d5deacbcf395bc77\slf4j-api-2.0.13.jar"
      resolved="org.slf4j:slf4j-api:2.0.13"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f0069afdef34cfd1c0badff78d69cf97\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f0069afdef34cfd1c0badff78d69cf97\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\jars\classes.jar;D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\jars\classes.jar"
      resolved="androidx.window:window:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83368d95587c53536fc6edbb6c2429e5\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83368d95587c53536fc6edbb6c2429e5\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\14ffc38d372cf655808c8468b31bd7e1\transformed\lifecycle-service-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\14ffc38d372cf655808c8468b31bd7e1\transformed\lifecycle-service-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.crypto.tink:tink-android:1.8.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.8.0\bda82b49568d444a3b54773ca5aa487816473295\tink-android-1.8.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.8.0"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.2.0\13b49fdb58c74bdfb12eb131b5e22d6d09683e30\concurrent-futures-ktx-1.2.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.2.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.1.1\f7ab6170b99b9421bd4942846426ff820b552f7d\constraintlayout-core-1.1.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.1.1"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\206c606608d7079d1ef7e55169d704e1\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\206c606608d7079d1ef7e55169d704e1\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1983d72d5ce7a98ffd9c46b1f699795\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1983d72d5ce7a98ffd9c46b1f699795\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
