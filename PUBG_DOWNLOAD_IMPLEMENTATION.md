# PUBG Mobile Download Implementation

## Overview

This implementation provides a complete PUBG Mobile download system using KeyAuth for secure file distribution. The system supports downloading APK and OBB files for different PUBG Mobile regions (Global, KR, TW, VN) with real-time progress tracking, file integrity verification, and automatic installation.

## Key Features

### 1. **KeyAuth Integration**
- Secure file downloads using KeyAuth file system
- File ID-based downloads with authentication
- Support for large file downloads (APK + OBB)

### 2. **Multi-Region Support**
- **Global**: `com.tencent.ig`
- **Korea (KR)**: `com.pubg.krmobile`
- **Taiwan (TW)**: `com.rekoo.pubgm`
- **Vietnam (VN)**: `com.vng.pubgmobile`

### 3. **Download Types**
- **APK Only**: Downloads only the APK file
- **APK + OBB**: Downloads both APK and OBB files sequentially
- **OBB Only**: Downloads only the OBB file

### 4. **Advanced Features**
- Real-time progress tracking with speed and ETA
- Storage space verification before download
- Automatic cleanup of old files when space is low
- File integrity verification after download
- Automatic APK installation after download
- Proper OBB file placement in Android/obb directory
- Download resumption capability
- Modern progress dialog with cancel functionality

## File Structure

```
app/src/main/java/com/bearmod/loader/
├── download/
│   ├── DownloadManager.java          # Main download manager
│   └── GameDownloadWorker.java       # Background download worker
├── utils/
│   ├── StorageManager.java           # Storage management utility
│   └── ApkInstaller.java            # APK installation utility
└── ui/download/
    └── DownloadProgressDialog.java   # Progress dialog UI
```

## Configuration

### KeyAuth File IDs

Update the file IDs in `DownloadManager.getPubgMobileConfig()`:

```java
case "pubg mobile global":
    return new PubgMobileConfig(
        "com.tencent.ig",
        "YOUR_APK_FILE_ID",     // Replace with actual KeyAuth file ID
        "YOUR_OBB_FILE_ID",     // Replace with actual KeyAuth file ID
        120.5,                  // APK size in MB
        2800.0,                 // OBB size in MB
        "3.8.0",               // Version
        380000                  // Version code
    );
```

### File Sizes

Update the file sizes to match your actual PUBG Mobile files:
- APK files: ~120-125 MB
- OBB files: ~2.7-2.9 GB

## Usage

### Basic Download

```java
DownloadManager downloadManager = DownloadManager.getInstance();
downloadManager.initialize(context);

downloadManager.downloadGameFiles("PUBG MOBILE Global", 
    DownloadManager.DownloadType.APK_AND_OBB,
    new DownloadManager.DownloadListener() {
        @Override
        public void onSuccess(File downloadedFile) {
            // Download completed successfully
        }
        
        @Override
        public void onError(String error) {
            // Handle download error
        }
    });
```

### Enhanced Download with Progress Dialog

```java
downloadManager.downloadGameFilesEnhanced(context, "PUBG MOBILE Global",
    DownloadManager.DownloadType.APK_AND_OBB,
    new DownloadManager.EnhancedDownloadListener() {
        @Override
        public void onSuccess(String filePath) {
            // Download completed with progress dialog
        }
        
        @Override
        public void onError(String error) {
            // Handle error with progress dialog
        }
    });
```

## Download Flow

### APK + OBB Download Sequence

1. **Validation**: Check game configuration and download type
2. **Storage Check**: Verify available storage space
3. **Cleanup**: Automatically clean old files if needed
4. **Directory Creation**: Create necessary download directories
5. **APK Download**: Download APK file using KeyAuth
6. **APK Verification**: Verify file integrity
7. **OBB Download**: Download OBB file using KeyAuth
8. **OBB Verification**: Verify OBB file integrity
9. **Installation**: Automatically install APK
10. **Completion**: Notify success with file paths

### File Locations

- **APK Files**: `/storage/emulated/0/Download/BearMod/`
- **OBB Files**: `/storage/emulated/0/Android/obb/{package_name}/`

## Error Handling

The system handles various error scenarios:

- **Insufficient Storage**: Automatic cleanup or user notification
- **Network Errors**: Retry mechanism with exponential backoff
- **File Corruption**: Integrity verification and re-download
- **Permission Issues**: Proper permission requests
- **KeyAuth Errors**: Authentication and session management

## Storage Management

### Automatic Space Management

```java
// Check available space
if (!StorageManager.hasEnoughSpace(requiredSizeMB)) {
    // Try automatic cleanup
    if (StorageManager.cleanupForSpace(context, requiredSizeMB)) {
        // Continue with download
    } else {
        // Show storage error to user
    }
}
```

### Storage Information

```java
StorageManager.StorageInfo info = StorageManager.getStorageInfo();
Log.d(TAG, "Available: " + info.availableSpaceMB + "MB");
Log.d(TAG, "Total: " + info.totalSpaceMB + "MB");
Log.d(TAG, "Usage: " + info.usagePercentage + "%");
```

## Security Features

1. **KeyAuth Authentication**: All downloads require valid KeyAuth session
2. **File Integrity**: SHA-256 verification of downloaded files
3. **Secure Storage**: Files stored in appropriate Android directories
4. **Permission Management**: Proper Android permission handling

## Testing

### Test with Demo Files

The system includes demo file IDs for testing:
- APK: Firefox APK (~120MB)
- OBB: Sample video file (~5MB)

### Production Setup

1. Upload your PUBG Mobile files to KeyAuth
2. Get the file IDs from KeyAuth dashboard
3. Update the configuration in `getPubgMobileConfig()`
4. Test with small files first
5. Deploy with actual PUBG Mobile files

## Troubleshooting

### Common Issues

1. **Download Fails**: Check KeyAuth session and file IDs
2. **Storage Errors**: Verify permissions and available space
3. **Installation Fails**: Check APK file integrity and permissions
4. **OBB Not Found**: Verify OBB file placement and package name

### Debug Logging

Enable debug logging to troubleshoot issues:

```java
Log.d("DownloadManager", "Download started for: " + gameName);
Log.d("StorageManager", "Available space: " + availableSpaceMB + "MB");
```

## Performance Optimization

- **Chunked Downloads**: Large files downloaded in chunks
- **Background Processing**: Downloads run in background threads
- **Progress Updates**: Efficient progress reporting every 100ms
- **Memory Management**: Proper cleanup of resources

## Future Enhancements

- Download pause/resume functionality
- Multiple simultaneous downloads
- Download queue management
- Bandwidth throttling
- Delta updates for patches
