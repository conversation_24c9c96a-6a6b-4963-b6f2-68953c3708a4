# BearMod Loader + Target Mod Architecture

## Overview

This architecture implements a **shared certificate/token system** where the BearMod Loader authenticates once with Key<PERSON>uth and generates a **BearToken** that target mod apps can use to skip individual KeyAuth authentication.

## Architecture Flow

```
[User] → [BearMod Loader] → [<PERSON>A<PERSON>] → [BearToken] → [Target Mod Apps]
   │            │              │           │              │
   │            │              │           │              └─ Skip KeyAuth
   │            │              │           └─ Shared Trust Token
   │            │              └─ Single Authentication
   │            └─ Download & Install Mods
   └─ Single Login Experience
```

## Components

### 1. **BearMod Loader (Main App)**

**Responsibilities:**
- User authentication with KeyAuth
- Download and install PUBG Mobile mods
- Generate and manage BearTokens
- Signature verification
- Security management

**Key Classes:**
- `BearTokenManager` - Manages shared authentication tokens
- `DownloadManager` - Handles mod downloads with KeyAuth integration
- `NativeSecurityManager` - Native signature verification
- `AppSecurityManager` - Java-based security checks

### 2. **BearToken System**

**Purpose:** Shared authentication between loader and target mods

**Features:**
- **Encrypted Payload:** AES-256 encryption of token data
- **Signature Verification:** SHA-256 integrity checking
- **Device Binding:** Tokens tied to specific devices
- **Time-based Expiry:** 24-hour validity period
- **Shared Storage:** Accessible by target mod apps

**Token Structure:**
```
BearToken {
    encryptedPayload: String    // AES encrypted token data
    signature: String           // SHA-256 integrity signature
    createdTime: Long          // Token creation timestamp
    expiryTime: Long           // Token expiration timestamp
    deviceId: String           // Device identifier for binding
}

TokenPayload {
    keyAuthSessionId: String   // Original KeyAuth session
    hashedLicenseKey: String   // Hashed user license key
    deviceId: String           // Device identifier
    createdTime: Long          // Creation timestamp
    expiryTime: Long           // Expiration timestamp
    loaderSignature: String    // Loader app signature
}
```

### 3. **Target Mod Apps**

**Authentication Flow:**
1. **OnLaunch:** Check for valid BearToken
2. **If BearToken Valid:** Skip KeyAuth, start with trust flag
3. **If BearToken Invalid:** Perform normal KeyAuth authentication
4. **Start Patching:** Begin mod functionality

**Integration:**
- Copy `TargetModAuth.java` to target mod project
- Check `TargetModAuth.checkBearTokenAuth()` on app start
- Handle both trusted and normal authentication paths

## Security Features

### 1. **Multi-Layer Signature Verification**

**Native Level (C++):**
- JNI-based signature extraction
- SHA-256 fingerprint calculation
- Anti-tampering detection
- Debugger/emulator detection

**Java Level:**
- Package signature verification
- Certificate chain validation
- Installation source verification

### 2. **Token Security**

**Encryption:**
- AES-256-CBC encryption
- Random IV generation
- Secure key derivation

**Integrity:**
- SHA-256 signature verification
- Device binding validation
- Timestamp verification

**Anti-Replay:**
- Time-based expiry
- Device-specific binding
- Session correlation

### 3. **Shared Certificate Verification**

```cpp
bool verifySharedCertificate(JNIEnv* env, jobject context, jstring pubgPackageName) {
    // 1. Verify loader signature
    if (!verifyLoaderSignature(env, context)) return false;
    
    // 2. Verify PUBG signature  
    if (!verifyPubgSignature(env, context, pubgPackageName)) return false;
    
    // 3. Both apps have trusted signatures
    return true;
}
```

## Implementation Steps

### 1. **BearMod Loader Setup**

```java
// After successful KeyAuth authentication
BearTokenManager tokenManager = BearTokenManager.getInstance(context);
BearToken token = tokenManager.generateBearToken(sessionId, licenseKey);

// After mod download/installation
generateBearTokenForTargetMod(packageName);
```

### 2. **Target Mod Integration**

```java
// In target mod Application.onCreate()
TargetModAuth.AuthResult result = TargetModAuth.checkBearTokenAuth(this);

if (result.canSkipKeyAuth) {
    // Start with trust flag
    startModWithTrustFlag(result.tokenPayload);
} else {
    // Use normal KeyAuth
    performKeyAuthAuthentication();
}
```

### 3. **Native Security Integration**

```cpp
// In target mod native code
extern "C" JNIEXPORT jboolean JNICALL
Java_com_yourmod_NativeLib_verifyTrustedStatus(JNIEnv *env, jobject thiz, jobject context) {
    return verifySharedCertificate(env, context, "com.tencent.ig");
}
```

## File Structure

```
BearMod-Loader/
├── app/src/main/java/com/bearmod/loader/
│   ├── auth/
│   │   ├── BearTokenManager.java      # Token generation & management
│   │   ├── TargetModAuth.java         # Target mod authentication
│   │   └── KeyAuthManager.java        # KeyAuth integration
│   ├── security/
│   │   ├── NativeSecurityManager.java # JNI security interface
│   │   └── AppSecurityManager.java    # Java security checks
│   ├── download/
│   │   └── DownloadManager.java       # Enhanced with BearToken
│   └── utils/
│       └── StorageManager.java        # Storage management
├── app/src/main/cpp/
│   ├── signature_verification.h       # Native security headers
│   └── bear_loader.cpp               # Native implementation
└── nativelib/src/main/cpp/
    ├── bear_loader.cpp               # Core native functionality
    ├── memory_patcher.cpp            # Memory patching
    ├── anti_debug.cpp                # Anti-debugging
    └── utils/
        ├── string_obfuscator.cpp     # String protection
        └── memory_utils.cpp          # Memory utilities
```

## Benefits

### 1. **User Experience**
- **Single Authentication:** User logs in once to BearMod Loader
- **Seamless Mod Usage:** Target mods start immediately without additional login
- **Unified Management:** All mods managed from single loader app

### 2. **Security**
- **Centralized Authentication:** Single point of KeyAuth integration
- **Signature Verification:** Both loader and target apps verified
- **Token-based Trust:** Secure token sharing between apps
- **Anti-Tampering:** Native-level security checks

### 3. **Development**
- **Simplified Integration:** Target mods just check for BearToken
- **Reduced KeyAuth Calls:** Less API usage, better performance
- **Modular Design:** Easy to add new target mods
- **Fallback Support:** Graceful degradation to KeyAuth if needed

## Configuration

### 1. **Update KeyAuth Integration**

```java
// In DownloadManager.java - implement these methods:
private String getCurrentKeyAuthSessionId() {
    // Return actual KeyAuth session ID
    return keyAuthManager.getSessionId();
}

private String getCurrentUserLicenseKey() {
    // Return actual user license key
    return keyAuthManager.getUserLicenseKey();
}
```

### 2. **Update Signature Hashes**

```java
// In AppSecurityManager.java
private static final Set<String> TRUSTED_SIGNATURES = new HashSet<>(Arrays.asList(
    "YOUR_ACTUAL_RELEASE_SIGNATURE_SHA256",
    "YOUR_ACTUAL_DEBUG_SIGNATURE_SHA256"
));
```

### 3. **Update Native Hashes**

```cpp
// In signature_verification.cpp
static const std::vector<std::string> TRUSTED_LOADER_HASHES = {
    "YOUR_ACTUAL_LOADER_SHA256_HASH",
    "YOUR_DEBUG_LOADER_SHA256_HASH"
};
```

## Testing

### 1. **BearToken Generation**
- Test token creation after KeyAuth success
- Verify token encryption and signature
- Check token storage in shared location

### 2. **Target Mod Authentication**
- Test BearToken validation in target mod
- Verify fallback to KeyAuth when token invalid
- Check device binding and expiry

### 3. **Security Verification**
- Test signature verification (both Java and native)
- Verify anti-tampering detection
- Check shared certificate validation

## Production Deployment

1. **Generate Release Signatures:** Get actual SHA-256 fingerprints
2. **Update Configuration:** Replace placeholder hashes with real ones
3. **Test Integration:** Verify BearToken flow with real KeyAuth
4. **Deploy Loader:** Release BearMod Loader with token generation
5. **Update Target Mods:** Integrate BearToken authentication
6. **Monitor Usage:** Track authentication success rates

This architecture provides a secure, user-friendly mod ecosystem with centralized authentication and distributed trust verification.
