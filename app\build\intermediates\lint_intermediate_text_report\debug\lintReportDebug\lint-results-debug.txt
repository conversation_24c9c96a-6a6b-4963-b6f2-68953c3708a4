D:\Augment_Code\BearMod-Loader\app\lint-baseline.xml: Hint: 1 error and 58 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml [LintBaseline]
D:\Augment_Code\BearMod-Loader\app\lint-baseline.xml: Hint: 53 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with android.lintOptions.checkDependencies=true. Unmatched issue types: ContentDescription (6), DefaultLocale, DiscouragedApi, HardcodedText (3), IconLocation (4), LockedOrientationActivity, Overdraw (5), SelectedPhotoAccess, StaticFieldLeak (4), UnusedAttribute, UnusedResources (23), UseCompoundDrawables (3) [LintBaselineFixed]

   Explanation for issues of type "LintBaselineFixed":
   If a lint baseline describes a problem which is no longer reported, then
   the problem has either been fixed, or perhaps the issue type has been
   disabled. In any case, the entry can be removed from the baseline (such
   that if the issue is reintroduced at some point, lint will complain rather
   than just silently starting to match the old baseline entry again.)

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\auth\TapToUnlockActivity.java:34: Error: Unexpected implicit cast to ShapeableImageView: layout tag was ImageView [WrongViewCast]
        glyphView = findViewById(R.id.glyphView);
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "WrongViewCast":
   Keeps track of the view types associated with ids and if it finds a usage
   of the id in the Java code it ensures that it is treated as the same type.

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\download\DownloadManager.java:1064: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String baseName = gameName.replaceAll("\\s+", "-").toLowerCase();
                                                           ~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:143: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String progressText = String.format("%d%% (%.1f/%.1f MB)", 
                                  ^
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:147: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                progressText += String.format(" - %.1f MB/s", speedMBps);
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:150: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    progressText += String.format(" - ETA: %d:%02d", etaMinutes, etaSeconds);
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:174: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String message = String.format(
                         ^
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:226: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String storageText = String.format("Storage: %d MB available / %d MB total (%d%% used)",
                             ^
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\utils\StorageManager.java:216: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("Storage: %dMB used / %dMB total (%d%% used), %dMB available",
                   ^

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\Augment_Code\BearMod-Loader\app\src\main\res\values-night\colors.xml:5: Error: The color "card_bg" in values-night has no declaration in the base values folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier [MissingDefaultResource]
    <color name="card_bg">#1F1F1F</color>
           ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values-night\colors.xml:11: Error: The color "surface_container_lowest" in values-night has no declaration in the base values folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier [MissingDefaultResource]
    <color name="surface_container_lowest">#0F0F0F</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values-night\colors.xml:13: Error: The color "surface_container_highest" in values-night has no declaration in the base values folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier [MissingDefaultResource]
    <color name="surface_container_highest">#424242</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingDefaultResource":
   If a resource is only defined in folders with qualifiers like -land or -en,
   and there is no default declaration in the base folder (layout or values
   etc), then the app will crash if that resource is accessed on a device
   where the device is in a configuration missing the given qualifier.

   As a special case, drawables do not have to be specified in the base
   folder; if there is a match in a density folder (such as drawable-mdpi)
   that image will be used and scaled. Note however that if you  only specify
   a drawable in a folder like drawable-en-hdpi, the app will crash in
   non-English locales.

   There may be scenarios where you have a resource, such as a -fr drawable,
   which is only referenced from some other resource with the same qualifiers
   (such as a -fr style), which itself has safe fallbacks. However, this still
   makes it possible for somebody to accidentally reference the drawable and
   crash, so it is safer to create a default fallback in the base folder.
   Alternatively, you can suppress the issue by adding
   tools:ignore="MissingDefaultResource" on the element.

   (This scenario frequently happens with string translations, where you might
   delete code and the corresponding resources, but forget to delete a
   translation. There is a dedicated issue id for that scenario, with the id
   ExtraTranslation.)

D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:32: Error: android:windowLightNavigationBar requires API level 27 (current min is 24) [NewApi]
        <item name="android:windowLightNavigationBar">false</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:38: Error: android:windowSplashScreenBackground requires API level 31 (current min is 24) [NewApi]
        <item name="android:windowSplashScreenBackground">@color/background</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:39: Error: android:windowSplashScreenAnimatedIcon requires API level 31 (current min is 24) [NewApi]
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_launcher_foreground</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:40: Error: android:windowSplashScreenIconBackgroundColor requires API level 31 (current min is 24) [NewApi]
        <item name="android:windowSplashScreenIconBackgroundColor">@color/primary</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:41: Error: android:windowSplashScreenBrandingImage requires API level 31 (current min is 24) [NewApi]
        <item name="android:windowSplashScreenBrandingImage">@drawable/splash_branding</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:57: Error: android:windowLightNavigationBar requires API level 27 (current min is 24) [NewApi]
        <item name="android:windowLightNavigationBar">false</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:255: Error: android:windowLightNavigationBar requires API level 27 (current min is 24) [NewApi]
        <item name="android:windowLightNavigationBar">false</item>
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:74: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_download_progress, null);
                                                                                                 ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

D:\Augment_Code\BearMod-Loader\app\build.gradle.kts:107: Warning: A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-alpha07 [GradleDependency]
    implementation("androidx.security:security-crypto:1.1.0-alpha06")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:25: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
            android:tint="#58A6FF"
            ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

D:\Augment_Code\BearMod-Loader\app\src\main\res\xml\network_security_config.xml:11: Warning: Insecure Base Configuration [InsecureBaseConfiguration]
    <base-config cleartextTrafficPermitted="true">
                                            ~~~~

   Explanation for issues of type "InsecureBaseConfiguration":
   Permitting cleartext traffic could allow eavesdroppers to intercept data
   sent by your app, which impacts the privacy of your users. Consider only
   allowing encrypted traffic by setting the cleartextTrafficPermitted tag to
   false.

   https://goo.gle/InsecureBaseConfiguration
   https://developer.android.com/preview/features/security-config.html

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:50: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\utils\ApkInstaller.java:56: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:232: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                <LinearLayout
                 ~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:256: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                <LinearLayout
                 ~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:291: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                <LinearLayout
                 ~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_main.xml:161: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
            <LinearLayout
             ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_settings.xml:9: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_settings.xml:10: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:7: Warning: Possible overdraw: Root element paints background @color/background with a theme that also paints a background (inferred theme is @style/Theme.BearLoader) [Overdraw]
    android:background="@color/background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:7: Warning: Possible overdraw: Root element paints background #0D1117 with a theme that also paints a background (inferred theme is @style/Theme.BearLoader) [Overdraw]
    android:background="#0D1117"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_fresh_download.xml:7: Warning: Possible overdraw: Root element paints background #0D1117 with a theme that also paints a background (inferred theme is @style/Theme.BearLoader) [Overdraw]
    android:background="#0D1117"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_tap_unlock.xml:6: Warning: Possible overdraw: Root element paints background @color/black with a theme that also paints a background (inferred theme is @style/Theme_BearLoader_NoActionBar) [Overdraw]
    android:background="@color/black">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:2: Warning: The resource R.layout.activity_download appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_main.xml:2: Warning: The resource R.layout.activity_main appears to be unused [UnusedResources]
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\values-night\colors.xml:5: Warning: The resource R.color.card_bg appears to be unused [UnusedResources]
    <color name="card_bg">#1F1F1F</color>
           ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml:16: Warning: The resource R.color.primary_container appears to be unused [UnusedResources]
    <color name="primary_container">#E3F2FD</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml:22: Warning: The resource R.color.secondary_light appears to be unused [UnusedResources]
    <color name="secondary_light">#B2EBF2</color>
           ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.secondary_container appears to be unused [UnusedResources]
    <color name="secondary_container">#E0F7FA</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.on_secondary appears to be unused [UnusedResources]
    <color name="on_secondary">#000000</color>
           ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml:28: Warning: The resource R.color.accent_dark appears to be unused [UnusedResources]
    <color name="accent_dark">#C51162</color>
           ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:10: Warning: The resource R.dimen.margin_large appears to be unused [UnusedResources]
    <dimen name="margin_large">24dp</dimen>
           ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:13: Warning: The resource R.dimen.padding_large appears to be unused [UnusedResources]
    <dimen name="padding_large">24dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:15: Warning: The resource R.dimen.padding_small appears to be unused [UnusedResources]
    <dimen name="padding_small">8dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:19: Warning: The resource R.dimen.corner_radius_medium appears to be unused [UnusedResources]
    <dimen name="corner_radius_medium">16dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:20: Warning: The resource R.dimen.corner_radius_small appears to be unused [UnusedResources]
    <dimen name="corner_radius_small">8dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:24: Warning: The resource R.dimen.elevation_large appears to be unused [UnusedResources]
    <dimen name="elevation_large">8dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:25: Warning: The resource R.dimen.elevation_medium appears to be unused [UnusedResources]
    <dimen name="elevation_medium">4dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:29: Warning: The resource R.dimen.icon_size_large appears to be unused [UnusedResources]
    <dimen name="icon_size_large">48dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:30: Warning: The resource R.dimen.icon_size_medium appears to be unused [UnusedResources]
    <dimen name="icon_size_medium">24dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml:31: Warning: The resource R.dimen.icon_size_small appears to be unused [UnusedResources]
    <dimen name="icon_size_small">16dp</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\download_card_bg.xml:2: Warning: The resource R.drawable.download_card_bg appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\raw\download_complete.json: Warning: The resource R.raw.download_complete appears to be unused [UnusedResources]
D:\Augment_Code\BearMod-Loader\app\src\main\res\menu\drawer_menu.xml:2: Warning: The resource R.menu.drawer_menu appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_dashboard.xml:2: Warning: The resource R.drawable.ic_dashboard appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_empty.xml:2: Warning: The resource R.drawable.ic_empty appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_error.xml:2: Warning: The resource R.drawable.ic_error appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_key.xml:2: Warning: The resource R.drawable.ic_key appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_logout.xml:2: Warning: The resource R.drawable.ic_logout appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_cloud.xml:2: Warning: The resource R.drawable.ic_nav_cloud appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_home.xml:2: Warning: The resource R.drawable.ic_nav_home appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_profile.xml:2: Warning: The resource R.drawable.ic_nav_profile appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_settings.xml:2: Warning: The resource R.drawable.ic_nav_settings appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_paste.xml:2: Warning: The resource R.drawable.ic_paste appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\logo_splash.xml:2: Warning: The resource R.drawable.logo_splash appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\nav_header.xml:2: Warning: The resource R.layout.nav_header appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\pubg_status_brutal.xml:2: Warning: The resource R.drawable.pubg_status_brutal appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\anim\slide_in_left.xml:2: Warning: The resource R.anim.slide_in_left appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\anim\slide_out_right.xml:2: Warning: The resource R.anim.slide_out_right appears to be unused [UnusedResources]
<set xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.app_logo appears to be unused [UnusedResources]
    <string name="app_logo">App Logo</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.login_subtitle appears to be unused [UnusedResources]
    <string name="login_subtitle">Enter your license key to continue</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:10: Warning: The resource R.string.license_key appears to be unused [UnusedResources]
    <string name="license_key">License Key</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:11: Warning: The resource R.string.enter_license_key appears to be unused [UnusedResources]
    <string name="enter_license_key">Enter your license key</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.invalid_license_key_format appears to be unused [UnusedResources]
    <string name="invalid_license_key_format">Invalid license key format</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:13: Warning: The resource R.string.remember_me appears to be unused [UnusedResources]
    <string name="remember_me">Remember me</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.login appears to be unused [UnusedResources]
    <string name="login">Login</string>
            ~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.login_success appears to be unused [UnusedResources]
    <string name="login_success">Login successful</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.login_error appears to be unused [UnusedResources]
    <string name="login_error">Login failed: %1$s</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:17: Warning: The resource R.string.license_valid_until appears to be unused [UnusedResources]
    <string name="license_valid_until">License valid until: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:18: Warning: The resource R.string.registration_date appears to be unused [UnusedResources]
    <string name="registration_date">Registration date: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.days_remaining appears to be unused [UnusedResources]
    <string name="days_remaining">%1$d days remaining</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.keyauth_init_warning appears to be unused [UnusedResources]
    <string name="keyauth_init_warning">KeyAuth initialization warning. You may experience login issues.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.dashboard_title appears to be unused [UnusedResources]
    <string name="dashboard_title">Dashboard</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.update_available appears to be unused [UnusedResources]
    <string name="update_available">Update Available</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.not_installed appears to be unused [UnusedResources]
    <string name="not_installed">Not Installed</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.game_version appears to be unused [UnusedResources]
    <string name="game_version">Game Version: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.updated appears to be unused [UnusedResources]
    <string name="updated">Updated: %1$s</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:35: Warning: The resource R.string.download_title appears to be unused [UnusedResources]
    <string name="download_title">Download</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:36: Warning: The resource R.string.available_releases appears to be unused [UnusedResources]
    <string name="available_releases">Available Releases</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:37: Warning: The resource R.string.no_releases_available appears to be unused [UnusedResources]
    <string name="no_releases_available">No releases available</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:38: Warning: The resource R.string.download_patches appears to be unused [UnusedResources]
    <string name="download_patches">Download</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:39: Warning: The resource R.string.target_app appears to be unused [UnusedResources]
    <string name="target_app">Target App</string>
            ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.scan_offsets appears to be unused [UnusedResources]
    <string name="scan_offsets">Scan Offsets</string>
            ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.obb_size appears to be unused [UnusedResources]
    <string name="obb_size">OBB Size: %1$s</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:44: Warning: The resource R.string.total_size appears to be unused [UnusedResources]
    <string name="total_size">Total Size: %1$s</string>
            ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:45: Warning: The resource R.string.download_progress appears to be unused [UnusedResources]
    <string name="download_progress">Download Progress</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:47: Warning: The resource R.string.downloading_detailed appears to be unused [UnusedResources]
    <string name="downloading_detailed">Downloading… %1$s / %2$s • %3$s • ETA: %4$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:49: Warning: The resource R.string.download_complete_path appears to be unused [UnusedResources]
    <string name="download_complete_path">Download Complete: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:50: Warning: The resource R.string.download_failed appears to be unused [UnusedResources]
    <string name="download_failed">Download Failed: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:51: Warning: The resource R.string.download_cancelled appears to be unused [UnusedResources]
    <string name="download_cancelled">Download Cancelled</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:52: Warning: The resource R.string.download_in_progress appears to be unused [UnusedResources]
    <string name="download_in_progress">Download in progress. Do you want to cancel?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:53: Warning: The resource R.string.continue_download appears to be unused [UnusedResources]
    <string name="continue_download">Continue Download</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:54: Warning: The resource R.string.cancel_download appears to be unused [UnusedResources]
    <string name="cancel_download">Cancel Download</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:55: Warning: The resource R.string.available appears to be unused [UnusedResources]
    <string name="available">Available</string>
            ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:91: Warning: The resource R.string.success appears to be unused [UnusedResources]
    <string name="success">Success</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:92: Warning: The resource R.string.warning appears to be unused [UnusedResources]
    <string name="warning">Warning</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:96: Warning: The resource R.string.navigation_drawer_open appears to be unused [UnusedResources]
    <string name="navigation_drawer_open">Open navigation drawer</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:97: Warning: The resource R.string.navigation_drawer_close appears to be unused [UnusedResources]
    <string name="navigation_drawer_close">Close navigation drawer</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:98: Warning: The resource R.string.sync_error appears to be unused [UnusedResources]
    <string name="sync_error">Sync error: %1$s</string>
            ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:105: Warning: The resource R.string.download appears to be unused [UnusedResources]
    <string name="download">Download</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:108: Warning: The resource R.string.pubg_global appears to be unused [UnusedResources]
    <string name="pubg_global">PUBG MOBILE GL</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:109: Warning: The resource R.string.pubg_kr appears to be unused [UnusedResources]
    <string name="pubg_kr">PUBG MOBILE KR</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:110: Warning: The resource R.string.pubg_tw appears to be unused [UnusedResources]
    <string name="pubg_tw">PUBG MOBILE TW</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:111: Warning: The resource R.string.pubg_vn appears to be unused [UnusedResources]
    <string name="pubg_vn">PUBG MOBILE VN</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:112: Warning: The resource R.string.version_format appears to be unused [UnusedResources]
    <string name="version_format">Version %s</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:113: Warning: The resource R.string.global_version_desc appears to be unused [UnusedResources]
    <string name="global_version_desc">Global version patch</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:114: Warning: The resource R.string.kr_version_desc appears to be unused [UnusedResources]
    <string name="kr_version_desc">Korean version patch</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:115: Warning: The resource R.string.tw_version_desc appears to be unused [UnusedResources]
    <string name="tw_version_desc">Taiwan version patch</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:116: Warning: The resource R.string.vn_version_desc appears to be unused [UnusedResources]
    <string name="vn_version_desc">Vietnam version patch</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:117: Warning: The resource R.string.game_icon appears to be unused [UnusedResources]
    <string name="game_icon">Game icon</string>
            ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:118: Warning: The resource R.string.game_version_format appears to be unused [UnusedResources]
    <string name="game_version_format">Version %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:119: Warning: The resource R.string.release_date_format appears to be unused [UnusedResources]
    <string name="release_date_format">Released: %1$s</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:120: Warning: The resource R.string.file_size_format appears to be unused [UnusedResources]
    <string name="file_size_format">%1$s MB</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:131: Warning: The resource R.string.resume appears to be unused [UnusedResources]
    <string name="resume">Resume</string>
            ~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:134: Warning: The resource R.string.username appears to be unused [UnusedResources]
    <string name="username">Username</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:135: Warning: The resource R.string.password appears to be unused [UnusedResources]
    <string name="password">Password</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:136: Warning: The resource R.string.forgot_password appears to be unused [UnusedResources]
    <string name="forgot_password">Forgot Password?</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:137: Warning: The resource R.string.register appears to be unused [UnusedResources]
    <string name="register">Register</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:138: Warning: The resource R.string.login_failed appears to be unused [UnusedResources]
    <string name="login_failed">Login failed</string>
            ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:139: Warning: The resource R.string.invalid_credentials appears to be unused [UnusedResources]
    <string name="invalid_credentials">Invalid username or password</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:140: Warning: The resource R.string.network_error appears to be unused [UnusedResources]
    <string name="network_error">Network error</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:141: Warning: The resource R.string.unknown_error appears to be unused [UnusedResources]
    <string name="unknown_error">Unknown error occurred</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:144: Warning: The resource R.string.theme appears to be unused [UnusedResources]
    <string name="theme">Theme</string>
            ~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:145: Warning: The resource R.string.theme_light appears to be unused [UnusedResources]
    <string name="theme_light">Light</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:146: Warning: The resource R.string.theme_dark appears to be unused [UnusedResources]
    <string name="theme_dark">Dark</string>
            ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:147: Warning: The resource R.string.theme_system appears to be unused [UnusedResources]
    <string name="theme_system">System default</string>
            ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:148: Warning: The resource R.string.notifications appears to be unused [UnusedResources]
    <string name="notifications">Notifications</string>
            ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:149: Warning: The resource R.string.auto_update appears to be unused [UnusedResources]
    <string name="auto_update">Auto-update</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:150: Warning: The resource R.string.version appears to be unused [UnusedResources]
    <string name="version">Version</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:151: Warning: The resource R.string.licenses appears to be unused [UnusedResources]
    <string name="licenses">Licenses</string>
            ~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:152: Warning: The resource R.string.privacy_policy appears to be unused [UnusedResources]
    <string name="privacy_policy">Privacy Policy</string>
            ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:153: Warning: The resource R.string.terms_of_service appears to be unused [UnusedResources]
    <string name="terms_of_service">Terms of Service</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:156: Warning: The resource R.string.downloads appears to be unused [UnusedResources]
    <string name="downloads">Downloads</string>
            ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:157: Warning: The resource R.string.no_downloads appears to be unused [UnusedResources]
    <string name="no_downloads">No downloads yet</string>
            ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:158: Warning: The resource R.string.delete appears to be unused [UnusedResources]
    <string name="delete">Delete</string>
            ~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:159: Warning: The resource R.string.paused appears to be unused [UnusedResources]
    <string name="paused">Paused</string>
            ~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:160: Warning: The resource R.string.cancelled appears to be unused [UnusedResources]
    <string name="cancelled">Cancelled</string>
            ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:165: Warning: The resource R.string.confirm appears to be unused [UnusedResources]
    <string name="confirm">Confirm</string>
            ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:166: Warning: The resource R.string.delete_download appears to be unused [UnusedResources]
    <string name="delete_download">Delete download?</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:167: Warning: The resource R.string.delete_all_downloads appears to be unused [UnusedResources]
    <string name="delete_all_downloads">Delete all downloads?</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:168: Warning: The resource R.string.yes appears to be unused [UnusedResources]
    <string name="yes">Yes</string>
            ~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:169: Warning: The resource R.string.no appears to be unused [UnusedResources]
    <string name="no">No</string>
            ~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:172: Warning: The resource R.string.icon_search appears to be unused [UnusedResources]
    <string name="icon_search">Search</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:173: Warning: The resource R.string.icon_filter appears to be unused [UnusedResources]
    <string name="icon_filter">Filter</string>
            ~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml:174: Warning: The resource R.string.icon_sort appears to be unused [UnusedResources]
    <string name="icon_sort">Sort</string>
            ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\raw\success_animation.json: Warning: The resource R.raw.success_animation appears to be unused [UnusedResources]
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:61: Warning: The resource R.style.Theme_BearLoader_Splash appears to be unused [UnusedResources]
    <style name="Theme.BearLoader.Splash" parent="Theme.BearLoader.NoActionBar">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:84: Warning: The resource R.style.Animation_BearLoader_Activity appears to be unused [UnusedResources]
    <style name="Animation.BearLoader.Activity" parent="@android:style/Animation.Activity">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:127: Warning: The resource R.style.Widget_BearLoader_Button_Secondary appears to be unused [UnusedResources]
    <style name="Widget.BearLoader.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:182: Warning: The resource R.style.Widget_BearLoader_Button_Small appears to be unused [UnusedResources]
    <style name="Widget.BearLoader.Button.Small" parent="Widget.Material3.Button">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:206: Warning: The resource R.style.Widget_BearLoader_Button_Icon appears to be unused [UnusedResources]
    <style name="Widget.BearLoader.Button.Icon" parent="Widget.Material3.Button.IconButton">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml:241: Warning: The resource R.style.Theme_BearMod appears to be unused [UnusedResources]
    <style name="Theme.BearMod" parent="Theme.Material3.DayNight.NoActionBar">
           ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:199: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:210: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

D:\Augment_Code\BearMod-Loader\app\build.gradle.kts:107: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.security:security-crypto:1.1.0-alpha06")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\auth\TapToUnlockActivity.java:50: Warning: Custom view `ShapeableImageView` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        glyphView.setOnTouchListener((v, event) -> {
        ^
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\auth\TapToUnlockActivity.java:50: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
        glyphView.setOnTouchListener((v, event) -> {
                                     ^

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:157: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:238: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:263: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:63: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            fallbackView.setText("Error loading item");
                                 ~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:114: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    holder.status.setText("Unknown");
                                          ~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:129: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        holder.tvProgressInline.setText(currentProgress + "%");
                                                        ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:132: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        holder.tvSpeedInline.setText(DECIMAL_FORMAT.format(currentSpeedMBps) + " MB/s");
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:132: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        holder.tvSpeedInline.setText(DECIMAL_FORMAT.format(currentSpeedMBps) + " MB/s");
                                                                                               ~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:136: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                            holder.tvEtaInline.setText(currentEtaMinutes + "m " + currentEtaSeconds + "s");
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java:138: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                            holder.tvEtaInline.setText(currentEtaSeconds + "s");
                                                       ~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:124: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        tvTitle.setText("Downloading " + downloadType);
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:124: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvTitle.setText("Downloading " + downloadType);
                        ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:127: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvSpeed.setText("0 MB/s");
                        ~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:128: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvEta.setText("Calculating...");
                      ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:129: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvFileSize.setText("0 / 0 MB");
                           ~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:149: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        tvProgress.setText(progress + "%");
                           ~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:153: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + " MB/s");
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:153: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + " MB/s");
                                                               ~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:155: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvSpeed.setText("0 MB/s");
                            ~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:161: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvEta.setText(etaMinutes + "m " + etaSeconds + "s");
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:163: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                tvEta.setText(etaSeconds + "s");
                              ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:170: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        tvFileSize.setText(DECIMAL_FORMAT.format(downloadedMB) + " / " + 
                           ^
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:171: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                          DECIMAL_FORMAT.format(totalSizeMB) + " MB");
                                                               ~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:181: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvTitle.setText("Download Complete");
                            ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:182: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvProgress.setText("100%");
                               ~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:184: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvSpeed.setText("Complete");
                            ~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:185: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvEta.setText("Done");
                          ~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:186: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            btnCancel.setText("Close");
                              ~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:188: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvTitle.setText("Download Failed");
                            ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:189: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvProgress.setText("Failed");
                               ~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:190: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            tvSpeed.setText("Error");
                            ~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java:192: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            btnCancel.setText("Close");
                              ~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:70: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvStatus.setText("Ready to download");
                         ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:98: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        tvStatus.setText("Starting download for " + gameName + "...");
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:98: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvStatus.setText("Starting download for " + gameName + "...");
                         ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:108: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        tvStatus.setText("Download completed: " + gameName);
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:108: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        tvStatus.setText("Download completed: " + gameName);
                                         ~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:109: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        tvProgress.setText("100%");
                                           ~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:125: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        tvStatus.setText("Download failed: " + error);
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:125: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        tvStatus.setText("Download failed: " + error);
                                         ~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:126: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        tvProgress.setText("Failed");
                                           ~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:190: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvStatus.setText("Storage test completed");
                         ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:204: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        tvStatus.setText("Cleaning up old files...");
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:211: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    tvStatus.setText("Cleanup completed successfully");
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\test\DownloadTestActivity.java:214: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    tvStatus.setText("Cleanup completed (no files to clean)");
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml:375: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                android:text="0%"
                ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:26: Warning: Hardcoded string "Back", should use @string resource [HardcodedText]
            android:contentDescription="Back" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:32: Warning: Hardcoded string "Download Test", should use @string resource [HardcodedText]
            android:text="Download Test"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:56: Warning: Hardcoded string "Download Status", should use @string resource [HardcodedText]
            android:text="Download Status"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:66: Warning: Hardcoded string "Ready to download", should use @string resource [HardcodedText]
            android:text="Ready to download"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:84: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
            android:text="0%"
            ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:102: Warning: Hardcoded string "Storage Information", should use @string resource [HardcodedText]
            android:text="Storage Information"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:112: Warning: Hardcoded string "Loading storage info...", should use @string resource [HardcodedText]
            android:text="Loading storage info..."
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:130: Warning: Hardcoded string "PUBG Mobile Downloads", should use @string resource [HardcodedText]
            android:text="PUBG Mobile Downloads"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:140: Warning: Hardcoded string "Download PUBG Mobile Global", should use @string resource [HardcodedText]
            android:text="Download PUBG Mobile Global"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:150: Warning: Hardcoded string "Download PUBG Mobile KR", should use @string resource [HardcodedText]
            android:text="Download PUBG Mobile KR"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:160: Warning: Hardcoded string "Download PUBG Mobile TW", should use @string resource [HardcodedText]
            android:text="Download PUBG Mobile TW"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:170: Warning: Hardcoded string "Download PUBG Mobile VN", should use @string resource [HardcodedText]
            android:text="Download PUBG Mobile VN"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:188: Warning: Hardcoded string "Utilities", should use @string resource [HardcodedText]
            android:text="Utilities"
            ~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:204: Warning: Hardcoded string "Test Storage", should use @string resource [HardcodedText]
                android:text="Test Storage"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download_test.xml:215: Warning: Hardcoded string "Cleanup Files", should use @string resource [HardcodedText]
                android:text="Cleanup Files"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml:74: Warning: Hardcoded string "•", should use @string resource [HardcodedText]
            android:text="•"
            ~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml:129: Warning: Hardcoded string "0%", should use @string resource [HardcodedText]
                    android:text="0%"
                    ~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml:138: Warning: Hardcoded string "0 MB/s", should use @string resource [HardcodedText]
                    android:text="0 MB/s"
                    ~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml:147: Warning: Hardcoded string "--", should use @string resource [HardcodedText]
                    android:text="--"
                    ~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

12 errors, 221 warnings (and 1 error, 58 warnings filtered by baseline lint-baseline.xml)
