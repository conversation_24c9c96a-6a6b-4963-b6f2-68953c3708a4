# Nativelib Integration Guide

## Overview

The `nativelib` module now contains the complete BearTrust signature verification system. This allows target mod apps to verify if they were launched by the trusted BearMod Loader and skip KeyAuth authentication accordingly.

## Module Structure

```
nativelib/
├── src/main/
│   ├── cpp/
│   │   ├── bear_trust_verifier.cpp    # Native signature verification
│   │   ├── nativelib.cpp              # Main native library
│   │   └── CMakeLists.txt             # Build configuration
│   └── java/com/bearmod/targetapp/
│       ├── BearTrust.java             # JNI bridge for native verification
│       ├── SignatureVerifier.java     # Enhanced signature verification
│       └── TargetAppIntegration.java  # Easy integration helper
```

## Integration Steps for Target Mod Apps

### 1. Add Nativelib Dependency

In your target mod app's `build.gradle`:

```gradle
dependencies {
    implementation project(':nativelib')
    // or if using as AAR:
    // implementation files('libs/nativelib.aar')
}
```

### 2. Copy Integration Files

Copy these files from nativelib to your target mod project:

```
src/main/java/com/bearmod/targetapp/
├── BearTrust.java
├── SignatureVerifier.java
└── TargetAppIntegration.java
```

### 3. Update Your Application Class

```java
public class YourModApplication extends Application {
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // Initialize with BearTrust
        if (TargetAppIntegration.initializeTargetApp(this)) {
            
            // Check if we can skip KeyAuth
            if (TargetAppIntegration.canSkipKeyAuth(this)) {
                Log.d("YourMod", "Trusted launch - skipping KeyAuth");
                startModWithTrustedMode();
            } else {
                Log.d("YourMod", "Standard launch - using KeyAuth");
                performKeyAuthAuthentication();
            }
            
        } else {
            Log.e("YourMod", "Authentication failed");
            System.exit(1);
        }
    }
    
    private void startModWithTrustedMode() {
        // Your mod implementation with enhanced features
        // NativeLib.startPatching(true);
    }
    
    private void performKeyAuthAuthentication() {
        // Your existing KeyAuth implementation
        // After success, call startModWithStandardMode()
    }
    
    @Override
    public void onTerminate() {
        super.onTerminate();
        TargetAppIntegration.cleanup();
    }
}
```

### 4. Update Your MainActivity

```java
public class MainActivity extends Activity {
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Simple signature verification
        if (!SignatureVerifier.isSignatureValid(this)) {
            Log.e("MainActivity", "Signature verification failed");
            finish();
            return;
        }
        
        // Continue with normal activity
        setContentView(R.layout.activity_main);
    }
}
```

### 5. Native Integration (Optional)

If you have native code in your target mod:

```cpp
// In your target mod's native code
#include <jni.h>
#include <android/log.h>

#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, "YourMod", __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, "YourMod", __VA_ARGS__)

extern "C" JNIEXPORT void JNICALL
Java_com_yourmod_NativeLib_startPatching(JNIEnv *env, jobject thiz, jboolean isTrusted) {
    
    if (isTrusted) {
        LOGI("Starting with trusted mode - enhanced features enabled");
        // Enable enhanced patching features
    } else {
        LOGI("Starting with standard mode");
        // Standard patching features
    }
    
    // Your mod patching logic here
}
```

## Configuration

### 1. Update Signature Hashes

In `bear_trust_verifier.cpp`, update the trusted signature hashes:

```cpp
// Replace with your actual signature hashes
static const std::vector<std::string> TRUSTED_LOADER_HASHES = {
    "your_bearmod_loader_release_sha256_hash",
    "your_bearmod_loader_debug_sha256_hash"
};

static const std::vector<std::string> TRUSTED_TARGET_HASHES = {
    "your_target_mod_release_sha256_hash",
    "your_target_mod_debug_sha256_hash"
};
```

### 2. Get Your Signature Hashes

Use this code during development to get your signature hashes:

```java
// In your target mod's onCreate()
TargetAppIntegration.logDebugInformation(this);

// Check logcat for output like:
// Current app signature: abc123def456...
// Add this to TRUSTED_TARGET_HASHES: "abc123def456...",
```

## Authentication Flow

```
[Target Mod Launches]
        ↓
[BearTrust.initialize()]
        ↓
[Native Signature Verification]
        ↓
[Check BearMod Loader Signature] → [If Invalid: Exit]
        ↓
[Check Target Mod Signature] → [If Invalid: Exit]
        ↓
[Both Valid: Return True] → [Skip KeyAuth]
        ↓
[Start Mod with Enhanced Features]

[If BearTrust Fails]
        ↓
[Use Standard KeyAuth Authentication]
        ↓
[Start Mod with Standard Features]
```

## API Reference

### BearTrust Class

```java
// Initialize BearTrust
BearTrust.initialize(context);

// Main verification function
boolean isVerified = BearTrust.nativeVerify(context);

// Get signature hash for debugging
String hash = BearTrust.getSignatureHash(context, packageName);

// Cleanup resources
BearTrust.cleanup();
```

### SignatureVerifier Class

```java
// Simple signature check (uses BearTrust internally)
boolean isValid = SignatureVerifier.isSignatureValid(context);

// Check if launched by trusted loader
boolean isTrusted = SignatureVerifier.isLaunchedByTrustedLoader(context);

// Comprehensive authentication
SignatureVerifier.AuthenticationResult result = 
    SignatureVerifier.performAuthentication(context);
```

### TargetAppIntegration Class

```java
// Complete initialization
boolean success = TargetAppIntegration.initializeTargetApp(context);

// Check if KeyAuth can be skipped
boolean canSkip = TargetAppIntegration.canSkipKeyAuth(context);

// Get authentication result
TargetAppIntegration.AuthenticationResult result = 
    TargetAppIntegration.performAuthentication(context);
```

## Security Features

1. **Native Verification**: Critical verification logic in C++ (harder to bypass)
2. **Multiple Hash Checks**: Verifies both loader and target app signatures
3. **Package Installation Verification**: Ensures BearMod Loader is actually installed
4. **SHA-256 Hashing**: Secure signature comparison
5. **Anti-Tampering**: Detects signature mismatches

## Troubleshooting

### Common Issues

1. **Library Not Found**: Ensure nativelib is properly included in dependencies
2. **Signature Mismatch**: Update TRUSTED_*_HASHES with actual signature values
3. **Native Library Load Failed**: Check CMakeLists.txt and ensure OpenSSL is available
4. **Verification Always Fails**: Use logDebugInformation() to check signature hashes

### Debug Commands

```java
// Log all signature information
TargetAppIntegration.logDebugInformation(context);

// Check if native library is available
boolean available = BearTrust.isNativeLibraryAvailable();

// Get current app signature
String signature = BearTrust.getSignatureHash(context, context.getPackageName());
```

## Building and Distribution

### 1. Build Nativelib Module

```bash
./gradlew :nativelib:assembleRelease
```

### 2. Use as AAR Dependency

```gradle
// In target mod app
dependencies {
    implementation files('libs/nativelib-release.aar')
}
```

### 3. Include in Target Mod

Copy the generated AAR to your target mod project and include as dependency.

## Benefits

1. **Single Sign-On**: Users authenticate once with BearMod Loader
2. **Enhanced Security**: Native-level signature verification
3. **Better UX**: Target mods start immediately without additional login
4. **Centralized Management**: All authentication handled by BearMod Loader
5. **Anti-Bypass Protection**: Harder to circumvent than Java-only verification

This integration provides a secure, user-friendly authentication system for your mod ecosystem!
