package com.bearmod.loader.ui.download;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.bearmod.loader.R;

import java.text.DecimalFormat;

/**
 * Enhanced download progress dialog with modern UI
 * Shows real-time progress, speed, ETA, and file information
 */
public class DownloadProgressDialog extends AlertDialog {
    
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.#");

    private ImageView ivGameIcon;
    private TextView tvTitle;
    private TextView tvFileName;
    private TextView tvProgress;
    private TextView tvSpeed;
    private TextView tvEta;
    private TextView tvFileSize;
    private ProgressBar progressBar;
    private Button btnCancel;
    private Button btnPause;

    private DownloadCancelListener cancelListener;
    private String downloadType;
    private String fileName;
    private int gameIconResId;
    
    /**
     * Constructor
     * @param context Context
     * @param downloadType Type of download (APK Only, APK + OBB, etc.)
     * @param fileName File name being downloaded
     * @param gameIconResId Game icon resource ID
     */
    public DownloadProgressDialog(@NonNull Context context, String downloadType, String fileName, int gameIconResId) {
        super(context);
        this.downloadType = downloadType;
        this.fileName = fileName;
        this.gameIconResId = gameIconResId;
        setCancelable(false);
    }

    /**
     * Constructor with default PUBG icon
     * @param context Context
     * @param downloadType Type of download (APK Only, APK + OBB, etc.)
     * @param fileName File name being downloaded
     */
    public DownloadProgressDialog(@NonNull Context context, String downloadType, String fileName) {
        this(context, downloadType, fileName, R.drawable.ic_pubg_mobile_global_logo);
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Inflate custom layout
        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_download_progress, null);
        setView(view);
        
        // Initialize views
        initViews(view);
        
        // Set initial values
        setupInitialState();
    }
    
    /**
     * Initialize views
     * @param view Root view
     */
    private void initViews(View view) {
        ivGameIcon = view.findViewById(R.id.iv_game_icon);
        tvTitle = view.findViewById(R.id.tv_download_title);
        tvFileName = view.findViewById(R.id.tv_file_name);
        tvProgress = view.findViewById(R.id.tv_progress);
        tvSpeed = view.findViewById(R.id.tv_speed);
        tvEta = view.findViewById(R.id.tv_eta);
        tvFileSize = view.findViewById(R.id.tv_file_size);
        progressBar = view.findViewById(R.id.progress_bar);
        btnCancel = view.findViewById(R.id.btn_cancel);
        btnPause = view.findViewById(R.id.btn_pause);

        // Set game icon
        if (ivGameIcon != null) {
            ivGameIcon.setImageResource(gameIconResId);
        }

        // Set cancel button listener
        btnCancel.setOnClickListener(v -> {
            if (cancelListener != null) {
                cancelListener.onCancelRequested();
            }
        });

        // Set pause button listener (for future implementation)
        if (btnPause != null) {
            btnPause.setOnClickListener(v -> {
                // TODO: Implement pause functionality
            });
        }
    }
    
    /**
     * Setup initial state
     */
    private void setupInitialState() {
        tvTitle.setText("Downloading " + downloadType);
        tvFileName.setText(fileName);
        tvProgress.setText("0%");
        tvSpeed.setText("0 MB/s");
        tvEta.setText("Calculating...");
        tvFileSize.setText("0 / 0 MB");
        progressBar.setProgress(0);
    }
    
    /**
     * Update download progress
     * @param progress Progress percentage (0-100)
     * @param downloadedMB Downloaded size in MB
     * @param totalSizeMB Total size in MB
     * @param speedMBps Download speed in MB/s
     * @param etaMinutes ETA minutes
     * @param etaSeconds ETA seconds
     */
    public void updateProgress(int progress, double downloadedMB, double totalSizeMB, 
                              double speedMBps, int etaMinutes, int etaSeconds) {
        
        // Update progress bar
        progressBar.setProgress(progress);
        
        // Update progress text
        tvProgress.setText(progress + "%");
        
        // Update speed
        if (speedMBps > 0) {
            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + " MB/s");
        } else {
            tvSpeed.setText("0 MB/s");
        }
        
        // Update ETA
        if (etaMinutes > 0 || etaSeconds > 0) {
            if (etaMinutes > 0) {
                tvEta.setText(etaMinutes + "m " + etaSeconds + "s");
            } else {
                tvEta.setText(etaSeconds + "s");
            }
        } else {
            tvEta.setText("--");
        }
        
        // Update file size
        tvFileSize.setText(DECIMAL_FORMAT.format(downloadedMB) + " / " + 
                          DECIMAL_FORMAT.format(totalSizeMB) + " MB");
    }
    
    /**
     * Show download completion
     * @param success True if download was successful
     * @param message Completion message
     */
    public void showCompletion(boolean success, String message) {
        if (success) {
            tvTitle.setText("Download Complete");
            tvProgress.setText("100%");
            progressBar.setProgress(100);
            tvSpeed.setText("Complete");
            tvEta.setText("Done");
            btnCancel.setText("Close");
        } else {
            tvTitle.setText("Download Failed");
            tvProgress.setText("Failed");
            tvSpeed.setText("Error");
            tvEta.setText(message);
            btnCancel.setText("Close");
        }
        
        // Update cancel button to close dialog
        btnCancel.setOnClickListener(v -> dismiss());
    }
    
    /**
     * Set download cancel listener
     * @param listener Cancel listener
     */
    public void setDownloadCancelListener(DownloadCancelListener listener) {
        this.cancelListener = listener;
    }
    
    /**
     * Download cancel listener interface
     */
    public interface DownloadCancelListener {
        void onCancelRequested();
    }
}
