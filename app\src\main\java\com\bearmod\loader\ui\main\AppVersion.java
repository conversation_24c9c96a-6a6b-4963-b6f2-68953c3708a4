package com.bearmod.loader.ui.main;

public class AppVersion {
    private int iconResId;
    private String name;
    private String version;
    private String description;
    private AppStatus status;

    /**
     * App status enum
     */
    public enum AppStatus {
        AVAILABLE,
        DOWNLOADING,
        INSTALLED
    }

    public AppVersion(int iconResId, String name, String version, String description) {
        this.iconResId = iconResId;
        this.name = name;
        this.version = version;
        this.description = description;
        this.status = AppStatus.AVAILABLE;
    }

    public int getIconResId() {
        return iconResId;
    }

    public String getName() {
        return name;
    }

    public String getVersion() {
        return version;
    }

    public String getDescription() {
        return description;
    }

    public AppStatus getStatus() {
        return status;
    }

    public void setStatus(AppStatus status) {
        this.status = status;
    }
} 