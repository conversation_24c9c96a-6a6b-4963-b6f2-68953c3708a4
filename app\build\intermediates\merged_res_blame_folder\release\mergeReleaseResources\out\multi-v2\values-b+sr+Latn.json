{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-59:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "58,59,60,61,62,63,64,189", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4303,4401,4503,4600,4704,4808,4913,14729", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4396,4498,4595,4699,4803,4908,5024,14825"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,328,407,480,568,652", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "125,186,251,323,402,475,563,647,722"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7445,7520,7581,7646,7718,7797,7870,7958,8042", "endColumns": "74,60,64,71,78,72,87,83,74", "endOffsets": "7515,7576,7641,7713,7792,7865,7953,8037,8112"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,324,401,478,558,666,760,854,986,1067,1130,1196,1289,1357,1420,1523,1583,1649,1705,1776,1836,1890,2002,2059,2120,2174,2250,2375,2462,2539,2632,2716,2799,2938,3020,3103,3234,3322,3400,3454,3510,3576,3650,3728,3799,3881,3957,4033,4108,4180,4287,4377,4450,4542,4638,4710,4786,4882,4935,5017,5084,5171,5258,5320,5384,5447,5516,5621,5731,5827,5935,5993,6053,6133,6216,6292", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "319,396,473,553,661,755,849,981,1062,1125,1191,1284,1352,1415,1518,1578,1644,1700,1771,1831,1885,1997,2054,2115,2169,2245,2370,2457,2534,2627,2711,2794,2933,3015,3098,3229,3317,3395,3449,3505,3571,3645,3723,3794,3876,3952,4028,4103,4175,4282,4372,4445,4537,4633,4705,4781,4877,4930,5012,5079,5166,5253,5315,5379,5442,5511,5616,5726,5822,5930,5988,6048,6128,6211,6287,6364"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,68,69,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,184,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,3867,3944,4021,4101,4209,5029,5123,5255,5336,5399,9239,9332,9400,9463,9566,9626,9692,9748,9819,9879,9933,10045,10102,10163,10217,10293,10418,10505,10582,10675,10759,10842,10981,11063,11146,11277,11365,11443,11497,11553,11619,11693,11771,11842,11924,12000,12076,12151,12223,12330,12420,12493,12585,12681,12753,12829,12925,12978,13060,13127,13214,13301,13363,13427,13490,13559,13664,13774,13870,13978,14036,14326,14493,14576,14652", "endLines": "25,53,54,55,56,57,65,66,67,68,69,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,184,186,187,188", "endColumns": "12,76,76,79,107,93,93,131,80,62,65,92,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,86,76,92,83,82,138,81,82,130,87,77,53,55,65,73,77,70,81,75,75,74,71,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "1131,3939,4016,4096,4204,4298,5118,5250,5331,5394,5460,9327,9395,9458,9561,9621,9687,9743,9814,9874,9928,10040,10097,10158,10212,10288,10413,10500,10577,10670,10754,10837,10976,11058,11141,11272,11360,11438,11492,11548,11614,11688,11766,11837,11919,11995,12071,12146,12218,12325,12415,12488,12580,12676,12748,12824,12920,12973,13055,13122,13209,13296,13358,13422,13485,13554,13659,13769,13865,13973,14031,14091,14401,14571,14647,14724"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,899,982,1064,1153,1244,1314,1381,1475,1570,1638,1702,1765,1837,1946,2060,2171,2247,2335,2409,2480,2572,2665,2732,2797,2850,2908,2956,3017,3083,3147,3210,3275,3339,3400,3466,3531,3597,3649,3711,3787,3863", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,894,977,1059,1148,1239,1309,1376,1470,1565,1633,1697,1760,1832,1941,2055,2166,2242,2330,2404,2475,2567,2660,2727,2792,2845,2903,2951,3012,3078,3142,3205,3270,3334,3395,3461,3526,3592,3644,3706,3782,3858,3914"}, "to": {"startLines": "2,11,16,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,5465,5547,5630,5712,5801,5892,5962,6029,6123,6218,6286,6350,6413,6485,6594,6708,6819,6895,6983,7057,7128,7220,7313,7380,8117,8170,8228,8276,8337,8403,8467,8530,8595,8659,8720,8786,8851,8917,8969,9031,9107,9183", "endLines": "10,15,20,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,81,82,81,88,90,69,66,93,94,67,63,62,71,108,113,110,75,87,73,70,91,92,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "377,646,907,5542,5625,5707,5796,5887,5957,6024,6118,6213,6281,6345,6408,6480,6589,6703,6814,6890,6978,7052,7123,7215,7308,7375,7440,8165,8223,8271,8332,8398,8462,8525,8590,8654,8715,8781,8846,8912,8964,9026,9102,9178,9234"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,816,898,989,1082,1177,1271,1371,1464,1559,1664,1755,1846,1932,2037,2143,2246,2353,2462,2569,2739,2836", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,811,893,984,1077,1172,1266,1366,1459,1554,1659,1750,1841,1927,2032,2138,2241,2348,2457,2564,2734,2831,2918"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1136,1243,1344,1450,1536,1640,1762,1847,1929,2020,2113,2208,2302,2402,2495,2590,2695,2786,2877,2963,3068,3174,3277,3384,3493,3600,3770,14406", "endColumns": "106,100,105,85,103,121,84,81,90,92,94,93,99,92,94,104,90,90,85,104,105,102,106,108,106,169,96,86", "endOffsets": "1238,1339,1445,1531,1635,1757,1842,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3379,3488,3595,3765,3862,14488"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "182,183", "startColumns": "4,4", "startOffsets": "14096,14203", "endColumns": "106,122", "endOffsets": "14198,14321"}}]}]}