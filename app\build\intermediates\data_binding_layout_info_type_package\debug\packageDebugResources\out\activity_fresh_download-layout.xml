<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fresh_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_fresh_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_fresh_download_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="786" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="27" endOffset="35"/></Target><Target id="@+id/btn_settings" view="ImageView"><Expressions/><location startLine="39" startOffset="8" endLine="49" endOffset="35"/></Target><Target id="@+id/btn_downloads" view="ImageView"><Expressions/><location startLine="51" startOffset="8" endLine="61" endOffset="35"/></Target><Target id="@+id/tab_global" view="LinearLayout"><Expressions/><location startLine="82" startOffset="8" endLine="108" endOffset="22"/></Target><Target id="@+id/tab_kr" view="LinearLayout"><Expressions/><location startLine="110" startOffset="8" endLine="136" endOffset="22"/></Target><Target id="@+id/tab_tw" view="LinearLayout"><Expressions/><location startLine="138" startOffset="8" endLine="164" endOffset="22"/></Target><Target id="@+id/tab_vn" view="LinearLayout"><Expressions/><location startLine="166" startOffset="8" endLine="191" endOffset="22"/></Target><Target id="@+id/card_global" view="LinearLayout"><Expressions/><location startLine="208" startOffset="12" endLine="321" endOffset="26"/></Target><Target id="@+id/btn_download_global" view="Button"><Expressions/><location startLine="295" startOffset="20" endLine="305" endOffset="66"/></Target><Target id="@+id/btn_update_global" view="Button"><Expressions/><location startLine="307" startOffset="20" endLine="317" endOffset="52"/></Target><Target id="@+id/card_kr" view="LinearLayout"><Expressions/><location startLine="324" startOffset="12" endLine="440" endOffset="26"/></Target><Target id="@+id/btn_download_kr" view="Button"><Expressions/><location startLine="414" startOffset="20" endLine="424" endOffset="66"/></Target><Target id="@+id/btn_update_kr" view="Button"><Expressions/><location startLine="426" startOffset="20" endLine="436" endOffset="66"/></Target><Target id="@+id/card_tw" view="LinearLayout"><Expressions/><location startLine="443" startOffset="12" endLine="559" endOffset="26"/></Target><Target id="@+id/btn_download_tw" view="Button"><Expressions/><location startLine="533" startOffset="20" endLine="543" endOffset="66"/></Target><Target id="@+id/btn_update_tw" view="Button"><Expressions/><location startLine="545" startOffset="20" endLine="555" endOffset="66"/></Target><Target id="@+id/card_vn" view="LinearLayout"><Expressions/><location startLine="562" startOffset="12" endLine="678" endOffset="26"/></Target><Target id="@+id/btn_download_vn" view="Button"><Expressions/><location startLine="652" startOffset="20" endLine="662" endOffset="66"/></Target><Target id="@+id/btn_update_vn" view="Button"><Expressions/><location startLine="664" startOffset="20" endLine="674" endOffset="66"/></Target><Target id="@+id/action_logout" view="LinearLayout"><Expressions/><location startLine="695" startOffset="8" endLine="723" endOffset="22"/></Target><Target id="@+id/action_clear_data" view="LinearLayout"><Expressions/><location startLine="725" startOffset="8" endLine="753" endOffset="22"/></Target><Target id="@+id/action_help" view="LinearLayout"><Expressions/><location startLine="755" startOffset="8" endLine="782" endOffset="22"/></Target></Targets></Layout>