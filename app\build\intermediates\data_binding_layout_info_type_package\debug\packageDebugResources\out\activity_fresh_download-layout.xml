<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fresh_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_fresh_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_fresh_download_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="730" endOffset="14"/></Target><Target id="@+id/tab_global" view="LinearLayout"><Expressions/><location startLine="61" startOffset="8" endLine="86" endOffset="22"/></Target><Target id="@+id/tab_kr" view="LinearLayout"><Expressions/><location startLine="88" startOffset="8" endLine="113" endOffset="22"/></Target><Target id="@+id/tab_tw" view="LinearLayout"><Expressions/><location startLine="115" startOffset="8" endLine="140" endOffset="22"/></Target><Target id="@+id/tab_vn" view="LinearLayout"><Expressions/><location startLine="142" startOffset="8" endLine="166" endOffset="22"/></Target><Target id="@+id/card_global" view="LinearLayout"><Expressions/><location startLine="183" startOffset="12" endLine="292" endOffset="26"/></Target><Target id="@+id/btn_download_global" view="Button"><Expressions/><location startLine="268" startOffset="20" endLine="277" endOffset="56"/></Target><Target id="@+id/btn_update_global" view="Button"><Expressions/><location startLine="279" startOffset="20" endLine="288" endOffset="58"/></Target><Target id="@+id/card_kr" view="LinearLayout"><Expressions/><location startLine="295" startOffset="12" endLine="404" endOffset="26"/></Target><Target id="@+id/btn_download_kr" view="Button"><Expressions/><location startLine="380" startOffset="20" endLine="389" endOffset="56"/></Target><Target id="@+id/btn_update_kr" view="Button"><Expressions/><location startLine="391" startOffset="20" endLine="400" endOffset="58"/></Target><Target id="@+id/card_tw" view="LinearLayout"><Expressions/><location startLine="407" startOffset="12" endLine="516" endOffset="26"/></Target><Target id="@+id/btn_download_tw" view="Button"><Expressions/><location startLine="492" startOffset="20" endLine="501" endOffset="56"/></Target><Target id="@+id/btn_update_tw" view="Button"><Expressions/><location startLine="503" startOffset="20" endLine="512" endOffset="58"/></Target><Target id="@+id/card_vn" view="LinearLayout"><Expressions/><location startLine="519" startOffset="12" endLine="628" endOffset="26"/></Target><Target id="@+id/btn_download_vn" view="Button"><Expressions/><location startLine="604" startOffset="20" endLine="613" endOffset="56"/></Target><Target id="@+id/btn_update_vn" view="Button"><Expressions/><location startLine="615" startOffset="20" endLine="624" endOffset="58"/></Target><Target id="@+id/action_logout" view="LinearLayout"><Expressions/><location startLine="644" startOffset="8" endLine="670" endOffset="22"/></Target><Target id="@+id/action_clear_data" view="LinearLayout"><Expressions/><location startLine="672" startOffset="8" endLine="698" endOffset="22"/></Target><Target id="@+id/action_help" view="LinearLayout"><Expressions/><location startLine="700" startOffset="8" endLine="726" endOffset="22"/></Target></Targets></Layout>