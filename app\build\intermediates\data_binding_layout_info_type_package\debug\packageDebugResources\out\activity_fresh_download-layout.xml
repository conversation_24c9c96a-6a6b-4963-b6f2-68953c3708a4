<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fresh_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_fresh_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_fresh_download_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="632" endOffset="14"/></Target><Target id="@+id/tab_global" view="LinearLayout"><Expressions/><location startLine="61" startOffset="8" endLine="86" endOffset="22"/></Target><Target id="@+id/tab_kr" view="LinearLayout"><Expressions/><location startLine="88" startOffset="8" endLine="113" endOffset="22"/></Target><Target id="@+id/tab_tw" view="LinearLayout"><Expressions/><location startLine="115" startOffset="8" endLine="140" endOffset="22"/></Target><Target id="@+id/tab_vn" view="LinearLayout"><Expressions/><location startLine="142" startOffset="8" endLine="166" endOffset="22"/></Target><Target id="@+id/card_global" view="LinearLayout"><Expressions/><location startLine="183" startOffset="12" endLine="279" endOffset="26"/></Target><Target id="@+id/btn_download_global" view="Button"><Expressions/><location startLine="255" startOffset="20" endLine="264" endOffset="56"/></Target><Target id="@+id/btn_update_global" view="Button"><Expressions/><location startLine="266" startOffset="20" endLine="275" endOffset="58"/></Target><Target id="@+id/card_kr" view="LinearLayout"><Expressions/><location startLine="282" startOffset="12" endLine="378" endOffset="26"/></Target><Target id="@+id/btn_download_kr" view="Button"><Expressions/><location startLine="354" startOffset="20" endLine="363" endOffset="56"/></Target><Target id="@+id/btn_update_kr" view="Button"><Expressions/><location startLine="365" startOffset="20" endLine="374" endOffset="58"/></Target><Target id="@+id/card_tw" view="LinearLayout"><Expressions/><location startLine="381" startOffset="12" endLine="477" endOffset="26"/></Target><Target id="@+id/btn_download_tw" view="Button"><Expressions/><location startLine="453" startOffset="20" endLine="462" endOffset="56"/></Target><Target id="@+id/btn_update_tw" view="Button"><Expressions/><location startLine="464" startOffset="20" endLine="473" endOffset="58"/></Target><Target id="@+id/card_vn" view="LinearLayout"><Expressions/><location startLine="480" startOffset="12" endLine="576" endOffset="26"/></Target><Target id="@+id/btn_download_vn" view="Button"><Expressions/><location startLine="552" startOffset="20" endLine="561" endOffset="56"/></Target><Target id="@+id/btn_update_vn" view="Button"><Expressions/><location startLine="563" startOffset="20" endLine="572" endOffset="58"/></Target><Target id="@+id/action_logout" view="TextView"><Expressions/><location startLine="591" startOffset="8" endLine="602" endOffset="36"/></Target><Target id="@+id/action_clear_data" view="TextView"><Expressions/><location startLine="604" startOffset="8" endLine="615" endOffset="36"/></Target><Target id="@+id/action_help" view="TextView"><Expressions/><location startLine="617" startOffset="8" endLine="628" endOffset="36"/></Target></Targets></Layout>