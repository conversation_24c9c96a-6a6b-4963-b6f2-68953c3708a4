{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx\\Debug\\6z2l3s6b\\x86", "soFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx\\Debug\\6z2l3s6b\\obj\\x86", "soRepublishFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cmake\\debug\\obj\\x86", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": ["-std=c++17"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx", "intermediatesBaseFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates", "intermediatesFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx", "gradleModulePathName": ":nativelib", "moduleRootFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib", "moduleBuildFile": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\build.gradle.kts", "makeFile": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973", "ndkFolderBeforeSymLinking": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973", "ndkVersion": "27.0.12077973", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "D:\\AndroidBuildEnv\\SDK\\cmake\\3.22.1\\bin\\cmake.exe", "cmakeVersionFromDsl": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\Augment_Code\\BearMod-Loader", "sdkFolder": "D:\\AndroidBuildEnv\\SDK", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "D:\\AndroidBuildEnv\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx\\Debug\\6z2l3s6b\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "6z2l3s6b1r5s624f4u4e3n2z2l465d1g13278275s60684zz69a133y1n2t", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.10.1.\n#   - $NDK is the path to NDK 27.0.12077973.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/nativelib/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-std=c++17\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/nativelib/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/nativelib/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-B$PROJECT/nativelib/.cxx/Debug/$HASH/$ABI\n-GNinja", "configurationArguments": ["-HD:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973", "-DCMAKE_ANDROID_NDK=D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973", "-DCMAKE_TOOLCHAIN_FILE=D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\AndroidBuildEnv\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-std=c++17", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx\\Debug\\6z2l3s6b\\obj\\x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx\\Debug\\6z2l3s6b\\obj\\x86", "-DCMAKE_BUILD_TYPE=Debug", "-BD:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx\\Debug\\6z2l3s6b\\x86", "-<PERSON><PERSON><PERSON><PERSON>"], "intermediatesParentFolder": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx\\Debug\\6z2l3s6b"}