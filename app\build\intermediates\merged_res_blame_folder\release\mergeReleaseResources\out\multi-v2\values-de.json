{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-59:/values-de/values-de.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,311,501,692,779,867,942,1032,1118,1197,1262,1366,1470,1539,1609,1681,1750,1877,2005,2138,2211,2295,2371,2448,2535,2623,2689,2754,2807,2867,2915,2976,3048,3118,3183,3254,3319,3377,3443,3507,3573,3625,3687,3763,3839", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "306,496,687,774,862,937,1027,1113,1192,1257,1361,1465,1534,1604,1676,1745,1872,2000,2133,2206,2290,2366,2443,2530,2618,2684,2749,2802,2862,2910,2971,3043,3113,3178,3249,3314,3372,3438,3502,3568,3620,3682,3758,3834,3890"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,406,596,5296,5383,5471,5546,5636,5722,5801,5866,5970,6074,6143,6213,6285,6354,6481,6609,6742,6815,6899,6975,7052,7139,7227,7293,8053,8106,8166,8214,8275,8347,8417,8482,8553,8618,8676,8742,8806,8872,8924,8986,9062,9138", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,86,87,74,89,85,78,64,103,103,68,69,71,68,126,127,132,72,83,75,76,86,87,65,64,52,59,47,60,71,69,64,70,64,57,65,63,65,51,61,75,75,55", "endOffsets": "401,591,782,5378,5466,5541,5631,5717,5796,5861,5965,6069,6138,6208,6280,6349,6476,6604,6737,6810,6894,6970,7047,7134,7222,7288,7353,8101,8161,8209,8270,8342,8412,8477,8548,8613,8671,8737,8801,8867,8919,8981,9057,9133,9189"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,612,727,805,880,972,1066,1162,1263,1370,1470,1574,1672,1770,1867,1949,2060,2162,2260,2367,2470,2574,2730,2832", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "205,303,415,501,607,722,800,875,967,1061,1157,1258,1365,1465,1569,1667,1765,1862,1944,2055,2157,2255,2362,2465,2569,2725,2827,2909"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "970,1075,1173,1285,1371,1477,1592,1670,1745,1837,1931,2027,2128,2235,2335,2439,2537,2635,2732,2814,2925,3027,3125,3232,3335,3439,3595,14415", "endColumns": "104,97,111,85,105,114,77,74,91,93,95,100,106,99,103,97,97,96,81,110,101,97,106,102,103,155,101,81", "endOffsets": "1070,1168,1280,1366,1472,1587,1665,1740,1832,1926,2022,2123,2230,2330,2434,2532,2630,2727,2809,2920,3022,3120,3227,3330,3434,3590,3692,14492"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,270,342,419,486,583,674", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "124,191,265,337,414,481,578,669,745"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7358,7432,7499,7573,7645,7722,7789,7886,7977", "endColumns": "73,66,73,71,76,66,96,90,75", "endOffsets": "7427,7494,7568,7640,7717,7784,7881,7972,8048"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,374,463,547,637,719,820,942,1023,1085,1151,1245,1315,1374,1482,1548,1617,1675,1747,1811,1865,1993,2053,2115,2169,2247,2384,2476,2554,2648,2734,2818,2963,3047,3133,3266,3356,3435,3492,3543,3609,3683,3765,3836,3911,3985,4063,4135,4209,4319,4411,4493,4582,4671,4745,4823,4909,4964,5043,5110,5190,5274,5336,5400,5463,5532,5639,5746,5845,5951,6012,6067,6149,6232,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "278,369,458,542,632,714,815,937,1018,1080,1146,1240,1310,1369,1477,1543,1612,1670,1742,1806,1860,1988,2048,2110,2164,2242,2379,2471,2549,2643,2729,2813,2958,3042,3128,3261,3351,3430,3487,3538,3604,3678,3760,3831,3906,3980,4058,4130,4204,4314,4406,4488,4577,4666,4740,4818,4904,4959,5038,5105,5185,5269,5331,5395,5458,5527,5634,5741,5840,5946,6007,6062,6144,6227,6304,6380"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3697,3788,3877,3961,4051,4864,4965,5087,5168,5230,9194,9288,9358,9417,9525,9591,9660,9718,9790,9854,9908,10036,10096,10158,10212,10290,10427,10519,10597,10691,10777,10861,11006,11090,11176,11309,11399,11478,11535,11586,11652,11726,11808,11879,11954,12028,12106,12178,12252,12362,12454,12536,12625,12714,12788,12866,12952,13007,13086,13153,13233,13317,13379,13443,13506,13575,13682,13789,13888,13994,14055,14333,14497,14580,14657", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,90,88,83,89,81,100,121,80,61,65,93,69,58,107,65,68,57,71,63,53,127,59,61,53,77,136,91,77,93,85,83,144,83,85,132,89,78,56,50,65,73,81,70,74,73,77,71,73,109,91,81,88,88,73,77,85,54,78,66,79,83,61,63,62,68,106,106,98,105,60,54,81,82,76,75", "endOffsets": "965,3783,3872,3956,4046,4128,4960,5082,5163,5225,5291,9283,9353,9412,9520,9586,9655,9713,9785,9849,9903,10031,10091,10153,10207,10285,10422,10514,10592,10686,10772,10856,11001,11085,11171,11304,11394,11473,11530,11581,11647,11721,11803,11874,11949,12023,12101,12173,12247,12357,12449,12531,12620,12709,12783,12861,12947,13002,13081,13148,13228,13312,13374,13438,13501,13570,13677,13784,13883,13989,14050,14105,14410,14575,14652,14728"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,118", "endOffsets": "154,273"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "14110,14214", "endColumns": "103,118", "endOffsets": "14209,14328"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4133,4231,4333,4433,4533,4641,4746,14733", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "4226,4328,4428,4528,4636,4741,4859,14829"}}]}]}