#include <jni.h>
#include <android/log.h>
#include <string>

#define LOG_TAG "BearMod"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Forward declarations from bear_trust_verifier.cpp
extern bool isVerifiedFromLoader(JNIEnv* env, jobject context);

/**
 * JNI_OnLoad - Called when the native library is loaded
 */
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    LOGI("BearMod native library loaded successfully");
    return JNI_VERSION_1_6;
}

/**
 * JNI_OnUnload - Called when the native library is unloaded
 */
JNIEXPORT void JNICALL JNI_OnUnload(JavaVM* vm, void* reserved) {
    LOGI("BearMod native library unloaded");
}

/**
 * Main verification function for target mods
 * Call this from your target mod's native code
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_NativeLib_verifyTrustedLaunch(JNIEnv* env, jobject thiz, jobject context) {
    LOGI("Verifying trusted launch from target mod");
    
    if (isVerifiedFromLoader(env, context)) {
        LOGI("Trusted by loader, skipping KeyAuth");
        return JNI_TRUE;
    } else {
        LOGE("Unauthorized launch!");
        return JNI_FALSE;
    }
}

/**
 * Initialize BearMod system
 */
extern "C" JNIEXPORT void JNICALL
Java_com_bearmod_NativeLib_initialize(JNIEnv* env, jobject thiz, jobject context) {
    LOGI("Initializing BearMod system");
    
    // Perform initial verification
    if (isVerifiedFromLoader(env, context)) {
        LOGI("BearMod system initialized with trusted status");
    } else {
        LOGI("BearMod system initialized with standard status");
    }
}

/**
 * Start patching with trust level
 */
extern "C" JNIEXPORT void JNICALL
Java_com_bearmod_NativeLib_startPatching(JNIEnv* env, jobject thiz, jobject context, jboolean isTrusted) {
    LOGI("Starting patching - Trusted: %s", isTrusted ? "true" : "false");
    
    if (isTrusted) {
        // Verify trust status
        if (isVerifiedFromLoader(env, context)) {
            LOGI("Trust verified - enabling enhanced patching features");
            // Your enhanced patching code here
        } else {
            LOGE("Trust verification failed - using standard features");
            // Fallback to standard patching
        }
    } else {
        LOGI("Standard patching mode");
        // Your standard patching code here
    }
}

/**
 * Get BearMod version info
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_NativeLib_getVersionInfo(JNIEnv* env, jobject thiz) {
    return env->NewStringUTF("BearMod v3.8.0 - Native Library");
}

/**
 * Perform security check
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_NativeLib_performSecurityCheck(JNIEnv* env, jobject thiz, jobject context) {
    LOGI("Performing security check");
    
    // Basic security checks
    // Add your security verification logic here
    
    return JNI_TRUE;
}
