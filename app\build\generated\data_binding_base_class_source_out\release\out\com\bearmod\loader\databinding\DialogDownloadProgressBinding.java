// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogDownloadProgressBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvDownloadTitle;

  @NonNull
  public final TextView tvEta;

  @NonNull
  public final TextView tvFileName;

  @NonNull
  public final TextView tvFileSize;

  @NonNull
  public final TextView tvProgress;

  @NonNull
  public final TextView tvSpeed;

  private DialogDownloadProgressBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull ProgressBar progressBar, @NonNull TextView tvDownloadTitle, @NonNull TextView tvEta,
      @NonNull TextView tvFileName, @NonNull TextView tvFileSize, @NonNull TextView tvProgress,
      @NonNull TextView tvSpeed) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.progressBar = progressBar;
    this.tvDownloadTitle = tvDownloadTitle;
    this.tvEta = tvEta;
    this.tvFileName = tvFileName;
    this.tvFileSize = tvFileSize;
    this.tvProgress = tvProgress;
    this.tvSpeed = tvSpeed;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogDownloadProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogDownloadProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_download_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogDownloadProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_download_title;
      TextView tvDownloadTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadTitle == null) {
        break missingId;
      }

      id = R.id.tv_eta;
      TextView tvEta = ViewBindings.findChildViewById(rootView, id);
      if (tvEta == null) {
        break missingId;
      }

      id = R.id.tv_file_name;
      TextView tvFileName = ViewBindings.findChildViewById(rootView, id);
      if (tvFileName == null) {
        break missingId;
      }

      id = R.id.tv_file_size;
      TextView tvFileSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFileSize == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      id = R.id.tv_speed;
      TextView tvSpeed = ViewBindings.findChildViewById(rootView, id);
      if (tvSpeed == null) {
        break missingId;
      }

      return new DialogDownloadProgressBinding((LinearLayout) rootView, btnCancel, progressBar,
          tvDownloadTitle, tvEta, tvFileName, tvFileSize, tvProgress, tvSpeed);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
