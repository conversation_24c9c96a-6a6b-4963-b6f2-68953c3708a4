<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Download Title -->
    <TextView
        android:id="@+id/tv_download_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Downloading APK + OBB"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- File Name -->
    <TextView
        android:id="@+id/tv_file_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="pubg-mobile-gl.apk"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginBottom="16dp"
        android:progressTint="@color/accent"
        android:progressBackgroundTint="@color/background_light"
        android:max="100"
        android:progress="0" />

    <!-- Progress Info Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="8dp">

        <!-- Progress Percentage -->
        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="0%"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Download Speed -->
        <TextView
            android:id="@+id/tv_speed"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0 MB/s"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- File Size and ETA Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <!-- File Size -->
        <TextView
            android:id="@+id/tv_file_size"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="0 / 0 MB"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

        <!-- ETA -->
        <TextView
            android:id="@+id/tv_eta"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Calculating..."
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- Cancel Button -->
    <Button
        android:id="@+id/btn_cancel"
        style="@style/Widget.BearLoader.Button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Cancel"
        android:textColor="@color/text_primary" />

</LinearLayout>
