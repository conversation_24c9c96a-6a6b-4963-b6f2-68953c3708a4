{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-ta/values-ta.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "967,1080,1182,1297,1386,1497,1618,1697,1773,1871,1971,2066,2160,2267,2367,2469,2563,2661,2759,2840,2948,3051,3150,3266,3369,3474,3631,14731", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "1075,1177,1292,1381,1492,1613,1692,1768,1866,1966,2061,2155,2262,2362,2464,2558,2656,2754,2835,2943,3046,3145,3261,3364,3469,3626,3728,14808"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,789,878,959,1064,1168,1247,1313,1409,1506,1577,1642,1704,1776,1923,2066,2215,2284,2368,2441,2521,2623,2725,2792,2860,2913,2976,3024,3085,3152,3217,3278,3347,3410,3473,3539,3602,3669,3723,3787,3865,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,784,873,954,1059,1163,1242,1308,1404,1501,1572,1637,1699,1771,1918,2061,2210,2279,2363,2436,2516,2618,2720,2787,2855,2908,2971,3019,3080,3147,3212,3273,3342,3405,3468,3534,3597,3664,3718,3782,3860,3938,3994"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,600,5368,5457,5546,5627,5732,5836,5915,5981,6077,6174,6245,6310,6372,6444,6591,6734,6883,6952,7036,7109,7189,7291,7393,7460,8236,8289,8352,8400,8461,8528,8593,8654,8723,8786,8849,8915,8978,9045,9099,9163,9241,9319", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "375,595,790,5452,5541,5622,5727,5831,5910,5976,6072,6169,6240,6305,6367,6439,6586,6729,6878,6947,7031,7104,7184,7286,7388,7455,7523,8284,8347,8395,8456,8523,8588,8649,8718,8781,8844,8910,8973,9040,9094,9158,9236,9314,9370"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1097,1161,1269,1337,1398,1506,1573,1659,1717,1801,1868,1922,2045,2107,2170,2224,2312,2440,2526,2618,2721,2813,2895,3027,3107,3188,3344,3433,3517,3574,3626,3692,3777,3865,3936,4016,4085,4162,4242,4310,4425,4524,4607,4699,4793,4867,4953,5047,5097,5180,5246,5331,5418,5481,5546,5609,5678,5786,5884,5982,6079,6140,6196,6282,6374,6457", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "267,355,441,525,628,722,831,949,1033,1092,1156,1264,1332,1393,1501,1568,1654,1712,1796,1863,1917,2040,2102,2165,2219,2307,2435,2521,2613,2716,2808,2890,3022,3102,3183,3339,3428,3512,3569,3621,3687,3772,3860,3931,4011,4080,4157,4237,4305,4420,4519,4602,4694,4788,4862,4948,5042,5092,5175,5241,5326,5413,5476,5541,5604,5673,5781,5879,5977,6074,6135,6191,6277,6369,6452,6534"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "795,3733,3821,3907,3991,4094,4934,5043,5161,5245,5304,9375,9483,9551,9612,9720,9787,9873,9931,10015,10082,10136,10259,10321,10384,10438,10526,10654,10740,10832,10935,11027,11109,11241,11321,11402,11558,11647,11731,11788,11840,11906,11991,12079,12150,12230,12299,12376,12456,12524,12639,12738,12821,12913,13007,13081,13167,13261,13311,13394,13460,13545,13632,13695,13760,13823,13892,14000,14098,14196,14293,14354,14645,14813,14905,14988", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,87,85,83,102,93,108,117,83,58,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,91,102,91,81,131,79,80,155,88,83,56,51,65,84,87,70,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85,91,82,81", "endOffsets": "962,3816,3902,3986,4089,4183,5038,5156,5240,5299,5363,9478,9546,9607,9715,9782,9868,9926,10010,10077,10131,10254,10316,10379,10433,10521,10649,10735,10827,10930,11022,11104,11236,11316,11397,11553,11642,11726,11783,11835,11901,11986,12074,12145,12225,12294,12371,12451,12519,12634,12733,12816,12908,13002,13076,13162,13256,13306,13389,13455,13540,13627,13690,13755,13818,13887,13995,14093,14191,14288,14349,14405,14726,14900,14983,15065"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7528,7597,7670,7739,7809,7891,7972,8069,8154", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "7592,7665,7734,7804,7886,7967,8064,8149,8231"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "14410,14518", "endColumns": "107,126", "endOffsets": "14513,14640"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4188,4284,4387,4486,4584,4691,4806,15070", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "4279,4382,4481,4579,4686,4801,4929,15166"}}]}]}