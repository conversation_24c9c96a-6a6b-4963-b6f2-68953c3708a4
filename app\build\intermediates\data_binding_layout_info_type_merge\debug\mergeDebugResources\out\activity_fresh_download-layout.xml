<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fresh_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_fresh_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_fresh_download_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="634" endOffset="14"/></Target><Target id="@+id/tab_global" view="LinearLayout"><Expressions/><location startLine="63" startOffset="8" endLine="88" endOffset="22"/></Target><Target id="@+id/tab_kr" view="LinearLayout"><Expressions/><location startLine="90" startOffset="8" endLine="115" endOffset="22"/></Target><Target id="@+id/tab_tw" view="LinearLayout"><Expressions/><location startLine="117" startOffset="8" endLine="142" endOffset="22"/></Target><Target id="@+id/tab_vn" view="LinearLayout"><Expressions/><location startLine="144" startOffset="8" endLine="168" endOffset="22"/></Target><Target id="@+id/card_global" view="LinearLayout"><Expressions/><location startLine="185" startOffset="12" endLine="281" endOffset="26"/></Target><Target id="@+id/btn_download_global" view="Button"><Expressions/><location startLine="257" startOffset="20" endLine="266" endOffset="56"/></Target><Target id="@+id/btn_update_global" view="Button"><Expressions/><location startLine="268" startOffset="20" endLine="277" endOffset="58"/></Target><Target id="@+id/card_kr" view="LinearLayout"><Expressions/><location startLine="284" startOffset="12" endLine="380" endOffset="26"/></Target><Target id="@+id/btn_download_kr" view="Button"><Expressions/><location startLine="356" startOffset="20" endLine="365" endOffset="56"/></Target><Target id="@+id/btn_update_kr" view="Button"><Expressions/><location startLine="367" startOffset="20" endLine="376" endOffset="58"/></Target><Target id="@+id/card_tw" view="LinearLayout"><Expressions/><location startLine="383" startOffset="12" endLine="479" endOffset="26"/></Target><Target id="@+id/btn_download_tw" view="Button"><Expressions/><location startLine="455" startOffset="20" endLine="464" endOffset="56"/></Target><Target id="@+id/btn_update_tw" view="Button"><Expressions/><location startLine="466" startOffset="20" endLine="475" endOffset="58"/></Target><Target id="@+id/card_vn" view="LinearLayout"><Expressions/><location startLine="482" startOffset="12" endLine="578" endOffset="26"/></Target><Target id="@+id/btn_download_vn" view="Button"><Expressions/><location startLine="554" startOffset="20" endLine="563" endOffset="56"/></Target><Target id="@+id/btn_update_vn" view="Button"><Expressions/><location startLine="565" startOffset="20" endLine="574" endOffset="58"/></Target><Target id="@+id/action_logout" view="TextView"><Expressions/><location startLine="593" startOffset="8" endLine="604" endOffset="36"/></Target><Target id="@+id/action_clear_data" view="TextView"><Expressions/><location startLine="606" startOffset="8" endLine="617" endOffset="36"/></Target><Target id="@+id/action_help" view="TextView"><Expressions/><location startLine="619" startOffset="8" endLine="630" endOffset="36"/></Target></Targets></Layout>