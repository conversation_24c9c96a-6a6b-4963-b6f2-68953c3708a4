<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fresh_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_fresh_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_fresh_download_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="776" endOffset="14"/></Target><Target id="@+id/btn_back" view="ImageView"><Expressions/><location startLine="18" startOffset="8" endLine="27" endOffset="35"/></Target><Target id="@+id/tab_global" view="LinearLayout"><Expressions/><location startLine="72" startOffset="8" endLine="98" endOffset="22"/></Target><Target id="@+id/tab_kr" view="LinearLayout"><Expressions/><location startLine="100" startOffset="8" endLine="126" endOffset="22"/></Target><Target id="@+id/tab_tw" view="LinearLayout"><Expressions/><location startLine="128" startOffset="8" endLine="154" endOffset="22"/></Target><Target id="@+id/tab_vn" view="LinearLayout"><Expressions/><location startLine="156" startOffset="8" endLine="181" endOffset="22"/></Target><Target id="@+id/card_global" view="LinearLayout"><Expressions/><location startLine="198" startOffset="12" endLine="311" endOffset="26"/></Target><Target id="@+id/btn_download_global" view="Button"><Expressions/><location startLine="285" startOffset="20" endLine="295" endOffset="66"/></Target><Target id="@+id/btn_update_global" view="Button"><Expressions/><location startLine="297" startOffset="20" endLine="307" endOffset="52"/></Target><Target id="@+id/card_kr" view="LinearLayout"><Expressions/><location startLine="314" startOffset="12" endLine="430" endOffset="26"/></Target><Target id="@+id/btn_download_kr" view="Button"><Expressions/><location startLine="404" startOffset="20" endLine="414" endOffset="66"/></Target><Target id="@+id/btn_update_kr" view="Button"><Expressions/><location startLine="416" startOffset="20" endLine="426" endOffset="66"/></Target><Target id="@+id/card_tw" view="LinearLayout"><Expressions/><location startLine="433" startOffset="12" endLine="549" endOffset="26"/></Target><Target id="@+id/btn_download_tw" view="Button"><Expressions/><location startLine="523" startOffset="20" endLine="533" endOffset="66"/></Target><Target id="@+id/btn_update_tw" view="Button"><Expressions/><location startLine="535" startOffset="20" endLine="545" endOffset="66"/></Target><Target id="@+id/card_vn" view="LinearLayout"><Expressions/><location startLine="552" startOffset="12" endLine="668" endOffset="26"/></Target><Target id="@+id/btn_download_vn" view="Button"><Expressions/><location startLine="642" startOffset="20" endLine="652" endOffset="66"/></Target><Target id="@+id/btn_update_vn" view="Button"><Expressions/><location startLine="654" startOffset="20" endLine="664" endOffset="66"/></Target><Target id="@+id/action_logout" view="LinearLayout"><Expressions/><location startLine="685" startOffset="8" endLine="713" endOffset="22"/></Target><Target id="@+id/action_clear_data" view="LinearLayout"><Expressions/><location startLine="715" startOffset="8" endLine="743" endOffset="22"/></Target><Target id="@+id/action_help" view="LinearLayout"><Expressions/><location startLine="745" startOffset="8" endLine="772" endOffset="22"/></Target></Targets></Layout>