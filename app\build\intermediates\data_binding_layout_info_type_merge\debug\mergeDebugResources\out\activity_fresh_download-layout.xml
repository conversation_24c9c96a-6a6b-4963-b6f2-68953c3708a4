<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_fresh_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_fresh_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_fresh_download_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="771" endOffset="14"/></Target><Target id="@+id/tab_global" view="LinearLayout"><Expressions/><location startLine="67" startOffset="8" endLine="93" endOffset="22"/></Target><Target id="@+id/tab_kr" view="LinearLayout"><Expressions/><location startLine="95" startOffset="8" endLine="121" endOffset="22"/></Target><Target id="@+id/tab_tw" view="LinearLayout"><Expressions/><location startLine="123" startOffset="8" endLine="149" endOffset="22"/></Target><Target id="@+id/tab_vn" view="LinearLayout"><Expressions/><location startLine="151" startOffset="8" endLine="176" endOffset="22"/></Target><Target id="@+id/card_global" view="LinearLayout"><Expressions/><location startLine="193" startOffset="12" endLine="306" endOffset="26"/></Target><Target id="@+id/btn_download_global" view="Button"><Expressions/><location startLine="280" startOffset="20" endLine="290" endOffset="66"/></Target><Target id="@+id/btn_update_global" view="Button"><Expressions/><location startLine="292" startOffset="20" endLine="302" endOffset="52"/></Target><Target id="@+id/card_kr" view="LinearLayout"><Expressions/><location startLine="309" startOffset="12" endLine="425" endOffset="26"/></Target><Target id="@+id/btn_download_kr" view="Button"><Expressions/><location startLine="399" startOffset="20" endLine="409" endOffset="66"/></Target><Target id="@+id/btn_update_kr" view="Button"><Expressions/><location startLine="411" startOffset="20" endLine="421" endOffset="66"/></Target><Target id="@+id/card_tw" view="LinearLayout"><Expressions/><location startLine="428" startOffset="12" endLine="544" endOffset="26"/></Target><Target id="@+id/btn_download_tw" view="Button"><Expressions/><location startLine="518" startOffset="20" endLine="528" endOffset="66"/></Target><Target id="@+id/btn_update_tw" view="Button"><Expressions/><location startLine="530" startOffset="20" endLine="540" endOffset="66"/></Target><Target id="@+id/card_vn" view="LinearLayout"><Expressions/><location startLine="547" startOffset="12" endLine="663" endOffset="26"/></Target><Target id="@+id/btn_download_vn" view="Button"><Expressions/><location startLine="637" startOffset="20" endLine="647" endOffset="66"/></Target><Target id="@+id/btn_update_vn" view="Button"><Expressions/><location startLine="649" startOffset="20" endLine="659" endOffset="66"/></Target><Target id="@+id/action_logout" view="LinearLayout"><Expressions/><location startLine="680" startOffset="8" endLine="708" endOffset="22"/></Target><Target id="@+id/action_clear_data" view="LinearLayout"><Expressions/><location startLine="710" startOffset="8" endLine="738" endOffset="22"/></Target><Target id="@+id/action_help" view="LinearLayout"><Expressions/><location startLine="740" startOffset="8" endLine="767" endOffset="22"/></Target></Targets></Layout>