<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="216" endOffset="43"/></Target><Target tag="binding_1" view="LinearLayout"><Expressions/><location startLine="138" startOffset="16" endLine="148" endOffset="30"/></Target><Target tag="binding_1" include="item_patch_shimmer"><Expressions/><location startLine="143" startOffset="20" endLine="143" endOffset="66"/></Target><Target tag="binding_1" include="item_patch_shimmer"><Expressions/><location startLine="144" startOffset="20" endLine="144" endOffset="66"/></Target><Target tag="binding_1" include="item_patch_shimmer"><Expressions/><location startLine="145" startOffset="20" endLine="145" endOffset="66"/></Target><Target tag="binding_1" include="item_patch_shimmer"><Expressions/><location startLine="146" startOffset="20" endLine="146" endOffset="66"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="20" startOffset="12" endLine="27" endOffset="51"/></Target><Target id="@+id/card_target_app" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="38" startOffset="12" endLine="85" endOffset="63"/></Target><Target id="@+id/spinner_target_app" view="Spinner"><Expressions/><location startLine="66" startOffset="20" endLine="73" endOffset="47"/></Target><Target id="@+id/btn_scan_offsets" view="Button"><Expressions/><location startLine="75" startOffset="20" endLine="81" endOffset="61"/></Target><Target id="@+id/tv_available_patches" view="TextView"><Expressions/><location startLine="88" startOffset="12" endLine="99" endOffset="76"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="101" startOffset="12" endLine="120" endOffset="67"/></Target><Target id="@+id/rv_patches" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="114" startOffset="16" endLine="118" endOffset="57"/></Target><Target id="@+id/shimmer_layout" view="com.facebook.shimmer.ShimmerFrameLayout"><Expressions/><location startLine="123" startOffset="12" endLine="150" endOffset="53"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="153" startOffset="12" endLine="187" endOffset="26"/></Target><Target id="@+id/btn_download_patches" view="Button"><Expressions/><location startLine="179" startOffset="16" endLine="185" endOffset="61"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="191" startOffset="8" endLine="200" endOffset="33"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="204" startOffset="4" endLine="214" endOffset="38"/></Target></Targets></Layout>