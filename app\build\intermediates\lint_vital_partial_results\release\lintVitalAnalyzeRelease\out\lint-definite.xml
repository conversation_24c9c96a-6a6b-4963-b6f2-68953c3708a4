<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="MissingDefaultResource"
        severity="fatal"
        message="The color &quot;card_bg&quot; in values-night has no declaration in the base `values` folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier">
        <fix-replace
            description="Remove resource override"
            replacement=""
            priority="0">
            <range
                file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
                startOffset="149"
                endOffset="186"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="26"
            endOffset="170"/>
    </incident>

    <incident
        id="MissingDefaultResource"
        severity="fatal"
        message="The color &quot;surface_container_lowest&quot; in values-night has no declaration in the base `values` folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier">
        <fix-replace
            description="Remove resource override"
            replacement=""
            priority="0">
            <range
                file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
                startOffset="389"
                endOffset="443"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="11"
            column="12"
            startOffset="396"
            endLine="11"
            endColumn="43"
            endOffset="427"/>
    </incident>

    <incident
        id="MissingDefaultResource"
        severity="fatal"
        message="The color &quot;surface_container_highest&quot; in values-night has no declaration in the base `values` folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier">
        <fix-replace
            description="Remove resource override"
            replacement=""
            priority="0">
            <range
                file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
                startOffset="507"
                endOffset="562"/>
        </fix-replace>
        <location
            file="${:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="13"
            column="12"
            startOffset="514"
            endLine="13"
            endColumn="44"
            endOffset="546"/>
    </incident>

</incidents>
