package com.bearmod;

import android.content.Context;
import android.util.Log;

/**
 * Native library interface for BearMod
 * This class provides the bridge between Java and native C++ code
 */
public class NativeLib {
    
    private static final String TAG = "NativeLib";
    private static boolean isLibraryLoaded = false;
    
    static {
        try {
            System.loadLibrary("bearmod");
            isLibraryLoaded = true;
            Log.d(TAG, "BearMod native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load BearMod native library", e);
            isLibraryLoaded = false;
        }
    }
    
    /**
     * Check if native library is available
     * @return true if native library is loaded, false otherwise
     */
    public static boolean isNativeLibraryAvailable() {
        return isLibraryLoaded;
    }
    
    /**
     * Initialize BearMod system
     * @param context Application context
     */
    public static void initialize(Context context) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Cannot initialize - native library not loaded");
            return;
        }
        
        try {
            nativeInitialize(context);
            Log.d(TAG, "BearMod system initialized");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize BearMod system", e);
        }
    }
    
    /**
     * Verify if app was launched by trusted BearMod Loader
     * @param context Application context
     * @return true if launched by trusted loader, false otherwise
     */
    public static boolean verifyTrustedLaunch(Context context) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Cannot verify trusted launch - native library not loaded");
            return false;
        }
        
        try {
            return nativeVerifyTrustedLaunch(context);
        } catch (Exception e) {
            Log.e(TAG, "Failed to verify trusted launch", e);
            return false;
        }
    }
    
    /**
     * Start patching with trust level
     * @param context Application context
     * @param isTrusted Whether the app was launched by trusted loader
     */
    public static void startPatching(Context context, boolean isTrusted) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Cannot start patching - native library not loaded");
            return;
        }
        
        try {
            nativeStartPatching(context, isTrusted);
            Log.d(TAG, "Patching started - Trusted: " + isTrusted);
        } catch (Exception e) {
            Log.e(TAG, "Failed to start patching", e);
        }
    }
    
    /**
     * Get BearMod version information
     * @return Version string
     */
    public static String getVersionInfo() {
        if (!isLibraryLoaded) {
            return "Native library not available";
        }
        
        try {
            return nativeGetVersionInfo();
        } catch (Exception e) {
            Log.e(TAG, "Failed to get version info", e);
            return "Error getting version";
        }
    }
    
    /**
     * Perform security check
     * @param context Application context
     * @return true if security check passed, false otherwise
     */
    public static boolean performSecurityCheck(Context context) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Cannot perform security check - native library not loaded");
            return false;
        }
        
        try {
            return nativePerformSecurityCheck(context);
        } catch (Exception e) {
            Log.e(TAG, "Security check failed", e);
            return false;
        }
    }
    
    /**
     * Complete authentication and initialization for target mods
     * Call this in your target mod's Application.onCreate()
     * 
     * @param context Application context
     * @return AuthResult with authentication status and trust level
     */
    public static AuthResult authenticateAndInitialize(Context context) {
        if (!isLibraryLoaded) {
            return new AuthResult(false, false, "Native library not available");
        }
        
        try {
            // Initialize system
            initialize(context);
            
            // Perform security check
            if (!performSecurityCheck(context)) {
                return new AuthResult(false, false, "Security check failed");
            }
            
            // Check if launched by trusted loader
            boolean isTrusted = verifyTrustedLaunch(context);
            
            if (isTrusted) {
                Log.d(TAG, "Trusted launch detected - enhanced features available");
                startPatching(context, true);
                return new AuthResult(true, true, "Authenticated via trusted BearMod Loader");
            } else {
                Log.d(TAG, "Standard launch - using normal features");
                startPatching(context, false);
                return new AuthResult(true, false, "Standard authentication");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Authentication and initialization failed", e);
            return new AuthResult(false, false, "Authentication error: " + e.getMessage());
        }
    }
    
    // Native method declarations
    private static native void nativeInitialize(Context context);
    private static native boolean nativeVerifyTrustedLaunch(Context context);
    private static native void nativeStartPatching(Context context, boolean isTrusted);
    private static native String nativeGetVersionInfo();
    private static native boolean nativePerformSecurityCheck(Context context);
    
    /**
     * Authentication result class
     */
    public static class AuthResult {
        public final boolean isAuthenticated;
        public final boolean isTrusted;
        public final String message;
        
        public AuthResult(boolean isAuthenticated, boolean isTrusted, String message) {
            this.isAuthenticated = isAuthenticated;
            this.isTrusted = isTrusted;
            this.message = message;
        }
        
        /**
         * Check if KeyAuth can be skipped
         */
        public boolean canSkipKeyAuth() {
            return isAuthenticated && isTrusted;
        }
        
        @Override
        public String toString() {
            return "AuthResult{" +
                    "isAuthenticated=" + isAuthenticated +
                    ", isTrusted=" + isTrusted +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
}

/*
USAGE EXAMPLE FOR TARGET MOD APPS:

// In your target mod's Application class:
public class YourModApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // Authenticate and initialize with BearMod
        NativeLib.AuthResult result = NativeLib.authenticateAndInitialize(this);
        
        if (result.isAuthenticated) {
            if (result.canSkipKeyAuth()) {
                Log.d("YourMod", "Skipping KeyAuth - trusted launch detected");
                // Start your mod directly with enhanced features
                startModFunctionality(true);
            } else {
                Log.d("YourMod", "Using KeyAuth authentication");
                // Perform KeyAuth authentication
                performKeyAuthThenStartMod();
            }
        } else {
            Log.e("YourMod", "Authentication failed: " + result.message);
            System.exit(1);
        }
    }
    
    private void startModFunctionality(boolean enhancedMode) {
        // Your mod implementation
        // NativeLib.startPatching(this, enhancedMode);
    }
    
    private void performKeyAuthThenStartMod() {
        // Your KeyAuth implementation
        // After successful KeyAuth, call startModFunctionality(false)
    }
}
*/
