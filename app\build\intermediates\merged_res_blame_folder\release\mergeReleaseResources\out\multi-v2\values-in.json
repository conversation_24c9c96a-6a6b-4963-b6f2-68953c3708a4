{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-59:/values-in/values-in.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,3617,3696,3772,3851,3941,4756,4862,4978,5061,5125,8949,9043,9108,9167,9254,9316,9378,9438,9504,9566,9620,9732,9789,9850,9904,9976,10102,10188,10266,10359,10445,10529,10668,10749,10830,10965,11055,11137,11190,11242,11308,11380,11464,11535,11615,11690,11766,11839,11914,12012,12097,12172,12264,12358,12432,12505,12599,12651,12733,12802,12887,12974,13036,13100,13163,13235,13338,13443,13538,13641,13698,13978,14143,14224,14302", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "905,3691,3767,3846,3936,4021,4857,4973,5056,5120,5185,9038,9103,9162,9249,9311,9373,9433,9499,9561,9615,9727,9784,9845,9899,9971,10097,10183,10261,10354,10440,10524,10663,10744,10825,10960,11050,11132,11185,11237,11303,11375,11459,11530,11610,11685,11761,11834,11909,12007,12092,12167,12259,12353,12427,12500,12594,12646,12728,12797,12882,12969,13031,13095,13158,13230,13333,13438,13533,13636,13693,13749,14053,14219,14297,14375"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7164,7232,7294,7359,7422,7498,7562,7662,7756", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "7227,7289,7354,7417,7493,7557,7657,7751,7820"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4026,4121,4223,4320,4417,4523,4641,14380", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "4116,4218,4315,4412,4518,4636,4751,14476"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,113", "endOffsets": "160,274"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "13754,13864", "endColumns": "109,113", "endOffsets": "13859,13973"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "910,1025,1129,1237,1324,1428,1544,1627,1705,1796,1889,1984,2078,2178,2271,2366,2460,2551,2642,2728,2831,2936,3037,3141,3250,3358,3518,14058", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "1020,1124,1232,1319,1423,1539,1622,1700,1791,1884,1979,2073,2173,2266,2361,2455,2546,2637,2723,2826,2931,3032,3136,3245,3353,3513,3612,14138"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1789,1907,2023,2095,2175,2245,2319,2403,2489,2556,2620,2673,2731,2779,2840,2904,2966,3029,3095,3157,3220,3286,3350,3416,3468,3530,3606,3682", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1784,1902,2018,2090,2170,2240,2314,2398,2484,2551,2615,2668,2726,2774,2835,2899,2961,3024,3090,3152,3215,3281,3345,3411,3463,3525,3601,3677,3739"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,567,5190,5273,5358,5434,5522,5615,5692,5761,5857,5951,6015,6079,6145,6218,6333,6451,6567,6639,6719,6789,6863,6947,7033,7100,7825,7878,7936,7984,8045,8109,8171,8234,8300,8362,8425,8491,8555,8621,8673,8735,8811,8887", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "375,562,736,5268,5353,5429,5517,5610,5687,5756,5852,5946,6010,6074,6140,6213,6328,6446,6562,6634,6714,6784,6858,6942,7028,7095,7159,7873,7931,7979,8040,8104,8166,8229,8295,8357,8420,8486,8550,8616,8668,8730,8806,8882,8944"}}]}]}