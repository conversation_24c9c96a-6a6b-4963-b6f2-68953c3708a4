[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx\\Debug\\6z2l3s6b\\x86_64\\android_gradle_build.json' was up-to-date", "file_": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]