import android.graphics.Color
import android.os.Bundle
import android.view.animation.AnimationUtils
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import com.bearmod.loader.R
import com.bearmod.loader.databinding.ActivityLoginBinding

class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Apply window insets
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Animate UI elements
        val views = listOf(
            binding.logoContainer,
            binding.appNameText,
            binding.textInputLayout,
            binding.checkboxRemember,
            binding.checkboxAutoLogin,
            binding.btnLogin,
            binding.versionText
        )
        
        views.forEachIndexed { index, view ->
            view.startAnimation(AnimationUtils.loadAnimation(this, R.anim.fade_slide_up).apply {
                startOffset = index * 100L
            })
        }

        // Set status bar and navigation bar colors
        window.statusBarColor = Color.TRANSPARENT
        window.navigationBarColor = Color.TRANSPARENT
    }
} 