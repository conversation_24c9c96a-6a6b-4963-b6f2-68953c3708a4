{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-nl/values-nl.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\21d61e9b45cfab2ee4b69c56a0b52398\\transformed\\appcompat-1.7.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,328,435,520,624,744,822,898,990,1084,1179,1273,1373,1467,1563,1658,1750,1842,1924,2035,2138,2237,2352,2466,2569,2724,2827", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "218,323,430,515,619,739,817,893,985,1079,1174,1268,1368,1462,1558,1653,1745,1837,1919,2030,2133,2232,2347,2461,2564,2719,2822,2905"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "941,1059,1164,1271,1356,1460,1580,1658,1734,1826,1920,2015,2109,2209,2303,2399,2494,2586,2678,2760,2871,2974,3073,3188,3302,3405,3560,14214", "endColumns": "117,104,106,84,103,119,77,75,91,93,94,93,99,93,95,94,91,91,81,110,102,98,114,113,102,154,102,82", "endOffsets": "1054,1159,1266,1351,1455,1575,1653,1729,1821,1915,2010,2104,2204,2298,2394,2489,2581,2673,2755,2866,2969,3068,3183,3297,3400,3555,3658,14292"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8fcff5946e9cb42bb9e1450a210c2616\\transformed\\navigation-ui-2.9.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,108", "endOffsets": "161,270"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "13913,14024", "endColumns": "110,108", "endOffsets": "14019,14128"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\eef25481716fc7890be7aca21c0506bf\\transformed\\exoplayer-core-2.19.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,190,254,321,398,467,556,639", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "121,185,249,316,393,462,551,634,706"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7255,7326,7390,7454,7521,7598,7667,7756,7839", "endColumns": "70,63,63,66,76,68,88,82,71", "endOffsets": "7321,7385,7449,7516,7593,7662,7751,7834,7906"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8c95fb1bf71448479a8aeb756e068ef5\\transformed\\exoplayer-ui-2.19.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,486,677,766,854,934,1027,1120,1193,1260,1362,1460,1528,1595,1660,1729,1848,1967,2082,2155,2234,2309,2378,2461,2543,2609,2674,2727,2785,2833,2894,2959,3021,3086,3154,3212,3270,3336,3401,3467,3519,3581,3657,3733", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "281,481,672,761,849,929,1022,1115,1188,1255,1357,1455,1523,1590,1655,1724,1843,1962,2077,2150,2229,2304,2373,2456,2538,2604,2669,2722,2780,2828,2889,2954,3016,3081,3149,3207,3265,3331,3396,3462,3514,3576,3652,3728,3783"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,581,5258,5347,5435,5515,5608,5701,5774,5841,5943,6041,6109,6176,6241,6310,6429,6548,6663,6736,6815,6890,6959,7042,7124,7190,7911,7964,8022,8070,8131,8196,8258,8323,8391,8449,8507,8573,8638,8704,8756,8818,8894,8970", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,88,87,79,92,92,72,66,101,97,67,66,64,68,118,118,114,72,78,74,68,82,81,65,64,52,57,47,60,64,61,64,67,57,57,65,64,65,51,61,75,75,54", "endOffsets": "376,576,767,5342,5430,5510,5603,5696,5769,5836,5938,6036,6104,6171,6236,6305,6424,6543,6658,6731,6810,6885,6954,7037,7119,7185,7250,7959,8017,8065,8126,8191,8253,8318,8386,8444,8502,8568,8633,8699,8751,8813,8889,8965,9020"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\50cbe4bff5d1390b137919b7462144e4\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4100,4202,4304,4404,4504,4611,4715,14528", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "4197,4299,4399,4499,4606,4710,4829,14624"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\9f99db3bdee5091140633361bed826be\\transformed\\material-1.12.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,355,437,514,612,706,803,925,1006,1066,1130,1219,1298,1361,1454,1516,1582,1640,1713,1777,1833,1955,2012,2074,2130,2206,2340,2425,2504,2602,2688,2774,2912,2993,3072,3196,3286,3363,3420,3471,3537,3615,3698,3769,3845,3920,3999,4072,4143,4252,4346,4424,4513,4603,4677,4758,4845,4898,4977,5044,5125,5209,5271,5335,5398,5469,5577,5689,5791,5902,5963,6018,6099,6182,6258", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "264,350,432,509,607,701,798,920,1001,1061,1125,1214,1293,1356,1449,1511,1577,1635,1708,1772,1828,1950,2007,2069,2125,2201,2335,2420,2499,2597,2683,2769,2907,2988,3067,3191,3281,3358,3415,3466,3532,3610,3693,3764,3840,3915,3994,4067,4138,4247,4341,4419,4508,4598,4672,4753,4840,4893,4972,5039,5120,5204,5266,5330,5393,5464,5572,5684,5786,5897,5958,6013,6094,6177,6253,6325"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,3663,3749,3831,3908,4006,4834,4931,5053,5134,5194,9025,9114,9193,9256,9349,9411,9477,9535,9608,9672,9728,9850,9907,9969,10025,10101,10235,10320,10399,10497,10583,10669,10807,10888,10967,11091,11181,11258,11315,11366,11432,11510,11593,11664,11740,11815,11894,11967,12038,12147,12241,12319,12408,12498,12572,12653,12740,12793,12872,12939,13020,13104,13166,13230,13293,13364,13472,13584,13686,13797,13858,14133,14297,14380,14456", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,85,81,76,97,93,96,121,80,59,63,88,78,62,92,61,65,57,72,63,55,121,56,61,55,75,133,84,78,97,85,85,137,80,78,123,89,76,56,50,65,77,82,70,75,74,78,72,70,108,93,77,88,89,73,80,86,52,78,66,80,83,61,63,62,70,107,111,101,110,60,54,80,82,75,71", "endOffsets": "936,3744,3826,3903,4001,4095,4926,5048,5129,5189,5253,9109,9188,9251,9344,9406,9472,9530,9603,9667,9723,9845,9902,9964,10020,10096,10230,10315,10394,10492,10578,10664,10802,10883,10962,11086,11176,11253,11310,11361,11427,11505,11588,11659,11735,11810,11889,11962,12033,12142,12236,12314,12403,12493,12567,12648,12735,12788,12867,12934,13015,13099,13161,13225,13288,13359,13467,13579,13681,13792,13853,13908,14209,14375,14451,14523"}}]}]}