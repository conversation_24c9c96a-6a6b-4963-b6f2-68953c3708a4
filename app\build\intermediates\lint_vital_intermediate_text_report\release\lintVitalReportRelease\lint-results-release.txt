D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_main.xml:110: Error: @+id/bottom_navigation is not a sibling in the same ConstraintLayout [NotSibling]
                app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_main.xml:133: Error: @+id/bottom_navigation is not a sibling in the same ConstraintLayout [NotSibling]
                app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_main.xml:161: Error: @+id/bottom_navigation is not a sibling in the same ConstraintLayout [NotSibling]
                app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotSibling":
   Layout constraints in a given ConstraintLayout or RelativeLayout should
   reference other views within the same relative layout (but not itself!)

3 errors
