<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
    <!-- PUBG KR - Korean flag inspired with PUBG elements -->
    <path
        android:fillColor="#58A6FF"
        android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2zM12,20c-4.41,0 -8,-3.59 -8,-8s3.59,-8 8,-8 8,3.59 8,8 -3.59,8 -8,8z"/>
    <!-- <PERSON><PERSON><PERSON> center (Korean flag element) -->
    <path
        android:fillColor="#F85149"
        android:pathData="M12,8c-1.1,0 -2,0.9 -2,2c0,0.55 0.22,1.05 0.59,1.41C10.95,11.78 11.45,12 12,12c1.1,0 2,-0.9 2,-2S13.1,8 12,8z"/>
    <path
        android:fillColor="#00D084"
        android:pathData="M12,12c-1.1,0 -2,0.9 -2,2s0.9,2 2,2c0.55,0 1.05,-0.22 1.41,-0.59C13.78,15.05 14,14.55 14,14C14,12.9 13.1,12 12,12z"/>
    <!-- Trigram lines (Korean flag elements) -->
    <path
        android:fillColor="#58A6FF"
        android:pathData="M7,9h2v1h-2zM7,11h2v1h-2zM7,13h2v1h-2z"/>
    <path
        android:fillColor="#58A6FF"
        android:pathData="M15,9h2v1h-2zM15,11h2v1h-2zM15,13h2v1h-2z"/>
    <!-- Additional PUBG crosshair elements -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M11.5,11.5h1v1h-1z"/>
</vector>