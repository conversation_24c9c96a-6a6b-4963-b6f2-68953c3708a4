<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="card_background">#1F1F1F</color>
    <color name="card_bg">#1F1F1F</color>
    <color name="on_surface">#FFFFFF</color>
    <color name="surface">#121212</color>
    <color name="surface_container">#1F1F1F</color>
    <color name="surface_container_high">#2D2D2D</color>
    <color name="surface_container_highest">#424242</color>
    <color name="surface_container_lowest">#0F0F0F</color>
    <color name="surface_variant">#1E1E1E</color>
    <string name="all">All</string>
    <string name="bearmod_loader">Bearmod-Loader</string>
    <string name="clear_data">Clear Data</string>
    <string name="download_size_1_08_gb">Download Size: 1.08 GB</string>
    <string name="downloaded">Downloaded</string>
    <string name="eta">ETA</string>
    <string name="global">Global</string>
    <string name="kr">KR</string>
    <string name="latest">Latest</string>
    <string name="pause">Pause</string>
    <string name="pubg_mobile_global">PUBG MOBILE Global</string>
    <string name="pubg_mobile_kr">PUBG MOBILE KR</string>
    <string name="pubg_mobile_tw">PUBG MOBILE TW</string>
    <string name="pubg_mobile_vn">PUBG MOBILE VN</string>
    <string name="pull_down_to_refresh_or_check_your_connection">Pull down to refresh or check your connection</string>
    <string name="safe">Safe</string>
    <string name="support">Support</string>
    <string name="tw">TW</string>
    <string name="update">Update</string>
    <string name="version_3_8_0_64bit">Version: 3.8.0 • 64BIT:</string>
    <string name="vn">VN</string>
    <style name="Theme.BearLoader" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_light</item>
        <item name="colorOnPrimaryContainer">@color/background</item>

        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/accent_light</item>
        <item name="colorOnSecondaryContainer">@color/background</item>

        
        <item name="colorSurface">@color/card_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/background_light</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/background</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:windowBackground">@color/background</item>
    </style>
</resources>