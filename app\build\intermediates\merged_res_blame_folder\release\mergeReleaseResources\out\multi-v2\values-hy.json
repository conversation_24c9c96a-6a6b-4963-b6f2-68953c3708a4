{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-59:/values-hy/values-hy.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\377aab835c73943f35a2dc72ba361cea\\transformed\\material-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1044,1108,1205,1290,1352,1439,1501,1565,1626,1693,1754,1808,1930,1987,2047,2101,2182,2317,2401,2477,2567,2646,2731,2867,2942,3017,3160,3255,3335,3391,3444,3510,3584,3663,3734,3817,3888,3964,4040,4117,4223,4311,4391,4487,4583,4657,4735,4835,4886,4970,5039,5126,5217,5279,5343,5406,5477,5582,5688,5788,5891,5951,6008,6093,6176,6250", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "260,336,412,492,584,672,767,897,978,1039,1103,1200,1285,1347,1434,1496,1560,1621,1688,1749,1803,1925,1982,2042,2096,2177,2312,2396,2472,2562,2641,2726,2862,2937,3012,3155,3250,3330,3386,3439,3505,3579,3658,3729,3812,3883,3959,4035,4112,4218,4306,4386,4482,4578,4652,4730,4830,4881,4965,5034,5121,5212,5274,5338,5401,5472,5577,5683,5783,5886,5946,6003,6088,6171,6245,6325"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "754,3628,3704,3780,3860,3952,4760,4855,4985,5066,5127,9006,9103,9188,9250,9337,9399,9463,9524,9591,9652,9706,9828,9885,9945,9999,10080,10215,10299,10375,10465,10544,10629,10765,10840,10915,11058,11153,11233,11289,11342,11408,11482,11561,11632,11715,11786,11862,11938,12015,12121,12209,12289,12385,12481,12555,12633,12733,12784,12868,12937,13024,13115,13177,13241,13304,13375,13480,13586,13686,13789,13849,14129,14297,14380,14454", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "914,3699,3775,3855,3947,4035,4850,4980,5061,5122,5186,9098,9183,9245,9332,9394,9458,9519,9586,9647,9701,9823,9880,9940,9994,10075,10210,10294,10370,10460,10539,10624,10760,10835,10910,11053,11148,11228,11284,11337,11403,11477,11556,11627,11710,11781,11857,11933,12010,12116,12204,12284,12380,12476,12550,12628,12728,12779,12863,12932,13019,13110,13172,13236,13299,13370,13475,13581,13681,13784,13844,13901,14209,14375,14449,14529"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\\transformed\\exoplayer-core-2.19.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7211,7283,7346,7410,7478,7559,7636,7710,7787", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "7278,7341,7405,7473,7554,7631,7705,7782,7860"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\a4fb1c2e1fb11023fe27b3d0a699c4d7\\transformed\\exoplayer-ui-2.19.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1818,1934,2054,2118,2199,2276,2354,2450,2545,2614,2679,2732,2792,2840,2901,2969,3037,3110,3177,3238,3299,3366,3431,3501,3553,3615,3691,3767", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1813,1929,2049,2113,2194,2271,2349,2445,2540,2609,2674,2727,2787,2835,2896,2964,3032,3105,3172,3233,3294,3361,3426,3496,3548,3610,3686,3762,3815"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,572,5191,5274,5356,5426,5517,5613,5689,5752,5853,5956,6026,6094,6162,6228,6350,6466,6586,6650,6731,6808,6886,6982,7077,7146,7865,7918,7978,8026,8087,8155,8223,8296,8363,8424,8485,8552,8617,8687,8739,8801,8877,8953", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,64,69,51,61,75,75,52", "endOffsets": "378,567,749,5269,5351,5421,5512,5608,5684,5747,5848,5951,6021,6089,6157,6223,6345,6461,6581,6645,6726,6803,6881,6977,7072,7141,7206,7913,7973,8021,8082,8150,8218,8291,8358,8419,8480,8547,8612,8682,8734,8796,8872,8948,9001"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\55919508f6a916749b6624a060d17abd\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4040,4140,4245,4343,4442,4547,4649,14534", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "4135,4240,4338,4437,4542,4644,4755,14630"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c331959512d9a33669ba0caa36c41fa5\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1027,1127,1237,1326,1432,1549,1631,1711,1802,1895,1990,2084,2184,2277,2372,2466,2557,2648,2731,2837,2943,3042,3152,3260,3361,3531,14214", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "1022,1122,1232,1321,1427,1544,1626,1706,1797,1890,1985,2079,2179,2272,2367,2461,2552,2643,2726,2832,2938,3037,3147,3255,3356,3526,3623,14292"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8d1253ec0fc93acd53c5fd46d4f60417\\transformed\\navigation-ui-2.9.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "13906,14012", "endColumns": "105,116", "endOffsets": "14007,14124"}}]}]}