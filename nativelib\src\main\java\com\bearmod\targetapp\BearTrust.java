package com.bearmod.targetapp;

import android.content.Context;
import android.util.Log;

/**
 * BearTrust - Native signature verification for target apps
 * This class provides the bridge between Java and native verification
 * Integrated into nativelib module for shared use
 */
public class BearTrust {
    
    private static final String TAG = "BearTrust";
    private static boolean isLibraryLoaded = false;
    private static boolean isContextSet = false;
    
    static {
        try {
            // Load the nativelib library (your existing native library)
            System.loadLibrary("nativelib");
            isLibraryLoaded = true;
            Log.d(TAG, "BearTrust native library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load BearTrust native library", e);
            isLibraryLoaded = false;
        }
    }
    
    /**
     * Initialize BearTrust with application context
     * Call this in your Application.onCreate() or MainActivity.onCreate()
     * 
     * @param context Application context
     */
    public static void initialize(Context context) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Cannot initialize BearTrust - native library not loaded");
            return;
        }
        
        try {
            nativeSetContext(context);
            isContextSet = true;
            Log.d(TAG, "BearTrust initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize BearTrust", e);
            isContextSet = false;
        }
    }
    
    /**
     * Main verification function - checks if app was launched by trusted BearMod Loader
     * This replaces your existing signature verification
     * 
     * @param context Application context
     * @return true if launched by trusted loader, false otherwise
     */
    public static boolean nativeVerify(Context context) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Native library not available for verification");
            return false;
        }
        
        try {
            boolean result = nativeVerifyInternal(context);
            Log.d(TAG, "BearTrust verification result: " + result);
            return result;
        } catch (Exception e) {
            Log.e(TAG, "BearTrust verification failed", e);
            return false;
        }
    }
    
    /**
     * Get signature hash for any package (debugging purposes)
     * 
     * @param context Application context
     * @param packageName Package name to get signature for
     * @return Signature hash or null if failed
     */
    public static String getSignatureHash(Context context, String packageName) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available for signature hash retrieval");
            return null;
        }
        
        try {
            return nativeGetSignatureHash(context, packageName);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get signature hash for " + packageName, e);
            return null;
        }
    }
    
    /**
     * Check if BearTrust is properly initialized
     * 
     * @return true if ready for verification, false otherwise
     */
    public static boolean isInitialized() {
        return isLibraryLoaded && isContextSet;
    }
    
    /**
     * Check if native library is available
     * 
     * @return true if native library is loaded, false otherwise
     */
    public static boolean isNativeLibraryAvailable() {
        return isLibraryLoaded;
    }
    
    /**
     * Log signature information for debugging
     * Call this during development to get signature hashes for configuration
     * 
     * @param context Application context
     */
    public static void logSignatureInformation(Context context) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Cannot log signature information - native library not available");
            return;
        }
        
        Log.d(TAG, "=== BearTrust Signature Information ===");
        
        try {
            // Log current app signature
            String currentAppHash = getSignatureHash(context, context.getPackageName());
            Log.d(TAG, "Current app signature: " + currentAppHash);
            Log.d(TAG, "Add this to TRUSTED_TARGET_HASHES: \"" + currentAppHash + "\",");
            
            // Log BearMod Loader signature if installed
            String loaderHash = getSignatureHash(context, "com.bearmod.loader");
            if (loaderHash != null) {
                Log.d(TAG, "BearMod Loader signature: " + loaderHash);
                Log.d(TAG, "Add this to TRUSTED_LOADER_HASHES: \"" + loaderHash + "\",");
            } else {
                Log.d(TAG, "BearMod Loader not installed or signature unavailable");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error logging signature information", e);
        }
        
        Log.d(TAG, "=== End BearTrust Signature Information ===");
    }
    
    /**
     * Cleanup BearTrust resources
     * Call this when your app is being destroyed
     */
    public static void cleanup() {
        if (!isLibraryLoaded) {
            return;
        }
        
        try {
            nativeCleanup();
            isContextSet = false;
            Log.d(TAG, "BearTrust cleaned up");
        } catch (Exception e) {
            Log.e(TAG, "Error during BearTrust cleanup", e);
        }
    }
    
    /**
     * Comprehensive verification with detailed result
     * 
     * @param context Application context
     * @return VerificationResult with detailed information
     */
    public static VerificationResult performDetailedVerification(Context context) {
        if (!isLibraryLoaded) {
            return new VerificationResult(false, "LIBRARY_ERROR", 
                "Native library not available", 
                "BearTrust native library failed to load");
        }
        
        try {
            boolean result = nativeVerify(context);
            
            if (result) {
                return new VerificationResult(true, "VERIFIED", 
                    "Verification successful", 
                    "App launched by trusted BearMod Loader");
            } else {
                return new VerificationResult(false, "VERIFICATION_FAILED", 
                    "Verification failed", 
                    "App not launched by trusted BearMod Loader or signatures invalid");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Detailed verification error", e);
            return new VerificationResult(false, "VERIFICATION_ERROR", 
                "Verification error: " + e.getMessage(), 
                "An error occurred during verification process");
        }
    }
    
    // Native method declarations
    private static native boolean nativeVerifyInternal(Context context);
    private static native void nativeSetContext(Context context);
    private static native void nativeCleanup();
    private static native String nativeGetSignatureHash(Context context, String packageName);
    
    /**
     * Verification result class
     */
    public static class VerificationResult {
        public final boolean isVerified;
        public final String resultCode;
        public final String message;
        public final String details;
        
        public VerificationResult(boolean isVerified, String resultCode, String message, String details) {
            this.isVerified = isVerified;
            this.resultCode = resultCode;
            this.message = message;
            this.details = details;
        }
        
        /**
         * Check if verification passed
         */
        public boolean isSuccess() {
            return isVerified && "VERIFIED".equals(resultCode);
        }
        
        /**
         * Check if failure was due to library issues
         */
        public boolean isLibraryError() {
            return !isVerified && "LIBRARY_ERROR".equals(resultCode);
        }
        
        /**
         * Check if failure was due to verification issues
         */
        public boolean isVerificationError() {
            return !isVerified && ("VERIFICATION_FAILED".equals(resultCode) || 
                                  "VERIFICATION_ERROR".equals(resultCode));
        }
        
        @Override
        public String toString() {
            return "VerificationResult{" +
                    "isVerified=" + isVerified +
                    ", resultCode='" + resultCode + '\'' +
                    ", message='" + message + '\'' +
                    ", details='" + details + '\'' +
                    '}';
        }
    }
}
