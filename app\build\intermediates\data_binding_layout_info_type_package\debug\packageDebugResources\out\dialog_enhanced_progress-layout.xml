<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_enhanced_progress" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\dialog_enhanced_progress.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_enhanced_progress_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="306" endOffset="14"/></Target><Target id="@+id/iv_game_icon" view="ImageView"><Expressions/><location startLine="16" startOffset="8" endLine="27" endOffset="42"/></Target><Target id="@+id/iv_download_icon" view="ImageView"><Expressions/><location startLine="30" startOffset="8" endLine="39" endOffset="42"/></Target><Target id="@+id/tv_download_title" view="TextView"><Expressions/><location startLine="53" startOffset="12" endLine="63" endOffset="46"/></Target><Target id="@+id/tv_file_name" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="76" endOffset="46"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="79" startOffset="12" endLine="89" endOffset="46"/></Target><Target id="@+id/progress_container" view="LinearLayout"><Expressions/><location startLine="96" startOffset="4" endLine="141" endOffset="18"/></Target><Target id="@+id/tv_progress" view="TextView"><Expressions/><location startLine="107" startOffset="8" endLine="118" endOffset="42"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="121" startOffset="8" endLine="130" endOffset="37"/></Target><Target id="@+id/tv_speed" view="TextView"><Expressions/><location startLine="182" startOffset="12" endLine="193" endOffset="46"/></Target><Target id="@+id/tv_file_size" view="TextView"><Expressions/><location startLine="229" startOffset="12" endLine="240" endOffset="46"/></Target><Target id="@+id/tv_eta" view="TextView"><Expressions/><location startLine="275" startOffset="12" endLine="286" endOffset="46"/></Target><Target id="@+id/btn_cancel" view="Button"><Expressions/><location startLine="293" startOffset="4" endLine="304" endOffset="38"/></Target></Targets></Layout>