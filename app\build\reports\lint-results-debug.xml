<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.1">

    <issue
        id="LintBaseline"
        severity="Hint"
        message="1 error and 79 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml"
        category="Lint"
        priority="10"
        summary="Baseline Applied"
        explanation="Lint can be configured with a &quot;baseline&quot;; a set of current issues found in a codebase, which future runs of lint will silently ignore. Only new issues not found in the baseline are reported.&#xA;&#xA;Note that while opening files in the IDE, baseline issues are not filtered out; the purpose of baselines is to allow you to get started using lint and break the build on all newly introduced errors, without having to go back and fix the entire codebase up front. However, when you open up existing files you still want to be aware of and fix issues as you come across them.&#xA;&#xA;This issue type is used to emit an informational-only warning if any issues were filtered out due to baseline matching. That way, you don&apos;t have a false sense of security if you forgot that you&apos;ve checked in a baseline file.">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\lint-baseline.xml"/>
    </issue>

    <issue
        id="LintBaselineFixed"
        severity="Hint"
        message="32 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with `android.lintOptions.checkDependencies=true`. Unmatched issue types: ContentDescription (5), DefaultLocale (2), DiscouragedApi, LockedOrientationActivity, Overdraw (5), SelectedPhotoAccess, StaticFieldLeak (2), UnusedResources (12), UseCompoundDrawables (3)"
        category="Lint"
        priority="10"
        summary="Baselined Issues Fixed"
        explanation="If a lint baseline describes a problem which is no longer reported, then the problem has either been fixed, or perhaps the issue type has been disabled. In any case, the entry can be removed from the baseline (such that if the issue is reintroduced at some point, lint will complain rather than just silently starting to match the old baseline entry again.)">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\lint-baseline.xml"/>
    </issue>

    <issue
        id="WrongViewCast"
        severity="Error"
        message="Unexpected implicit cast to `ShapeableImageView`: layout tag was `ImageView`"
        category="Correctness"
        priority="9"
        summary="Mismatched view type"
        explanation="Keeps track of the view types associated with ids and if it finds a usage of the id in the Java code it ensures that it is treated as the same type."
        errorLine1="        glyphView = findViewById(R.id.glyphView);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\auth\TapToUnlockActivity.java"
            line="34"
            column="9"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowLightNavigationBar` requires API level 27 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowLightNavigationBar&quot;>false&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="32"
            column="15"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowSplashScreenBackground` requires API level 31 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowSplashScreenBackground&quot;>@color/background&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="38"
            column="15"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowSplashScreenAnimatedIcon` requires API level 31 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowSplashScreenAnimatedIcon&quot;>@drawable/ic_launcher_foreground&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="39"
            column="15"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowSplashScreenIconBackgroundColor` requires API level 31 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowSplashScreenIconBackgroundColor&quot;>@color/primary&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="40"
            column="15"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowSplashScreenBrandingImage` requires API level 31 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowSplashScreenBrandingImage&quot;>@drawable/splash_branding&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="41"
            column="15"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowLightNavigationBar` requires API level 27 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowLightNavigationBar&quot;>false&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="57"
            column="15"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="`android:windowLightNavigationBar` requires API level 27 (current min is 24)"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="        &lt;item name=&quot;android:windowLightNavigationBar&quot;>false&lt;/item>"
        errorLine2="              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="255"
            column="15"/>
    </issue>

    <issue
        id="InflateParams"
        severity="Warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        category="Correctness"
        priority="5"
        summary="Layout Inflation without a Parent"
        explanation="When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored."
        url="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        urls="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/"
        errorLine1="        View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_download_progress, null);"
        errorLine2="                                                                                                 ~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="74"
            column="98"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-alpha07"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation(&quot;androidx.security:security-crypto:1.1.0-alpha06&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\build.gradle.kts"
            line="110"
            column="20"/>
    </issue>

    <issue
        id="InsecureBaseConfiguration"
        severity="Warning"
        message="Insecure Base Configuration"
        category="Security"
        priority="5"
        summary="Insecure Base Configuration"
        explanation="Permitting cleartext traffic could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider only allowing encrypted traffic by setting the `cleartextTrafficPermitted` tag to `false`."
        url="https://goo.gle/InsecureBaseConfiguration"
        urls="https://goo.gle/InsecureBaseConfiguration,https://developer.android.com/preview/features/security-config.html"
        errorLine1="    &lt;base-config cleartextTrafficPermitted=&quot;true&quot;>"
        errorLine2="                                            ~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\xml\network_security_config.xml"
            line="11"
            column="45"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        severity="Warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        category="Performance"
        priority="8"
        summary="Invalidating All RecyclerView Data"
        explanation="The `RecyclerView` adapter&apos;s `onNotifyDataSetChanged` method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views."
        errorLine1="        notifyDataSetChanged();"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; `SDK_INT` is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\utils\ApkInstaller.java"
            line="56"
            column="17"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        severity="Warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        category="Performance"
        priority="6"
        summary="Node can be replaced by a `TextView` with compound drawables"
        explanation="A `LinearLayout` which contains an `ImageView` and a `TextView` can be more efficiently handled as a compound drawable (a single TextView, using the `drawableTop`, `drawableLeft`, `drawableRight` and/or `drawableBottom` attributes to draw one or more images adjacent to the text).&#xA;&#xA;If the two widgets are offset from each other with margins, this can be replaced with a `drawablePadding` attribute.&#xA;&#xA;There&apos;s a lint quickfix to perform this conversion in the Eclipse plugin."
        errorLine1="                &lt;LinearLayout"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="232"
            column="18"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        severity="Warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        category="Performance"
        priority="6"
        summary="Node can be replaced by a `TextView` with compound drawables"
        explanation="A `LinearLayout` which contains an `ImageView` and a `TextView` can be more efficiently handled as a compound drawable (a single TextView, using the `drawableTop`, `drawableLeft`, `drawableRight` and/or `drawableBottom` attributes to draw one or more images adjacent to the text).&#xA;&#xA;If the two widgets are offset from each other with margins, this can be replaced with a `drawablePadding` attribute.&#xA;&#xA;There&apos;s a lint quickfix to perform this conversion in the Eclipse plugin."
        errorLine1="                &lt;LinearLayout"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="256"
            column="18"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        severity="Warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        category="Performance"
        priority="6"
        summary="Node can be replaced by a `TextView` with compound drawables"
        explanation="A `LinearLayout` which contains an `ImageView` and a `TextView` can be more efficiently handled as a compound drawable (a single TextView, using the `drawableTop`, `drawableLeft`, `drawableRight` and/or `drawableBottom` attributes to draw one or more images adjacent to the text).&#xA;&#xA;If the two widgets are offset from each other with margins, this can be replaced with a `drawablePadding` attribute.&#xA;&#xA;There&apos;s a lint quickfix to perform this conversion in the Eclipse plugin."
        errorLine1="                &lt;LinearLayout"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="291"
            column="18"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="        android:pathData=&quot;M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z&quot;/>"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_settings.xml"
            line="9"
            column="27"/>
    </issue>

    <issue
        id="VectorPath"
        severity="Warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        category="Performance"
        priority="5"
        summary="Long vector paths"
        explanation="Using long vector paths is bad for performance. There are several ways to make the `pathData` shorter:&#xA;* Using less precision&#xA;* Removing some minor details&#xA;* Using the Android Studio vector conversion tool&#xA;* Rasterizing the image (converting to PNG)"
        errorLine1="        android:pathData=&quot;M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z&quot;/>"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_settings.xml"
            line="10"
            column="27"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `#0D1117` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;#0D1117&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_fresh_download.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_patch_execution.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_tap_unlock.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.activity_download` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.activity_main` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;androidx.drawerlayout.widget.DrawerLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_main.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.activity_patch_execution` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;androidx.constraintlayout.widget.ConstraintLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_patch_execution.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.bg_status` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\bg_status.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.menu.bottom_nav_menu` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\menu\bottom_nav_menu.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_700` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="6"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.primary_container` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;primary_container&quot;>#E3F2FD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="22"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.secondary_light` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;secondary_light&quot;>#B2EBF2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.secondary_container` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;secondary_container&quot;>#E0F7FA&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.on_secondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;on_secondary&quot;>#000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.accent_dark` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;accent_dark&quot;>#C51162&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="34"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.outline` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;outline&quot;>#CCCCCC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="48"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.on_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;on_background&quot;>#FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="54"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.card_bg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;card_bg&quot;>#1F1F1F&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="58"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.transparent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;transparent&quot;>#00000000&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="66"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.shimmer_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;shimmer_color&quot;>#DDDDDD&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="75"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.progress_background` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;progress_background&quot;>#424242&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\colors.xml"
            line="76"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.margin_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;margin_large&quot;>24dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="10"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.padding_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;padding_large&quot;>24dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.padding_small` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;padding_small&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="15"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.corner_radius_medium` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;corner_radius_medium&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.corner_radius_small` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;corner_radius_small&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.elevation_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;elevation_large&quot;>8dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.elevation_medium` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;elevation_medium&quot;>4dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.icon_size_large` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;icon_size_large&quot;>48dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="29"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.icon_size_medium` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;icon_size_medium&quot;>24dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.dimen.icon_size_small` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;dimen name=&quot;icon_size_small&quot;>16dp&lt;/dimen>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\dimens.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.download_card_bg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\download_card_bg.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.raw.download_complete` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\raw\download_complete.json"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.menu.drawer_menu` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\menu\drawer_menu.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_dashboard` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_dashboard.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_empty` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_empty.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_error.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_key` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_key.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_logout` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_logout.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_nav_cloud` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_cloud.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_nav_home` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_home.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_nav_profile` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_profile.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_nav_settings` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_nav_settings.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_paste` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_paste.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_pubg_gl` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_gl.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_pubg_kr` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_kr.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_pubg_tw` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_tw.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_pubg_tw_vector` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_tw_vector.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_pubg_vn` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\ic_pubg_vn.png"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.item_patch_release` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;com.google.android.material.card.MaterialCardView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_patch_release.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.item_patch_shimmer` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;com.google.android.material.card.MaterialCardView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_patch_shimmer.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.item_release` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;com.google.android.material.card.MaterialCardView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_release.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.logo_splash` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;layer-list xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\logo_splash.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.layout.nav_header` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\nav_header.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.pubg_status_brutal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\pubg_status_brutal.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.pubg_tactical_card_overlay` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\pubg_tactical_card_overlay.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.rounded_image_bg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot; android:shape=&quot;rectangle&quot;>"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\drawable\rounded_image_bg.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.anim.slide_in_left` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;set xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\anim\slide_in_left.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.anim.slide_out_right` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;set xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\anim\slide_out_right.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.app_logo` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;app_logo&quot;>App Logo&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="8"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.login_subtitle` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;login_subtitle&quot;>Enter your license key to continue&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.license_key` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;license_key&quot;>License Key&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.enter_license_key` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;enter_license_key&quot;>Enter your license key&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.invalid_license_key_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;invalid_license_key_format&quot;>Invalid license key format&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.remember_me` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;remember_me&quot;>Remember me&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.login` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;login&quot;>Login&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.login_success` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;login_success&quot;>Login successful&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.login_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;login_error&quot;>Login failed: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.license_valid_until` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;license_valid_until&quot;>License valid until: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.registration_date` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;registration_date&quot;>Registration date: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.days_remaining` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;days_remaining&quot;>%1$d days remaining&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.keyauth_init_warning` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;keyauth_init_warning&quot;>KeyAuth initialization warning. You may experience login issues.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.dashboard_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;dashboard_title&quot;>Dashboard&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.target_app` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;target_app&quot;>Target App&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.scan_offsets` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;scan_offsets&quot;>Scan Offsets&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.apply_patch` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;apply_patch&quot;>Apply Patch&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.update_available` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;update_available&quot;>Update Available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.not_installed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;not_installed&quot;>Not Installed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.no_patches_available` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;no_patches_available&quot;>No patches available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.game_version` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;game_version&quot;>Game Version: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.updated` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;updated&quot;>Updated: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.patch_execution` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;patch_execution&quot;>Patch Execution&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.execution_logs` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;execution_logs&quot;>Execution Logs&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.execution_mode` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;execution_mode&quot;>Execution Mode&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.root_mode` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;root_mode&quot;>Root&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="43"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.non_root_mode` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;non_root_mode&quot;>Non-Root&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.start_patching` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;start_patching&quot;>Start Patching&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.stop_patching` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;stop_patching&quot;>Stop Patching&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.patching_in_progress` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;patching_in_progress&quot;>Patching in progress…&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.patching_complete` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;patching_complete&quot;>Patching completed successfully&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.patching_failed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;patching_failed&quot;>Patching failed: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_title&quot;>Download&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_patches` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_patches&quot;>Download Patches&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.available_releases` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;available_releases&quot;>Available Releases&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.no_releases_available` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;no_releases_available&quot;>No releases available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.select_release_first` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;select_release_first&quot;>Please select a release first&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.apk_size` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;apk_size&quot;>APK Size: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.obb_size` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;obb_size&quot;>OBB Size: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.total_size` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;total_size&quot;>Total Size: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_progress` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_progress&quot;>Download Progress&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.downloading_detailed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;downloading_detailed&quot;>Downloading… %1$s / %2$s • %3$s • ETA: %4$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_complete_path` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_complete_path&quot;>Download Complete: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="65"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_failed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_failed&quot;>Download Failed: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="66"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_cancelled` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_cancelled&quot;>Download Cancelled&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="67"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download_in_progress` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download_in_progress&quot;>Download in progress. Do you want to cancel?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="68"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.continue_download` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;continue_download&quot;>Continue Download&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="69"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.cancel_download` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;cancel_download&quot;>Cancel Download&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="70"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.available` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;available&quot;>Available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.success` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;success&quot;>Success&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.warning` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;warning&quot;>Warning&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="109"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.navigation_drawer_open` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;navigation_drawer_open&quot;>Open navigation drawer&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="113"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.navigation_drawer_close` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;navigation_drawer_close&quot;>Close navigation drawer&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="114"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.patches_updated` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;patches_updated&quot;>Patches updated&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="115"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.sync_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;sync_error&quot;>Sync error: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="116"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.scanning_offsets` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;scanning_offsets&quot;>Scanning Offsets&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="117"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.scanning_offsets_for` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;scanning_offsets_for&quot;>Scanning offsets for %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="118"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.download` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;download&quot;>Download&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="125"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.pubg_global` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;pubg_global&quot;>PUBG MOBILE GL&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="128"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.pubg_kr` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;pubg_kr&quot;>PUBG MOBILE KR&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="129"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.pubg_tw` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;pubg_tw&quot;>PUBG MOBILE TW&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="130"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.pubg_vn` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;pubg_vn&quot;>PUBG MOBILE VN&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="131"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.version_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;version_format&quot;>Version %s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="132"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.global_version_desc` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;global_version_desc&quot;>Global version patch&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="133"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.kr_version_desc` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;kr_version_desc&quot;>Korean version patch&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="134"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.tw_version_desc` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;tw_version_desc&quot;>Taiwan version patch&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="135"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.vn_version_desc` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;vn_version_desc&quot;>Vietnam version patch&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="136"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.game_icon` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;game_icon&quot;>Game icon&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="137"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.game_version_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;game_version_format&quot;>Version %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="138"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.release_date_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;release_date_format&quot;>Released: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="139"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.file_size_format` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;file_size_format&quot;>%1$s MB&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="140"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.resume` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;resume&quot;>Resume&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="151"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.username` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;username&quot;>Username&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="154"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.password` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;password&quot;>Password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="155"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.forgot_password` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;forgot_password&quot;>Forgot Password?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="156"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.register` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;register&quot;>Register&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="157"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.login_failed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;login_failed&quot;>Login failed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="158"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.invalid_credentials` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;invalid_credentials&quot;>Invalid username or password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="159"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.network_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;network_error&quot;>Network error&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="160"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.unknown_error` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;unknown_error&quot;>Unknown error occurred&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="161"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.theme` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;theme&quot;>Theme&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="164"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.theme_light` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;theme_light&quot;>Light&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="165"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.theme_dark` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;theme_dark&quot;>Dark&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="166"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.theme_system` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;theme_system&quot;>System default&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="167"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.notifications` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;notifications&quot;>Notifications&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="168"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.auto_update` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;auto_update&quot;>Auto-update&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="169"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.version` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;version&quot;>Version&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="170"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.licenses` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;licenses&quot;>Licenses&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="171"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.privacy_policy` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;privacy_policy&quot;>Privacy Policy&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="172"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.terms_of_service` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;terms_of_service&quot;>Terms of Service&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="173"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.downloads` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;downloads&quot;>Downloads&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="176"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.no_downloads` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;no_downloads&quot;>No downloads yet&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="177"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.delete` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;delete&quot;>Delete&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="178"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.paused` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;paused&quot;>Paused&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="179"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.cancelled` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;cancelled&quot;>Cancelled&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="180"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.confirm` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;confirm&quot;>Confirm&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="185"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.delete_download` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;delete_download&quot;>Delete download?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="186"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.delete_all_downloads` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;delete_all_downloads&quot;>Delete all downloads?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="187"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.yes` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;yes&quot;>Yes&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="188"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.no` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;no&quot;>No&lt;/string>"
        errorLine2="            ~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="189"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.icon_search` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;icon_search&quot;>Search&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="192"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.icon_filter` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;icon_filter&quot;>Filter&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="193"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.icon_sort` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;string name=&quot;icon_sort&quot;>Sort&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\strings.xml"
            line="194"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.raw.success_animation` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\raw\success_animation.json"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Theme_BearLoader_Splash` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Theme.BearLoader.Splash&quot; parent=&quot;Theme.BearLoader.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="61"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Animation_BearLoader_Activity` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Animation.BearLoader.Activity&quot; parent=&quot;@android:style/Animation.Activity&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="84"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Widget_BearLoader_Button_Secondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Widget.BearLoader.Button.Secondary&quot; parent=&quot;Widget.Material3.Button.OutlinedButton&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="127"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Widget_BearLoader_Button_Small` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Widget.BearLoader.Button.Small&quot; parent=&quot;Widget.Material3.Button&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="182"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Widget_BearLoader_Button_Icon` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Widget.BearLoader.Button.Icon&quot; parent=&quot;Widget.Material3.Button.IconButton&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="206"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.Theme_BearMod` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;style name=&quot;Theme.BearMod&quot; parent=&quot;Theme.Material3.DayNight.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\values\themes.xml"
            line="241"
            column="12"/>
    </issue>

    <issue
        id="UseTomlInstead"
        severity="Warning"
        message="Use version catalog instead"
        category="Productivity"
        priority="4"
        summary="Use TOML Version Catalog Instead"
        explanation="If your project is using a `libs.versions.toml` file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically)."
        errorLine1="    implementation(&quot;androidx.security:security-crypto:1.1.0-alpha06&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\build.gradle.kts"
            line="110"
            column="20"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view ``ShapeableImageView`` has `setOnTouchListener` called on it but does not override `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        glyphView.setOnTouchListener((v, event) -> {"
        errorLine2="        ^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\auth\TapToUnlockActivity.java"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        glyphView.setOnTouchListener((v, event) -> {"
        errorLine2="                                     ^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\auth\TapToUnlockActivity.java"
            line="50"
            column="38"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="157"
            column="22"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="238"
            column="22"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="263"
            column="22"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            fallbackView.setText(&quot;Error loading item&quot;);"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="63"
            column="34"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                    holder.status.setText(&quot;Unknown&quot;);"
        errorLine2="                                          ~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="114"
            column="43"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        holder.tvProgressInline.setText(currentProgress + &quot;%&quot;);"
        errorLine2="                                                        ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="129"
            column="57"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        holder.tvSpeedInline.setText(DECIMAL_FORMAT.format(currentSpeedMBps) + &quot; MB/s&quot;);"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="132"
            column="54"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                        holder.tvSpeedInline.setText(DECIMAL_FORMAT.format(currentSpeedMBps) + &quot; MB/s&quot;);"
        errorLine2="                                                                                               ~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="132"
            column="96"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                            holder.tvEtaInline.setText(currentEtaMinutes + &quot;m &quot; + currentEtaSeconds + &quot;s&quot;);"
        errorLine2="                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="136"
            column="56"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                            holder.tvEtaInline.setText(currentEtaSeconds + &quot;s&quot;);"
        errorLine2="                                                       ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\main\AppVersionAdapter.java"
            line="138"
            column="56"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvTitle.setText(&quot;Downloading &quot; + downloadType);"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="124"
            column="25"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvTitle.setText(&quot;Downloading &quot; + downloadType);"
        errorLine2="                        ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="124"
            column="25"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvSpeed.setText(&quot;0 MB/s&quot;);"
        errorLine2="                        ~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="127"
            column="25"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvEta.setText(&quot;Calculating...&quot;);"
        errorLine2="                      ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="128"
            column="23"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvFileSize.setText(&quot;0 / 0 MB&quot;);"
        errorLine2="                           ~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="129"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvProgress.setText(progress + &quot;%&quot;);"
        errorLine2="                           ~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="149"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + &quot; MB/s&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="153"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + &quot; MB/s&quot;);"
        errorLine2="                                                               ~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="153"
            column="64"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvSpeed.setText(&quot;0 MB/s&quot;);"
        errorLine2="                            ~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="155"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                tvEta.setText(etaMinutes + &quot;m &quot; + etaSeconds + &quot;s&quot;);"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="161"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                tvEta.setText(etaSeconds + &quot;s&quot;);"
        errorLine2="                              ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="163"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="        tvFileSize.setText(DECIMAL_FORMAT.format(downloadedMB) + &quot; / &quot; + "
        errorLine2="                           ^">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="170"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                          DECIMAL_FORMAT.format(totalSizeMB) + &quot; MB&quot;);"
        errorLine2="                                                               ~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="171"
            column="64"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvTitle.setText(&quot;Download Complete&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="181"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvProgress.setText(&quot;100%&quot;);"
        errorLine2="                               ~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="182"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvSpeed.setText(&quot;Complete&quot;);"
        errorLine2="                            ~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="184"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvEta.setText(&quot;Done&quot;);"
        errorLine2="                          ~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="185"
            column="27"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            btnCancel.setText(&quot;Close&quot;);"
        errorLine2="                              ~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="186"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvTitle.setText(&quot;Download Failed&quot;);"
        errorLine2="                            ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="188"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvProgress.setText(&quot;Failed&quot;);"
        errorLine2="                               ~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="189"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            tvSpeed.setText(&quot;Error&quot;);"
        errorLine2="                            ~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="190"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            btnCancel.setText(&quot;Close&quot;);"
        errorLine2="                              ~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\java\com\bearmod\loader\ui\download\DownloadProgressDialog.java"
            line="192"
            column="31"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;0%&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\activity_download.xml"
            line="375"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;•&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;•&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml"
            line="74"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;0%&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml"
            line="129"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;0 MB/s&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;0 MB/s&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml"
            line="138"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;--&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;--&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="D:\Augment_Code\BearMod-Loader\app\src\main\res\layout\item_app_version.xml"
            line="147"
            column="21"/>
    </issue>

</issues>
