#ifndef SIGNATURE_VERIFICATION_H
#define SIGNATURE_VERIFICATION_H

#include <jni.h>
#include <string>
#include <vector>

// Signature verification functions for BearMod Loader
namespace BearLoader {
    namespace Security {
        
        // Known good signature hashes (SHA-256) - replace with your actual hashes
        extern const std::vector<std::string> TRUSTED_LOADER_HASHES;
        extern const std::vector<std::string> TRUSTED_PUBG_HASHES;
        
        /**
         * Calculate SHA-256 hash of byte array
         */
        std::string calculateSHA256(const unsigned char* data, size_t length);
        
        /**
         * Get package signature hash
         */
        std::string getPackageSignatureHash(JNIEnv* env, jobject context, jstring packageName);
        
        /**
         * Verify loader app signature
         */
        bool verifyLoaderSignature(JNIEnv* env, jobject context);
        
        /**
         * Verify PUBG Mobile app signature
         */
        bool verifyPubgSignature(JNIEnv* env, jobject context, jstring packageName);
        
        /**
         * Verify shared certificate between loader and PUBG
         */
        bool verifySharedCertificate(JNIEnv* env, jobject context, jstring pubgPackageName);
        
        /**
         * Anti-tampering check
         */
        bool performAntiTamperingCheck(JNIEnv* env, jobject context);
        
        /**
         * Initialize security system
         */
        void initializeSecurity();
        
        /**
         * Check if running in debugger/emulator
         */
        bool isDebuggingDetected();
        
        /**
         * Check for root access
         */
        bool isRootDetected();
        
        /**
         * Check for Xposed/Frida
         */
        bool isHookingDetected();
    }
}

// JNI function declarations for Java interface
extern "C" {
    JNIEXPORT jboolean JNICALL
    Java_com_bearmod_loader_security_NativeSecurityManager_nativeVerifyLoaderSignature(
            JNIEnv* env, jobject thiz, jobject context);
    
    JNIEXPORT jboolean JNICALL
    Java_com_bearmod_loader_security_NativeSecurityManager_nativeVerifyPubgSignature(
            JNIEnv* env, jobject thiz, jobject context, jstring packageName);
    
    JNIEXPORT jboolean JNICALL
    Java_com_bearmod_loader_security_NativeSecurityManager_nativeVerifySharedCertificate(
            JNIEnv* env, jobject thiz, jobject context, jstring pubgPackageName);
    
    JNIEXPORT jstring JNICALL
    Java_com_bearmod_loader_security_NativeSecurityManager_nativeGetSignatureHash(
            JNIEnv* env, jobject thiz, jobject context, jstring packageName);
    
    JNIEXPORT jboolean JNICALL
    Java_com_bearmod_loader_security_NativeSecurityManager_nativePerformAntiTamperingCheck(
            JNIEnv* env, jobject thiz, jobject context);
}

#endif // SIGNATURE_VERIFICATION_H
