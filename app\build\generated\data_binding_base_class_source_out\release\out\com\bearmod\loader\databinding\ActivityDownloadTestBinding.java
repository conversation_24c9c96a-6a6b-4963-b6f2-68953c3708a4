// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDownloadTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnBack;

  @NonNull
  public final Button btnCleanup;

  @NonNull
  public final Button btnDownloadGlobal;

  @NonNull
  public final Button btnDownloadKr;

  @NonNull
  public final Button btnDownloadTw;

  @NonNull
  public final Button btnDownloadVn;

  @NonNull
  public final Button btnTestStorage;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView tvProgress;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvStorageInfo;

  private ActivityDownloadTestBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnBack,
      @NonNull Button btnCleanup, @NonNull Button btnDownloadGlobal, @NonNull Button btnDownloadKr,
      @NonNull Button btnDownloadTw, @NonNull Button btnDownloadVn, @NonNull Button btnTestStorage,
      @NonNull ProgressBar progressBar, @NonNull TextView tvProgress, @NonNull TextView tvStatus,
      @NonNull TextView tvStorageInfo) {
    this.rootView = rootView;
    this.btnBack = btnBack;
    this.btnCleanup = btnCleanup;
    this.btnDownloadGlobal = btnDownloadGlobal;
    this.btnDownloadKr = btnDownloadKr;
    this.btnDownloadTw = btnDownloadTw;
    this.btnDownloadVn = btnDownloadVn;
    this.btnTestStorage = btnTestStorage;
    this.progressBar = progressBar;
    this.tvProgress = tvProgress;
    this.tvStatus = tvStatus;
    this.tvStorageInfo = tvStorageInfo;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDownloadTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDownloadTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_download_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDownloadTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_back;
      ImageButton btnBack = ViewBindings.findChildViewById(rootView, id);
      if (btnBack == null) {
        break missingId;
      }

      id = R.id.btn_cleanup;
      Button btnCleanup = ViewBindings.findChildViewById(rootView, id);
      if (btnCleanup == null) {
        break missingId;
      }

      id = R.id.btn_download_global;
      Button btnDownloadGlobal = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadGlobal == null) {
        break missingId;
      }

      id = R.id.btn_download_kr;
      Button btnDownloadKr = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadKr == null) {
        break missingId;
      }

      id = R.id.btn_download_tw;
      Button btnDownloadTw = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadTw == null) {
        break missingId;
      }

      id = R.id.btn_download_vn;
      Button btnDownloadVn = ViewBindings.findChildViewById(rootView, id);
      if (btnDownloadVn == null) {
        break missingId;
      }

      id = R.id.btn_test_storage;
      Button btnTestStorage = ViewBindings.findChildViewById(rootView, id);
      if (btnTestStorage == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      id = R.id.tv_status;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tv_storage_info;
      TextView tvStorageInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvStorageInfo == null) {
        break missingId;
      }

      return new ActivityDownloadTestBinding((LinearLayout) rootView, btnBack, btnCleanup,
          btnDownloadGlobal, btnDownloadKr, btnDownloadTw, btnDownloadVn, btnTestStorage,
          progressBar, tvProgress, tvStatus, tvStorageInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
