<resources>
    <!-- Base application theme. -->
    <style name="Theme.BearLoader" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_light</item>
        <item name="colorOnPrimaryContainer">@color/background</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/accent_light</item>
        <item name="colorOnSecondaryContainer">@color/background</item>

        <!-- Surface colors -->
        <item name="colorSurface">@color/card_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/background_light</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary</item>

        <!-- Background -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/background</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background</item>
    </style>
</resources>