<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/bg_dialog_rounded">

    <!-- Header with Game Icon -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <ImageView
            android:id="@+id/iv_game_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_pubg_mobile_global_logo"
            android:contentDescription="Game Icon"
            android:layout_marginEnd="16dp"
            tools:ignore="HardcodedText" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Download Title -->
            <TextView
                android:id="@+id/tv_download_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Downloading APK + OBB"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

            <!-- File Name -->
            <TextView
                android:id="@+id/tv_file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="pubg-mobile-gl.apk"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:layout_marginTop="4dp"
                tools:ignore="HardcodedText" />

        </LinearLayout>

    </LinearLayout>

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bg_progress_container"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <!-- Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="12dp"
            android:layout_marginBottom="12dp"
            android:progressTint="@color/accent"
            android:progressBackgroundTint="@color/background_light"
            android:max="100"
            android:progress="0" />

        <!-- Progress Percentage (Large) -->
        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp"
            tools:ignore="HardcodedText" />

    </LinearLayout>

    <!-- Download Stats Grid -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        tools:ignore="DisableBaselineAlignment">

        <!-- Speed Card -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/bg_stat_card"
            android:padding="12dp"
            android:layout_marginEnd="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Speed"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:gravity="center"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_speed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 MB/s"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginTop="4dp"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <!-- File Size Card -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/bg_stat_card"
            android:padding="12dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Downloaded"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:gravity="center"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_file_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 / 0 MB"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginTop="4dp"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <!-- ETA Card -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/bg_stat_card"
            android:padding="12dp"
            android:layout_marginStart="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Time Left"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:gravity="center"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_eta"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="--"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginTop="4dp"
                tools:ignore="HardcodedText" />

        </LinearLayout>

    </LinearLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:textColor="@color/text_primary"
            android:layout_marginEnd="8dp"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/btn_pause"
            style="@style/Widget.Material3.Button.TonalButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Pause"
            android:layout_marginStart="8dp"
            android:visibility="gone"
            tools:ignore="HardcodedText" />

    </LinearLayout>

</LinearLayout>
