D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c54ebc8070a4ffb7c6c997b81f2f8ec4\transformed\out\jni\arm64-v8a\libnativelib.so: Warning: The native library arm64-v8a/libnativelib.so (from Bear-Loader:nativelib:unspecified) is not 16 KB aligned [Aligned16KB]
D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c54ebc8070a4ffb7c6c997b81f2f8ec4\transformed\out\jni\arm64-v8a\libnativelib.so: Warning: The native library arm64-v8a/libnativelib.so (from Bear-Loader:nativelib:unspecified) is not 16 KB aligned [Aligned16KB]

   Explanation for issues of type "Aligned16KB":
   Android has traditionally used 4 KB memory page sizes. However, to support
   future devices that only work with 16 KB aligned libraries apps containing
   native libraries need to be built with 16 KB alignment.

   Apps with 4 KB aligned native libraries may not work correctly on devices
   requiring 16 KB alignment. To ensure compatibility and future-proof your
   app, it is strongly recommended that your native libraries are aligned to
   16 KB boundaries.

   If your app uses any NDK libraries, directly or indirectly through an SDK,
   you should rebuild your app to meet this recommendation. Make sure all
   native libraries within your application, including those from
   dependencies, are built with 16 KB page alignment.

   This lint check looks at all native libraries that your app depends on. If
   any are found to be aligned to 4 KB instead of 16 KB, you will need to
   address this.

   When a library is flagged, first try to update to a newer version that
   supports 16 KB alignment. If an updated version is not available, contact
   the library vendor to ask about their plans for 16 KB support and request a
   compatible version. Updating your libraries proactively will help ensure
   your app works properly on a wider range of devices.

   https://developer.android.com/guide/practices/page-sizes

0 errors, 2 warnings
