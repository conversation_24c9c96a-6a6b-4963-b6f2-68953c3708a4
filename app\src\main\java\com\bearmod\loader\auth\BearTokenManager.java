package com.bearmod.loader.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import android.util.Base64;

import com.bearmod.loader.security.NativeSecurityManager;

import java.security.SecureRandom;
import java.security.MessageDigest;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;

/**
 * BearToken Manager - Handles shared authentication tokens between loader and target mods
 * This allows target mods to skip KeyAuth if they have a valid BearToken from the loader
 */
public class BearTokenManager {
    
    private static final String TAG = "BearTokenManager";
    private static final String PREFS_NAME = "bear_token_prefs";
    private static final String TOKEN_KEY = "bear_token";
    private static final String TOKEN_EXPIRY_KEY = "bear_token_expiry";
    private static final String TOKEN_SIGNATURE_KEY = "bear_token_signature";
    
    // Token validity period (24 hours)
    private static final long TOKEN_VALIDITY_PERIOD = 24 * 60 * 60 * 1000L;
    
    // AES encryption key for token protection
    private static final String AES_KEY = "BearModLoader2024"; // 16 bytes for AES-128
    
    private static BearTokenManager instance;
    private Context context;
    
    private BearTokenManager(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public static synchronized BearTokenManager getInstance(Context context) {
        if (instance == null) {
            instance = new BearTokenManager(context);
        }
        return instance;
    }
    
    /**
     * Generate a new BearToken after successful KeyAuth authentication
     * @param keyAuthSessionId KeyAuth session ID
     * @param userLicenseKey User's license key
     * @return Generated BearToken
     */
    public BearToken generateBearToken(String keyAuthSessionId, String userLicenseKey) {
        try {
            Log.d(TAG, "Generating new BearToken");
            
            // Verify loader signature first
            if (!NativeSecurityManager.verifyLoaderSignature(context)) {
                Log.e(TAG, "Loader signature verification failed");
                return null;
            }
            
            // Generate token components
            long currentTime = System.currentTimeMillis();
            long expiryTime = currentTime + TOKEN_VALIDITY_PERIOD;
            String deviceId = getDeviceId();
            String loaderSignature = NativeSecurityManager.getSignatureHash(context, context.getPackageName());
            
            // Create token payload
            String tokenPayload = String.format("%s|%s|%s|%d|%d|%s",
                keyAuthSessionId,
                hashString(userLicenseKey), // Hash license key for privacy
                deviceId,
                currentTime,
                expiryTime,
                loaderSignature
            );
            
            // Encrypt token payload
            String encryptedToken = encryptString(tokenPayload);
            
            // Generate signature for integrity
            String tokenSignature = generateTokenSignature(encryptedToken, loaderSignature);
            
            // Create BearToken object
            BearToken bearToken = new BearToken(
                encryptedToken,
                tokenSignature,
                currentTime,
                expiryTime,
                deviceId
            );
            
            // Store token securely
            storeBearToken(bearToken);
            
            // Store token in shared location for target mods
            storeSharedBearToken(bearToken);
            
            Log.d(TAG, "BearToken generated successfully");
            return bearToken;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to generate BearToken", e);
            return null;
        }
    }
    
    /**
     * Validate existing BearToken
     * @return Valid BearToken or null if invalid/expired
     */
    public BearToken validateBearToken() {
        try {
            BearToken token = loadBearToken();
            if (token == null) {
                Log.d(TAG, "No BearToken found");
                return null;
            }
            
            // Check expiry
            if (System.currentTimeMillis() > token.expiryTime) {
                Log.d(TAG, "BearToken expired");
                clearBearToken();
                return null;
            }
            
            // Verify signature
            String loaderSignature = NativeSecurityManager.getSignatureHash(context, context.getPackageName());
            String expectedSignature = generateTokenSignature(token.encryptedPayload, loaderSignature);
            
            if (!expectedSignature.equals(token.signature)) {
                Log.e(TAG, "BearToken signature verification failed");
                clearBearToken();
                return null;
            }
            
            // Verify device ID
            if (!getDeviceId().equals(token.deviceId)) {
                Log.e(TAG, "BearToken device ID mismatch");
                clearBearToken();
                return null;
            }
            
            Log.d(TAG, "BearToken validation successful");
            return token;
            
        } catch (Exception e) {
            Log.e(TAG, "BearToken validation failed", e);
            clearBearToken();
            return null;
        }
    }
    
    /**
     * Store BearToken in shared location for target mods
     * @param token BearToken to store
     */
    private void storeSharedBearToken(BearToken token) {
        try {
            // Store in shared preferences accessible by target mods
            SharedPreferences sharedPrefs = context.getSharedPreferences("bearmod_shared", Context.MODE_WORLD_READABLE);
            SharedPreferences.Editor editor = sharedPrefs.edit();
            
            editor.putString("bear_token", token.encryptedPayload);
            editor.putString("bear_signature", token.signature);
            editor.putLong("bear_expiry", token.expiryTime);
            editor.putString("bear_device", token.deviceId);
            editor.putLong("bear_created", token.createdTime);
            
            editor.apply();
            
            Log.d(TAG, "BearToken stored in shared location");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to store shared BearToken", e);
        }
    }
    
    /**
     * Store BearToken locally
     * @param token BearToken to store
     */
    private void storeBearToken(BearToken token) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putString(TOKEN_KEY, token.encryptedPayload);
        editor.putString(TOKEN_SIGNATURE_KEY, token.signature);
        editor.putLong(TOKEN_EXPIRY_KEY, token.expiryTime);
        
        editor.apply();
    }
    
    /**
     * Load BearToken from storage
     * @return BearToken or null if not found
     */
    private BearToken loadBearToken() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        String encryptedPayload = prefs.getString(TOKEN_KEY, null);
        String signature = prefs.getString(TOKEN_SIGNATURE_KEY, null);
        long expiryTime = prefs.getLong(TOKEN_EXPIRY_KEY, 0);
        
        if (encryptedPayload == null || signature == null || expiryTime == 0) {
            return null;
        }
        
        return new BearToken(
            encryptedPayload,
            signature,
            0, // Created time not stored locally
            expiryTime,
            getDeviceId()
        );
    }
    
    /**
     * Clear BearToken from all storage locations
     */
    public void clearBearToken() {
        // Clear local storage
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().clear().apply();
        
        // Clear shared storage
        try {
            SharedPreferences sharedPrefs = context.getSharedPreferences("bearmod_shared", Context.MODE_WORLD_READABLE);
            sharedPrefs.edit().clear().apply();
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear shared BearToken", e);
        }
        
        Log.d(TAG, "BearToken cleared");
    }
    
    /**
     * Generate token signature for integrity verification
     */
    private String generateTokenSignature(String encryptedPayload, String loaderSignature) throws Exception {
        String signatureInput = encryptedPayload + "|" + loaderSignature + "|" + AES_KEY;
        return hashString(signatureInput);
    }
    
    /**
     * Encrypt string using AES
     */
    private String encryptString(String plaintext) throws Exception {
        SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(), "AES");
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        
        // Generate random IV
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plaintext.getBytes());
        
        // Combine IV + encrypted data
        byte[] combined = new byte[iv.length + encrypted.length];
        System.arraycopy(iv, 0, combined, 0, iv.length);
        System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);
        
        return Base64.encodeToString(combined, Base64.DEFAULT);
    }
    
    /**
     * Hash string using SHA-256
     */
    private String hashString(String input) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(input.getBytes());
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
    
    /**
     * Get device ID for token binding
     */
    private String getDeviceId() {
        // Use a combination of device identifiers
        String deviceInfo = android.os.Build.FINGERPRINT + 
                           android.os.Build.SERIAL + 
                           android.os.Build.ID;
        try {
            return hashString(deviceInfo).substring(0, 16);
        } catch (Exception e) {
            return "unknown_device";
        }
    }
    
    /**
     * BearToken data class
     */
    public static class BearToken {
        public final String encryptedPayload;
        public final String signature;
        public final long createdTime;
        public final long expiryTime;
        public final String deviceId;
        
        public BearToken(String encryptedPayload, String signature, long createdTime, long expiryTime, String deviceId) {
            this.encryptedPayload = encryptedPayload;
            this.signature = signature;
            this.createdTime = createdTime;
            this.expiryTime = expiryTime;
            this.deviceId = deviceId;
        }
        
        public boolean isValid() {
            return System.currentTimeMillis() < expiryTime;
        }
        
        public long getRemainingTime() {
            return Math.max(0, expiryTime - System.currentTimeMillis());
        }
    }
}
