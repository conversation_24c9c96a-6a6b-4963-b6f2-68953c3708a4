<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.databinding:viewbinding:8.10.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3ed499b067677bc8aba1f7638863683\transformed\viewbinding-8.10.0\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.10.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3ed499b067677bc8aba1f7638863683\transformed\viewbinding-8.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5726a729d57e3701914f576476bac454\transformed\navigation-runtime-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5726a729d57e3701914f576476bac454\transformed\navigation-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5606969ee47eab2a73779a52b526778\transformed\navigation-common-release\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5606969ee47eab2a73779a52b526778\transformed\navigation-common-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-fragment:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0817d30fadf18d76b47bcdf841f67615\transformed\navigation-fragment-2.9.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-fragment:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0817d30fadf18d76b47bcdf841f67615\transformed\navigation-fragment-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-ui:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4a1e893a995f6a58f7d5e0e2b76f6f2\transformed\navigation-ui-2.9.0\jars\classes.jar"
      resolved="androidx.navigation:navigation-ui:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4a1e893a995f6a58f7d5e0e2b76f6f2\transformed\navigation-ui-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.6.6@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.6.6"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.2.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.10.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c66bbe2ba25b5a719c47c2efc8dba1b4\transformed\work-runtime-ktx-2.10.1\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.10.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c66bbe2ba25b5a719c47c2efc8dba1b4\transformed\work-runtime-ktx-2.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.10.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.10.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ef7b832545248bde80afca51104877d\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ef7b832545248bde80afca51104877d\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0957f9b8573f6b47549742aecd39688e\transformed\glide-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0957f9b8573f6b47549742aecd39688e\transformed\glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\99c6c94c3d8e08f53ea550fcad6e7157\transformed\viewpager2-1.1.0-beta02\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.1.0-beta02"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\99c6c94c3d8e08f53ea550fcad6e7157\transformed\viewpager2-1.1.0-beta02"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.8.7@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\93651dcfea9155ae73e717938583485b\transformed\fragment-ktx-1.8.7\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.8.7"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\93651dcfea9155ae73e717938583485b\transformed\fragment-ktx-1.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.8.7@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bab17e508190d53482501d234406c6b5\transformed\fragment-1.8.7\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.8.7"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bab17e508190d53482501d234406c6b5\transformed\fragment-1.8.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6356cbc0e65a962ba247e25511f5d906\transformed\activity-ktx-1.8.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6356cbc0e65a962ba247e25511f5d906\transformed\activity-ktx-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\45f22f727c679be5214bf583be8ed144\transformed\activity-1.8.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\45f22f727c679be5214bf583be8ed144\transformed\activity-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\34b734569ec2fe2b468b73ce2bb9b529\transformed\swiperefreshlayout-1.1.0\jars\classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\34b734569ec2fe2b468b73ce2bb9b529\transformed\swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.4.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f313c980208ef502340a69b9d5b3a65c\transformed\recyclerview-1.4.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.4.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f313c980208ef502340a69b9d5b3a65c\transformed\recyclerview-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\922d33f919fe961fe77679f6d2f35f9d\transformed\slidingpanelayout-1.2.0\jars\classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\922d33f919fe961fe77679f6d2f35f9d\transformed\slidingpanelayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41ecbfe5a5e3508966db1fbd732a1548\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41ecbfe5a5e3508966db1fbd732a1548\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4513d30e06476a0f1149dafe7a246eac\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4513d30e06476a0f1149dafe7a246eac\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\339f0ea61d8a6306bd55f549bb75c4d5\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\339f0ea61d8a6306bd55f549bb75c4d5\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96ee932b5376bae21d77b4fc6db89631\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96ee932b5376bae21d77b4fc6db89631\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cdb4f4d97de4f3bb947c4bde4dbf1220\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cdb4f4d97de4f3bb947c4bde4dbf1220\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4e672e8c6d97ea00c84bd8e756ea36e\transformed\core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4e672e8c6d97ea00c84bd8e756ea36e\transformed\core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5798c03ce18352d930661264524dff86\transformed\exoplayer-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5798c03ce18352d930661264524dff86\transformed\exoplayer-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-ui:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b7fdc3266dd2bd73d6604182feb99497\transformed\exoplayer-ui-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-ui:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b7fdc3266dd2bd73d6604182feb99497\transformed\exoplayer-ui-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.6.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\22791bd86d76463413ecfdee575332ad\transformed\media-1.6.0\jars\classes.jar"
      resolved="androidx.media:media:1.6.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\22791bd86d76463413ecfdee575332ad\transformed\media-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4808c427e838e98fe6acbf0a4278ade\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4808c427e838e98fe6acbf0a4278ade\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6441eb97bf9e700a142cb39f30110b83\transformed\lifecycle-livedata-core-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6441eb97bf9e700a142cb39f30110b83\transformed\lifecycle-livedata-core-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\806fe9648c6cce1cb4bf5b8eec4e4e97\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\806fe9648c6cce1cb4bf5b8eec4e4e97\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\66a9ccbd348e1d6b7897ad4ba64e258a\transformed\lifecycle-livedata-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\66a9ccbd348e1d6b7897ad4ba64e258a\transformed\lifecycle-livedata-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0887e407a2dd67a120df4aa0d7ccdffa\transformed\lifecycle-viewmodel-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0887e407a2dd67a120df4aa0d7ccdffa\transformed\lifecycle-viewmodel-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:3.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\3.0.0\f267e39336e822e2abb835818606986a96b4d5aa\converter-gson-3.0.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:3.0.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:3.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\3.0.0\c0cdf6d243c5187732134129fda05a74f9197874\retrofit-3.0.0.jar"
      resolved="com.squareup.retrofit2:retrofit:3.0.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.google.code.gson:gson:2.13.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.13.1\853ce06c11316b33a8eae5e9095da096a9528b8f\gson-2.13.1.jar"
      resolved="com.google.code.gson:gson:2.13.1"/>
  <library
      name="androidx.room:room-runtime-android:2.7.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\jars\classes.jar"
      resolved="androidx.room:room-runtime-android:2.7.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common-jvm:2.7.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.room\room-common-jvm\2.7.1\46a9845444d0b849131baeb85bfe4051828261ad\room-common-jvm-2.7.1.jar"
      resolved="androidx.room:room-common-jvm:2.7.1"/>
  <library
      name="androidx.room:room-ktx:2.7.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4a7ec5e033837f3f68379b265aea0192\transformed\room-ktx-2.7.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.7.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4a7ec5e033837f3f68379b265aea0192\transformed\room-ktx-2.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.shimmer:shimmer:0.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f6c54e1da2ec9c8ee275643a44333038\transformed\shimmer-0.5.0\jars\classes.jar"
      resolved="com.facebook.shimmer:shimmer:0.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f6c54e1da2ec9c8ee275643a44333038\transformed\shimmer-0.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.json:json:20250517@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.json\json\20250517\d67181bbd819ccceb929b580a4e2fcb0c8b17cd8\json-20250517.jar"
      resolved="org.json:json:20250517"/>
  <library
      name="androidx.security:security-crypto:1.1.0-alpha06@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c0a00c0c42a46d30408269b4088e9950\transformed\security-crypto-1.1.0-alpha06\jars\classes.jar"
      resolved="androidx.security:security-crypto:1.1.0-alpha06"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c0a00c0c42a46d30408269b4088e9950\transformed\security-crypto-1.1.0-alpha06"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.2.0\6a1dfd69226e148898410721f4b14315343d1429\concurrent-futures-1.2.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.2.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-core:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-core:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-common:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-common:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:33.4.8-android@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\33.4.8-android\cb7510a733595f02fe633ffff62d40cfc3056e9\guava-33.4.8-android.jar"
      resolved="com.google.guava:guava:33.4.8-android"/>
  <library
      name="org.java-websocket:Java-WebSocket:1.6.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.java-websocket\Java-WebSocket\1.6.0\52d7f6dc1b06607d4d12e438c840c25f1a0bb854\Java-WebSocket-1.6.0.jar"
      resolved="org.java-websocket:Java-WebSocket:1.6.0"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.mockito:mockito-core:5.18.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.mockito\mockito-core\5.18.0\ab47dbbf954ffa2501f29000600742098617272d\mockito-core-5.18.0.jar"
      resolved="org.mockito:mockito-core:5.18.0"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="net.bytebuddy:byte-buddy:1.17.5@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy\1.17.5\88450f120903b7e72470462cdbd2b75a3842223c\byte-buddy-1.17.5.jar"
      resolved="net.bytebuddy:byte-buddy:1.17.5"/>
  <library
      name="net.bytebuddy:byte-buddy-agent:1.17.5@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy-agent\1.17.5\58f9507f5f28d1d3e7adfb4ec2fe37c29d332c9b\byte-buddy-agent-1.17.5.jar"
      resolved="net.bytebuddy:byte-buddy-agent:1.17.5"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e26a53be7a7916f10f130fb66f1ee131\transformed\gifdecoder-4.16.0\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e26a53be7a7916f10f130fb66f1ee131\transformed\gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.collection:collection-ktx:1.4.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.2\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.2.jar"
      resolved="androidx.collection:collection-ktx:1.4.2"/>
  <library
      name="androidx.collection:collection-jvm:1.4.2@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.2\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\collection-jvm-1.4.2.jar"
      resolved="androidx.collection:collection-jvm:1.4.2"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework-android:2.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\43f391cecb985a27d6bb078c5ee1b4a8\transformed\sqlite-framework-release\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework-android:2.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\43f391cecb985a27d6bb078c5ee1b4a8\transformed\sqlite-framework-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-android:2.5.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4ff71d02c4960793b0365d811a66bb3\transformed\sqlite-release\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-android:2.5.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4ff71d02c4960793b0365d811a66bb3\transformed\sqlite-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ba5e66eb6f83825b0d07643743a9aa7\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ba5e66eb6f83825b0d07643743a9aa7\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.1.21@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.1.21\97a0975aa19d925e109537af60eb46902920015c\kotlin-stdlib-2.1.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.1.21"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cd0d0e6726c53a43dc29e6c7c159172f\transformed\tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cd0d0e6726c53a43dc29e6c7c159172f\transformed\tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.38.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.38.0\fc0ae991433e8590ba51cd558421478318a74c8c\error_prone_annotations-2.38.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.38.0"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.16.0\411aa175d50d10b37c7a1a04d21a4e7145249557\disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.16.0\90730f6498299d207aa0878124ab7585969808f0\annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7b93ebc42d048d238f4adb237df20227\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7b93ebc42d048d238f4adb237df20227\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:failureaccess:1.0.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.3\aeaffd00d57023a2c947393ed251f0354f0985fc\failureaccess-1.0.3.jar"
      resolved="com.google.guava:failureaccess:1.0.3"/>
  <library
      name="com.google.j2objc:j2objc-annotations:3.0.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\3.0.0\7399e65dd7e9ff3404f4535b2f017093bdb134c7\j2objc-annotations-3.0.0.jar"
      resolved="com.google.j2objc:j2objc-annotations:3.0.0"/>
  <library
      name="com.google.android.exoplayer:exoplayer-database:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bcd0827a4251f546bfdf7f4c32f9ef6f\transformed\exoplayer-database-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-database:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bcd0827a4251f546bfdf7f4c32f9ef6f\transformed\exoplayer-database-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-datasource:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5984dfa9289185364acc030aade2db8a\transformed\exoplayer-datasource-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-datasource:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5984dfa9289185364acc030aade2db8a\transformed\exoplayer-datasource-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-decoder:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fd5b71f0f43b49ea68ded67cd5b2f138\transformed\exoplayer-decoder-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-decoder:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fd5b71f0f43b49ea68ded67cd5b2f138\transformed\exoplayer-decoder-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-extractor:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\97535ac83451f328bbaa041d0d70e945\transformed\exoplayer-extractor-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-extractor:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\97535ac83451f328bbaa041d0d70e945\transformed\exoplayer-extractor-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-container:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1dd6d02f59940b6d75faa9312e99b8e3\transformed\exoplayer-container-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-container:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1dd6d02f59940b6d75faa9312e99b8e3\transformed\exoplayer-container-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-dash:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0fbb3b211719ae958d97451a3183ace9\transformed\exoplayer-dash-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-dash:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0fbb3b211719ae958d97451a3183ace9\transformed\exoplayer-dash-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-hls:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6695614451976994491f11c39abcce61\transformed\exoplayer-hls-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-hls:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6695614451976994491f11c39abcce61\transformed\exoplayer-hls-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-rtsp:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3af46e84777bcd36e645549e75b56457\transformed\exoplayer-rtsp-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-rtsp:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3af46e84777bcd36e645549e75b56457\transformed\exoplayer-rtsp-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\26be560e9ef01f7e364ba7c5215432bc\transformed\exoplayer-smoothstreaming-2.19.1\jars\classes.jar"
      resolved="com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\26be560e9ef01f7e364ba7c5215432bc\transformed\exoplayer-smoothstreaming-2.19.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.slf4j:slf4j-api:2.0.13@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.13\80229737f704b121a318bba5d5deacbcf395bc77\slf4j-api-2.0.13.jar"
      resolved="org.slf4j:slf4j-api:2.0.13"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\jars\classes.jar;D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\jars\classes.jar"
      resolved="androidx.window:window:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3fcb5dc4e9abc517aedcb5fc52a1729\transformed\customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3fcb5dc4e9abc517aedcb5fc52a1729\transformed\customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.9.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5bbdf0c494f35e618481c6837f0a746\transformed\lifecycle-service-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.9.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5bbdf0c494f35e618481c6837f0a746\transformed\lifecycle-service-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.crypto.tink:tink-android:1.8.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\com.google.crypto.tink\tink-android\1.8.0\bda82b49568d444a3b54773ca5aa487816473295\tink-android-1.8.0.jar"
      resolved="com.google.crypto.tink:tink-android:1.8.0"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.2.0@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.2.0\13b49fdb58c74bdfb12eb131b5e22d6d09683e30\concurrent-futures-ktx-1.2.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.2.0"/>
  <library
      name="org.objenesis:objenesis:3.3@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\org.objenesis\objenesis\3.3\1049c09f1de4331e8193e579448d0916d75b7631\objenesis-3.3.jar"
      resolved="org.objenesis:objenesis:3.3"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.1.1@jar"
      jars="D:\AndroidBuildEnv\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.1.1\f7ab6170b99b9421bd4942846426ff820b552f7d\constraintlayout-core-1.1.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.1.1"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cb13b020c0d38d87f0347711efcc50ec\transformed\tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cb13b020c0d38d87f0347711efcc50ec\transformed\tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ded6a0e4e424f1acfc5a5e666a7ca955\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      folder="D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ded6a0e4e424f1acfc5a5e666a7ca955\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
</libraries>
