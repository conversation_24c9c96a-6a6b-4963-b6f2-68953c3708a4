{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-hr/values-hr.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\21d61e9b45cfab2ee4b69c56a0b52398\\transformed\\appcompat-1.7.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1179,1284,1379,1486,1572,1676,1795,1880,1962,2053,2146,2241,2335,2435,2528,2623,2718,2809,2900,2986,3090,3202,3303,3408,3522,3624,3793,14628", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1279,1374,1481,1567,1671,1790,1875,1957,2048,2141,2236,2330,2430,2523,2618,2713,2804,2895,2981,3085,3197,3298,3403,3517,3619,3788,3885,14708"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8fcff5946e9cb42bb9e1450a210c2616\\transformed\\navigation-ui-2.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "182,183", "startColumns": "4,4", "startOffsets": "14320,14423", "endColumns": "102,124", "endOffsets": "14418,14543"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\eef25481716fc7890be7aca21c0506bf\\transformed\\exoplayer-core-2.19.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7539,7614,7675,7740,7813,7892,7965,8050,8132", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "7609,7670,7735,7808,7887,7960,8045,8127,8200"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\9f99db3bdee5091140633361bed826be\\transformed\\material-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1128,1196,1292,1360,1423,1531,1591,1657,1713,1784,1844,1898,2024,2081,2143,2197,2272,2406,2491,2569,2664,2749,2830,2967,3051,3137,3270,3361,3439,3495,3550,3616,3690,3768,3839,3921,3993,4070,4150,4224,4331,4424,4497,4589,4685,4759,4835,4931,4983,5065,5132,5219,5306,5368,5432,5495,5565,5671,5787,5884,5998,6058,6117,6197,6280,6357", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "315,393,471,556,653,746,842,972,1056,1123,1191,1287,1355,1418,1526,1586,1652,1708,1779,1839,1893,2019,2076,2138,2192,2267,2401,2486,2564,2659,2744,2825,2962,3046,3132,3265,3356,3434,3490,3545,3611,3685,3763,3834,3916,3988,4065,4145,4219,4326,4419,4492,4584,4680,4754,4830,4926,4978,5060,5127,5214,5301,5363,5427,5490,5560,5666,5782,5879,5993,6053,6112,6192,6275,6352,6427"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,68,69,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,184,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "959,3890,3968,4046,4131,4228,5047,5143,5273,5357,5424,9399,9495,9563,9626,9734,9794,9860,9916,9987,10047,10101,10227,10284,10346,10400,10475,10609,10694,10772,10867,10952,11033,11170,11254,11340,11473,11564,11642,11698,11753,11819,11893,11971,12042,12124,12196,12273,12353,12427,12534,12627,12700,12792,12888,12962,13038,13134,13186,13268,13335,13422,13509,13571,13635,13698,13768,13874,13990,14087,14201,14261,14548,14713,14796,14873", "endLines": "25,53,54,55,56,57,65,66,67,68,69,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,184,186,187,188", "endColumns": "12,77,77,84,96,92,95,129,83,66,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,77,94,84,80,136,83,85,132,90,77,55,54,65,73,77,70,81,71,76,79,73,106,92,72,91,95,73,75,95,51,81,66,86,86,61,63,62,69,105,115,96,113,59,58,79,82,76,74", "endOffsets": "1174,3963,4041,4126,4223,4316,5138,5268,5352,5419,5487,9490,9558,9621,9729,9789,9855,9911,9982,10042,10096,10222,10279,10341,10395,10470,10604,10689,10767,10862,10947,11028,11165,11249,11335,11468,11559,11637,11693,11748,11814,11888,11966,12037,12119,12191,12268,12348,12422,12529,12622,12695,12787,12883,12957,13033,13129,13181,13263,13330,13417,13504,13566,13630,13693,13763,13869,13985,14082,14196,14256,14315,14623,14791,14868,14943"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8c95fb1bf71448479a8aeb756e068ef5\\transformed\\exoplayer-ui-2.19.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3693,3764,3816,3879,3964,4049", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3688,3759,3811,3874,3959,4044,4100"}, "to": {"startLines": "2,11,16,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,390,680,5492,5573,5655,5735,5842,5949,6019,6086,6177,6269,6334,6405,6468,6540,6659,6783,6904,6972,7056,7127,7198,7302,7407,7474,8205,8258,8316,8364,8425,8499,8578,8654,8728,8792,8851,8922,8987,9058,9110,9173,9258,9343", "endLines": "10,15,20,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "385,675,954,5568,5650,5730,5837,5944,6014,6081,6172,6264,6329,6400,6463,6535,6654,6778,6899,6967,7051,7122,7193,7297,7402,7469,7534,8253,8311,8359,8420,8494,8573,8649,8723,8787,8846,8917,8982,9053,9105,9168,9253,9338,9394"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\50cbe4bff5d1390b137919b7462144e4\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "58,59,60,61,62,63,64,189", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4321,4419,4526,4623,4722,4826,4930,14948", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "4414,4521,4618,4717,4821,4925,5042,15044"}}]}]}