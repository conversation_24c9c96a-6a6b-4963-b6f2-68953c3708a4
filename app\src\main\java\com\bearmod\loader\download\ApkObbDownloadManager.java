package com.bearmod.loader.download;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkInfo;
import androidx.work.WorkManager;

import com.bearmod.loader.model.Patch;
import com.bearmod.loader.ui.download.DownloadProgressDialog;
import com.bearmod.loader.utils.ApkInstaller;

import java.io.File;
import java.text.DecimalFormat;

/**
 * Enhanced APK+OBB download manager
 * Handles modern download system with options dialog, progress tracking, and auto-installation
 */
public class ApkObbDownloadManager {
    
    private static final String TAG = "ApkObbDownloadManager";
    private static final String DOWNLOAD_WORK_NAME = "apk_obb_download";
    
    private static ApkObbDownloadManager instance;
    private final WorkManager workManager;
    private final Handler handler;
    private final DecimalFormat decimalFormat;
    
    private boolean isDownloading = false;
    private DownloadProgressDialog progressDialog;
    
    /**
     * Private constructor
     */
    private ApkObbDownloadManager() {
        workManager = WorkManager.getInstance();
        handler = new Handler(Looper.getMainLooper());
        decimalFormat = new DecimalFormat("#.#");
    }
    
    /**
     * Get singleton instance
     * @return ApkObbDownloadManager instance
     */
    public static synchronized ApkObbDownloadManager getInstance() {
        if (instance == null) {
            instance = new ApkObbDownloadManager();
        }
        return instance;
    }
    
    /**
     * Download APK/OBB files based on download type
     * @param context Application context
     * @param patch Patch to download
     * @param downloadType Type of download
     * @param listener Download completion listener
     */
    public void downloadGameFiles(Context context, Patch patch, DownloadType downloadType, 
                                 DownloadCompletionListener listener) {
        
        if (isDownloading) {
            Log.w(TAG, "Download already in progress");
            listener.onError("Download already in progress");
            return;
        }
        
        Log.d(TAG, "Starting download for: " + patch.getName() + " with type: " + downloadType);
        
        // Create download info
        DownloadInfo downloadInfo = createDownloadInfo(patch, downloadType);
        
        // Show progress dialog
        showProgressDialog(context, downloadInfo);
        
        // Create work request
        Data inputData = new Data.Builder()
                .putString("patchId", patch.getId())
                .putString("patchName", patch.getName())
                .putString("downloadType", downloadType.name())
                .putString("downloadUrl", downloadInfo.downloadUrl)
                .putString("fileName", downloadInfo.fileName)
                .putDouble("totalSizeMB", downloadInfo.totalSizeMB)
                .build();
        
        OneTimeWorkRequest downloadWorkRequest = new OneTimeWorkRequest.Builder(GameDownloadWorker.class)
                .setInputData(inputData)
                .build();
        
        // Set up progress tracking
        GameDownloadWorker.setProgressListener((progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds) -> {
            handler.post(() -> {
                if (progressDialog != null) {
                    progressDialog.updateProgress(progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds);
                }
            });
        });
        
        // Observe work status
        workManager.getWorkInfoByIdLiveData(downloadWorkRequest.getId()).observeForever(workInfo -> {
            if (workInfo != null) {
                if (workInfo.getState() == WorkInfo.State.SUCCEEDED) {
                    handler.post(() -> {
                        isDownloading = false;
                        String filePath = workInfo.getOutputData().getString("filePath");
                        handleDownloadSuccess(context, downloadType, filePath, listener);
                    });
                } else if (workInfo.getState() == WorkInfo.State.FAILED) {
                    handler.post(() -> {
                        isDownloading = false;
                        String error = workInfo.getOutputData().getString("error");
                        handleDownloadError(error != null ? error : "Download failed", listener);
                    });
                } else if (workInfo.getState() == WorkInfo.State.CANCELLED) {
                    handler.post(() -> {
                        isDownloading = false;
                        handleDownloadError("Download cancelled", listener);
                    });
                }
            }
        });
        
        // Set cancel listener
        if (progressDialog != null) {
            progressDialog.setDownloadCancelListener(() -> {
                Log.d(TAG, "User requested download cancellation");
                workManager.cancelUniqueWork(DOWNLOAD_WORK_NAME);
            });
        }
        
        // Enqueue work
        workManager.enqueueUniqueWork(
                DOWNLOAD_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                downloadWorkRequest);
        
        isDownloading = true;
    }
    
    /**
     * Create download info based on patch and download type
     * @param patch Patch to download
     * @param downloadType Download type
     * @return Download info
     */
    private DownloadInfo createDownloadInfo(Patch patch, DownloadType downloadType) {
        DownloadInfo info = new DownloadInfo();
        
        String baseName = patch.getName().replaceAll("\\s+", "-").toLowerCase();
        
        switch (downloadType) {
            case APK_ONLY:
                info.fileName = baseName + ".apk";
                info.downloadUrl = "https://example.com/apk/" + baseName + ".apk"; // Mock URL
                info.totalSizeMB = 120.5; // Mock size
                break;
            case APK_AND_OBB:
                info.fileName = baseName + "_full.zip";
                info.downloadUrl = "https://example.com/full/" + baseName + "_full.zip"; // Mock URL
                info.totalSizeMB = 1100.8; // Mock size
                break;
            case OBB_ONLY:
                info.fileName = baseName + ".obb";
                info.downloadUrl = "https://example.com/obb/" + baseName + ".obb"; // Mock URL
                info.totalSizeMB = 980.3; // Mock size
                break;
        }
        
        return info;
    }
    
    /**
     * Show progress dialog
     * @param context Application context
     * @param downloadInfo Download information
     */
    private void showProgressDialog(Context context, DownloadInfo downloadInfo) {
        String downloadTypeText = getDownloadTypeText(downloadInfo.fileName);
        
        progressDialog = new DownloadProgressDialog(context, downloadTypeText, downloadInfo.fileName);
        progressDialog.show();
    }
    
    /**
     * Get download type text for display
     * @param fileName File name
     * @return Display text
     */
    private String getDownloadTypeText(String fileName) {
        if (fileName.endsWith(".apk")) {
            return "APK Only";
        } else if (fileName.contains("_full")) {
            return "APK + OBB";
        } else if (fileName.endsWith(".obb")) {
            return "OBB Only";
        }
        return "Game Files";
    }
    
    /**
     * Handle download success
     * @param context Application context
     * @param downloadType Download type
     * @param filePath Downloaded file path
     * @param listener Completion listener
     */
    private void handleDownloadSuccess(Context context, DownloadType downloadType, String filePath, 
                                     DownloadCompletionListener listener) {
        Log.d(TAG, "Download completed: " + filePath);
        
        if (progressDialog != null) {
            progressDialog.showCompletion(true, "Download completed successfully");
        }
        
        // Auto-install APK if applicable
        if (downloadType == DownloadType.APK_ONLY || downloadType == DownloadType.APK_AND_OBB) {
            File downloadedFile = new File(filePath);
            if (downloadedFile.getName().endsWith(".apk")) {
                autoInstallApk(context, downloadedFile);
            }
        }
        
        listener.onSuccess(filePath);
    }
    
    /**
     * Handle download error
     * @param error Error message
     * @param listener Completion listener
     */
    private void handleDownloadError(String error, DownloadCompletionListener listener) {
        Log.e(TAG, "Download failed: " + error);
        
        if (progressDialog != null) {
            progressDialog.showCompletion(false, error);
        }
        
        listener.onError(error);
    }
    
    /**
     * Auto-install APK file
     * @param context Application context
     * @param apkFile APK file to install
     */
    private void autoInstallApk(Context context, File apkFile) {
        Log.d(TAG, "Auto-installing APK: " + apkFile.getAbsolutePath());
        
        ApkInstaller.installApk(context, apkFile, new ApkInstaller.InstallationCallback() {
            @Override
            public void onSuccess(String message) {
                Log.d(TAG, "APK installation started: " + message);
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "APK installation failed: " + error);
            }
        });
    }
    
    /**
     * Download type enum
     */
    public enum DownloadType {
        APK_ONLY,
        APK_AND_OBB,
        OBB_ONLY
    }
    
    /**
     * Download info class
     */
    private static class DownloadInfo {
        String fileName;
        String downloadUrl;
        double totalSizeMB;
    }
    
    /**
     * Download completion listener interface
     */
    public interface DownloadCompletionListener {
        void onSuccess(String filePath);
        void onError(String error);
    }
}
