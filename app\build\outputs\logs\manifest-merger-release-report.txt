-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:102:9-110:20
	android:grantUriPermissions
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:13-47
	android:authorities
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-64
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:13-37
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:13-62
manifest
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
MERGED from [androidx.databinding:viewbinding:8.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\be28ce63bac2cd01f6d1c391dfa6fe9d\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\425d4b0539cae6d21a1eeed5036978e4\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\318ade96510d95484c2030e5b9d0a298\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\023225165cd912e87d52c63a0aa23f5d\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8d1253ec0fc93acd53c5fd46d4f60417\transformed\navigation-ui-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\10e9d34d0303215234c3e3878d1883b0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c331959512d9a33669ba0caa36c41fa5\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c613a16b68334527942b1cc594b4b5c7\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3955b6ccf628507c33f20f62c1c095e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9e80fb7ee9d1ca535c9b4dbf890423b5\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\404707ec20de0d8cde58d9d9cd0236bc\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7eec7d8268129de76a0be6a0757a7ef5\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a4fb1c2e1fb11023fe27b3d0a699c4d7\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c5a298e53c344c649a9b9b36873b545d\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\80d596ff6c7c808027613582bf3daad8\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aeb794549544d5c7523f1b0f200eabbb\transformed\fragment-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f0069afdef34cfd1c0badff78d69cf97\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\316e9950fbf2ca26b95f45c9ba2d689e\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\15f42126263e7a97448eae59481a2719\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f23d3013b7dd70b3efd40d31fe472652\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\87facd601e715e44da759569aba39b55\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\65cce4ad8d326a359666f63f17960852\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83a97e62e224813103a23de5ca3b904e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96e810d4b42cd7e0b024ad1e271cdd1b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec2fd796047ad88361eadb9f5f0cd9a4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\76a00043e874c48a1dc77c287bc3408b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ef72eb30f31ff399b2d506cdbbf7d88\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b10f86cf90f289cc114b9070b751b65\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ebb0eddcd9530b78e5e381e120ee35c8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fcb99e4be41ac499d148d39068a0b83\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e6caa8a463050f6cc448924f7e871fb4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\49e128596586890b5e80ac1c291cda1a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d79b6b71ed43a1fb3436d66eab5af176\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fda4875abbd57cf0e40ab62624686879\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\571379312175ad99b7875cc45ec3fd3c\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0a72a8d5710ad5df6fd141087e41aaa4\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4e33c84bc290cbdec91d52594f99f91b\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f2f3e22b3f6dd62e1208b0ff6abdd97\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f47e9a9ad0b993acc6a49b69c0511798\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83368d95587c53536fc6edbb6c2429e5\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a3e5a162c52ed8d94cf025e7305c0638\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4eca2ce34e099cac84914b9887a72d0f\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\badf863738da4cec4ec347369b8fed6d\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd377c14ba1b94a88b1b8ef27d698d2\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\14ffc38d372cf655808c8468b31bd7e1\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6839d992cb28d16b266ea057f71823c3\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b760527796b8626827aa3edd74d0c222\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12c8e7255809dc3614e072d49b125866\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\59078c7ce5438c1d077427388eebc0f5\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\444dd2219c4910d2620f2f5771ca00ff\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b32fefe702eea1aedd00bcc3c93a8df0\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d1a7c107f2d1734124d9e52c4ba2a69b\transformed\shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ad2fb29818d2947c5dc114316131624\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\feb8895d496fe59f08aac8280ecac2b6\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ea2740499c19c3787ddc8e653559a738\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e8236a4be1fa10a5b0da3dbe3dd15a76\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc66b50e9957a4098b76f50ce9caf901\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9a35b2465b3db4d554dff592a91a2e2d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6a6713bc90828ded954d9ff58d670318\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2b553a0a6797c3149764651761d5c1ff\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\40503dca37aff8c7683d2e8fe94d5a34\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5741c001d0effa857fe45a66a8490590\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f02c364c67b6c43bc136e348f92531b7\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\206c606608d7079d1ef7e55169d704e1\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71633e66afbd02b6778236104ce9c5d5\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\830ea6a84521a856ce8413b11e7f8c7f\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\abcc1de915a86fc02ac40bf72d886657\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dc89b11354378e8afc26a40b32acfa4\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\861b36ff44440a93b4355f1b6f46b88f\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\647611225c4245b1ce9c8ab5245e6ae0\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a9ea16a098136264d5d7f84e19d5f4e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1983d72d5ce7a98ffd9c46b1f699795\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-14:40
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:14:9-37
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:5-90
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:22-87
uses-permission#android.permission.VIBRATE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:5-66
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-68
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-77
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:5-80
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:22-77
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-83
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-80
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:5-35:47
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:35:9-44
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:22-72
application
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-112:19
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-112:19
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-54
	android:icon
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-43
	android:networkSecurityConfig
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:48:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-35
	android:label
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-41
	android:fullBackupContent
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-54
	tools:targetApi
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:49:9-29
	android:allowBackup
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-35
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-48
	android:dataExtractionRules
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-65
	android:usesCleartextTraffic
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-44
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-46
activity#com.bearmod.loader.ui.splash.SplashActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:9-62:20
	android:screenOrientation
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:13-52
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-36
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:13-42
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:13-61:29
action#android.intent.action.MAIN
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:17-69
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:17-77
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:27-74
activity#com.bearmod.loader.ui.auth.LoginActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:9-69:58
	android:windowSoftInputMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:69:13-55
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:67:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:68:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-50
activity#com.bearmod.loader.ui.auth.TapToUnlockActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:9-75:67
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:74:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:75:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-56
activity#com.bearmod.loader.ui.main.MainLoaderActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:9-82:46
	android:launchMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:82:13-43
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:80:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:81:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-55
activity#com.bearmod.loader.ui.download.ModernDownloadActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:9-89:46
	android:launchMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:89:13-43
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:87:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:88:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-63
activity#com.bearmod.loader.ui.settings.SettingsActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:94:9-99:45
	android:screenOrientation
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:13-52
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:99:13-42
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:97:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:95:13-57
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:107:13-109:54
	android:resource
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:109:17-51
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:108:17-67
uses-sdk
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\be28ce63bac2cd01f6d1c391dfa6fe9d\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\be28ce63bac2cd01f6d1c391dfa6fe9d\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\425d4b0539cae6d21a1eeed5036978e4\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\425d4b0539cae6d21a1eeed5036978e4\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\318ade96510d95484c2030e5b9d0a298\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\318ade96510d95484c2030e5b9d0a298\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\023225165cd912e87d52c63a0aa23f5d\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\023225165cd912e87d52c63a0aa23f5d\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8d1253ec0fc93acd53c5fd46d4f60417\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8d1253ec0fc93acd53c5fd46d4f60417\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\377aab835c73943f35a2dc72ba361cea\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\807ff79b5d357f9c16844927d19acb41\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0b2c07c996ea0d0dd989a691e4c5ab9f\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\10e9d34d0303215234c3e3878d1883b0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\10e9d34d0303215234c3e3878d1883b0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c331959512d9a33669ba0caa36c41fa5\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c331959512d9a33669ba0caa36c41fa5\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c613a16b68334527942b1cc594b4b5c7\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c613a16b68334527942b1cc594b4b5c7\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3955b6ccf628507c33f20f62c1c095e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3955b6ccf628507c33f20f62c1c095e\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9e80fb7ee9d1ca535c9b4dbf890423b5\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9e80fb7ee9d1ca535c9b4dbf890423b5\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\404707ec20de0d8cde58d9d9cd0236bc\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\404707ec20de0d8cde58d9d9cd0236bc\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7eec7d8268129de76a0be6a0757a7ef5\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7eec7d8268129de76a0be6a0757a7ef5\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a4fb1c2e1fb11023fe27b3d0a699c4d7\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a4fb1c2e1fb11023fe27b3d0a699c4d7\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c5a298e53c344c649a9b9b36873b545d\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c5a298e53c344c649a9b9b36873b545d\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\80d596ff6c7c808027613582bf3daad8\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\80d596ff6c7c808027613582bf3daad8\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aeb794549544d5c7523f1b0f200eabbb\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aeb794549544d5c7523f1b0f200eabbb\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f0069afdef34cfd1c0badff78d69cf97\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f0069afdef34cfd1c0badff78d69cf97\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\316e9950fbf2ca26b95f45c9ba2d689e\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\316e9950fbf2ca26b95f45c9ba2d689e\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\15f42126263e7a97448eae59481a2719\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\15f42126263e7a97448eae59481a2719\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f23d3013b7dd70b3efd40d31fe472652\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f23d3013b7dd70b3efd40d31fe472652\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\87facd601e715e44da759569aba39b55\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\87facd601e715e44da759569aba39b55\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\65cce4ad8d326a359666f63f17960852\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\65cce4ad8d326a359666f63f17960852\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83a97e62e224813103a23de5ca3b904e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83a97e62e224813103a23de5ca3b904e\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96e810d4b42cd7e0b024ad1e271cdd1b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96e810d4b42cd7e0b024ad1e271cdd1b\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec2fd796047ad88361eadb9f5f0cd9a4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec2fd796047ad88361eadb9f5f0cd9a4\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\76a00043e874c48a1dc77c287bc3408b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\76a00043e874c48a1dc77c287bc3408b\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ef72eb30f31ff399b2d506cdbbf7d88\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ef72eb30f31ff399b2d506cdbbf7d88\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b10f86cf90f289cc114b9070b751b65\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b10f86cf90f289cc114b9070b751b65\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ebb0eddcd9530b78e5e381e120ee35c8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ebb0eddcd9530b78e5e381e120ee35c8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fcb99e4be41ac499d148d39068a0b83\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fcb99e4be41ac499d148d39068a0b83\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e6caa8a463050f6cc448924f7e871fb4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e6caa8a463050f6cc448924f7e871fb4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\49e128596586890b5e80ac1c291cda1a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\49e128596586890b5e80ac1c291cda1a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d79b6b71ed43a1fb3436d66eab5af176\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d79b6b71ed43a1fb3436d66eab5af176\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fda4875abbd57cf0e40ab62624686879\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fda4875abbd57cf0e40ab62624686879\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\571379312175ad99b7875cc45ec3fd3c\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\571379312175ad99b7875cc45ec3fd3c\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0a72a8d5710ad5df6fd141087e41aaa4\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0a72a8d5710ad5df6fd141087e41aaa4\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4e33c84bc290cbdec91d52594f99f91b\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4e33c84bc290cbdec91d52594f99f91b\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f2f3e22b3f6dd62e1208b0ff6abdd97\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4f2f3e22b3f6dd62e1208b0ff6abdd97\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f47e9a9ad0b993acc6a49b69c0511798\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f47e9a9ad0b993acc6a49b69c0511798\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83368d95587c53536fc6edbb6c2429e5\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\83368d95587c53536fc6edbb6c2429e5\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a3e5a162c52ed8d94cf025e7305c0638\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a3e5a162c52ed8d94cf025e7305c0638\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4eca2ce34e099cac84914b9887a72d0f\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4eca2ce34e099cac84914b9887a72d0f\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\badf863738da4cec4ec347369b8fed6d\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\badf863738da4cec4ec347369b8fed6d\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd377c14ba1b94a88b1b8ef27d698d2\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd377c14ba1b94a88b1b8ef27d698d2\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\14ffc38d372cf655808c8468b31bd7e1\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\14ffc38d372cf655808c8468b31bd7e1\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6839d992cb28d16b266ea057f71823c3\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6839d992cb28d16b266ea057f71823c3\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b760527796b8626827aa3edd74d0c222\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b760527796b8626827aa3edd74d0c222\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12c8e7255809dc3614e072d49b125866\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\12c8e7255809dc3614e072d49b125866\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\59078c7ce5438c1d077427388eebc0f5\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\59078c7ce5438c1d077427388eebc0f5\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\444dd2219c4910d2620f2f5771ca00ff\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\444dd2219c4910d2620f2f5771ca00ff\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b32fefe702eea1aedd00bcc3c93a8df0\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b32fefe702eea1aedd00bcc3c93a8df0\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d1a7c107f2d1734124d9e52c4ba2a69b\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d1a7c107f2d1734124d9e52c4ba2a69b\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ad2fb29818d2947c5dc114316131624\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ad2fb29818d2947c5dc114316131624\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\feb8895d496fe59f08aac8280ecac2b6\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\feb8895d496fe59f08aac8280ecac2b6\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ea2740499c19c3787ddc8e653559a738\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ea2740499c19c3787ddc8e653559a738\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e8236a4be1fa10a5b0da3dbe3dd15a76\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e8236a4be1fa10a5b0da3dbe3dd15a76\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc66b50e9957a4098b76f50ce9caf901\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc66b50e9957a4098b76f50ce9caf901\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b64ae44a4b39fd32f83551ff7b44b772\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9a35b2465b3db4d554dff592a91a2e2d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9a35b2465b3db4d554dff592a91a2e2d\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6a6713bc90828ded954d9ff58d670318\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6a6713bc90828ded954d9ff58d670318\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2b553a0a6797c3149764651761d5c1ff\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2b553a0a6797c3149764651761d5c1ff\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\40503dca37aff8c7683d2e8fe94d5a34\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\40503dca37aff8c7683d2e8fe94d5a34\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5741c001d0effa857fe45a66a8490590\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5741c001d0effa857fe45a66a8490590\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f02c364c67b6c43bc136e348f92531b7\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f02c364c67b6c43bc136e348f92531b7\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\acb2146fe67d207adf9b8d19595c8e8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\206c606608d7079d1ef7e55169d704e1\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\206c606608d7079d1ef7e55169d704e1\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71633e66afbd02b6778236104ce9c5d5\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71633e66afbd02b6778236104ce9c5d5\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\830ea6a84521a856ce8413b11e7f8c7f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\830ea6a84521a856ce8413b11e7f8c7f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\abcc1de915a86fc02ac40bf72d886657\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\abcc1de915a86fc02ac40bf72d886657\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dc89b11354378e8afc26a40b32acfa4\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dc89b11354378e8afc26a40b32acfa4\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\861b36ff44440a93b4355f1b6f46b88f\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\861b36ff44440a93b4355f1b6f46b88f\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\647611225c4245b1ce9c8ab5245e6ae0\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\647611225c4245b1ce9c8ab5245e6ae0\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a9ea16a098136264d5d7f84e19d5f4e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a9ea16a098136264d5d7f84e19d5f4e\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1983d72d5ce7a98ffd9c46b1f699795\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a1983d72d5ce7a98ffd9c46b1f699795\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfaac63df5999980b4868c3a36f4fdec\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4df6a9d2af294a8be8604e4a14b6e139\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1ade474999f9be7a36a910d702ed7183\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41b63ad5aa65d1548fb7548572ef771d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\55919508f6a916749b6624a060d17abd\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b44ca9054b0af5bdf8bf4a39ab85986a\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e1d41ad0d5c82592d36e9b8a70e5ff24\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7ab424fa6be443539b478839e20f51cc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
