<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF4081</color>
    <color name="accent_dark">#C51162</color>
    <color name="accent_light">#FF80AB</color>
    <color name="background">#121212</color>
    <color name="background_light">#1E1E1E</color>
    <color name="black">#FF000000</color>
    <color name="card_background">#1F1F1F</color>
    <color name="card_bg">#1F1F1F</color>
    <color name="divider">#E0E0E0</color>
    <color name="error">#F44336</color>
    <color name="info">#2196F3</color>
    <color name="on_background">#FFFFFF</color>
    <color name="on_primary">#FFFFFF</color>
    <color name="on_secondary">#000000</color>
    <color name="on_surface">#000000</color>
    <color name="outline">#CCCCCC</color>
    <color name="outline_variant">#E0E0E0</color>
    <color name="primary">#2196F3</color>
    <color name="primary_container">#E3F2FD</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="progress_background">#424242</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="ripple">#33FFFFFF</color>
    <color name="secondary">#03DAC5</color>
    <color name="secondary_container">#E0F7FA</color>
    <color name="secondary_dark">#018786</color>
    <color name="secondary_light">#B2EBF2</color>
    <color name="shimmer_color">#DDDDDD</color>
    <color name="success">#4CAF50</color>
    <color name="surface">#FFFFFF</color>
    <color name="surface_container">#FAFAFA</color>
    <color name="surface_container_high">#EEEEEE</color>
    <color name="surface_container_highest">#E0E0E0</color>
    <color name="surface_container_low">#F8F8F8</color>
    <color name="surface_container_lowest">#FFFFFF</color>
    <color name="surface_variant">#F5F5F5</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_hint">#80FFFFFF</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3FFFFFF</color>
    <color name="transparent">#00000000</color>
    <color name="warning">#FFC107</color>
    <color name="white">#FFFFFF</color>
    <dimen name="corner_radius_large">24dp</dimen>
    <dimen name="corner_radius_medium">16dp</dimen>
    <dimen name="corner_radius_small">8dp</dimen>
    <dimen name="elevation_large">8dp</dimen>
    <dimen name="elevation_medium">4dp</dimen>
    <dimen name="elevation_small">2dp</dimen>
    <dimen name="glyph_corner_radius">28dp</dimen>
    <dimen name="icon_size_large">48dp</dimen>
    <dimen name="icon_size_medium">24dp</dimen>
    <dimen name="icon_size_small">16dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_medium">16dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="text_size_caption">12sp</dimen>
    <dimen name="text_size_large">20sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <string name="about">About</string>
    <string name="about_description">BearMod-Loader is a powerful tool for managing and applying patches to enhance your gaming experience.</string>
    <string name="all">All</string>
    <string name="apk_size">APK Size: %1$s</string>
    <string name="app_logo">App Logo</string>
    <string name="app_name">BearMod Loader</string>
    <string name="app_settings">App Settings</string>
    <string name="app_version">Version 1.3.0</string>
    <string name="apply_patch">Apply Patch</string>
    <string name="auto_login">Auto Login</string>
    <string name="auto_update">Auto-update</string>
    <string name="available">Available</string>
    <string name="available_patches">Available Patches</string>
    <string name="available_releases">Available Releases</string>
    <string name="cache_cleared">Cache cleared</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_download">Cancel Download</string>
    <string name="cancelled">Cancelled</string>
    <string name="clear_cache">Clear Cache</string>
    <string name="clear_cache_confirm">Are you sure you want to clear the cache? This will remove all downloaded patches.</string>
    <string name="clear_filters">Clear filters</string>
    <string name="completed">Completed</string>
    <string name="config_reset">Configuration reset</string>
    <string name="confirm">Confirm</string>
    <string name="continue_anyway">Continue Anyway</string>
    <string name="continue_download">Continue Download</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="dashboard_title">Dashboard</string>
    <string name="days_remaining">%1$d days remaining</string>
    <string name="delete">Delete</string>
    <string name="delete_all_downloads">Delete all downloads?</string>
    <string name="delete_download">Delete download?</string>
    <string name="download">Download</string>
    <string name="download_cancelled">Download Cancelled</string>
    <string name="download_complete">Download Complete</string>
    <string name="download_complete_path">Download Complete: %1$s</string>
    <string name="download_failed">Download Failed: %1$s</string>
    <string name="download_in_progress">Download in progress. Do you want to cancel?</string>
    <string name="download_patches">Download Patches</string>
    <string name="download_progress">Download Progress</string>
    <string name="download_title">Download</string>
    <string name="downloaded">Downloaded</string>
    <string name="downloading">Downloading… %1$s / %2$s</string>
    <string name="downloading_detailed">Downloading… %1$s / %2$s • %3$s • ETA: %4$s</string>
    <string name="downloading_status">Downloading</string>
    <string name="downloads">Downloads</string>
    <string name="enter_license_key">Enter your license key</string>
    <string name="error">Error</string>
    <string name="eta">ETA</string>
    <string name="execution_logs">Execution Logs</string>
    <string name="execution_mode">Execution Mode</string>
    <string name="exit">Exit</string>
    <string name="failed">Failed</string>
    <string name="file_size_format">%1$s MB</string>
    <string name="filter_by_version">Filter by version</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="game_icon">Game icon</string>
    <string name="game_version">Game Version: %1$s</string>
    <string name="game_version_format">Version %1$s</string>
    <string name="global_version_desc">Global version patch</string>
    <string name="icon_filter">Filter</string>
    <string name="icon_search">Search</string>
    <string name="icon_sort">Sort</string>
    <string name="info">Information</string>
    <string name="invalid_credentials">Invalid username or password</string>
    <string name="invalid_license_key_format">Invalid license key format</string>
    <string name="keyauth_init_failed">Failed to initialize KeyAuth. Please check your internet connection and try again.</string>
    <string name="keyauth_init_warning">KeyAuth initialization warning. You may experience login issues.</string>
    <string name="kr_version_desc">Korean version patch</string>
    <string name="language">Language</string>
    <string name="latest">Latest</string>
    <string name="license_key">License Key</string>
    <string name="license_valid_until">License valid until: %1$s</string>
    <string name="licenses">Licenses</string>
    <string name="loading">Loading…</string>
    <string name="login">Login</string>
    <string name="login_error">Login failed: %1$s</string>
    <string name="login_failed">Login failed</string>
    <string name="login_subtitle">Enter your license key to continue</string>
    <string name="login_success">Login successful</string>
    <string name="logout">Logout</string>
    <string name="logout_confirm">Are you sure you want to logout? You will need to enter your license key again.</string>
    <string name="nav_downloads">Downloads</string>
    <string name="nav_home">Home</string>
    <string name="nav_settings">Settings</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="network_error">Network error</string>
    <string name="no">No</string>
    <string name="no_downloads">No downloads yet</string>
    <string name="no_patches_available">No patches available</string>
    <string name="no_releases_available">No releases available</string>
    <string name="no_results_found">No results found</string>
    <string name="non_root_mode">Non-Root</string>
    <string name="not_installed">Not Installed</string>
    <string name="notification_settings">Notification Settings</string>
    <string name="notifications">Notifications</string>
    <string name="obb_size">OBB Size: %1$s</string>
    <string name="ok">OK</string>
    <string name="password">Password</string>
    <string name="patch_execution">Patch Execution</string>
    <string name="patch_settings">Patch Settings</string>
    <string name="patch_status">Patch Status</string>
    <string name="patches_updated">Patches updated</string>
    <string name="patching_complete">Patching completed successfully</string>
    <string name="patching_failed">Patching failed: %1$s</string>
    <string name="patching_in_progress">Patching in progress…</string>
    <string name="pause">Pause</string>
    <string name="paused">Paused</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="pubg_global">PUBG MOBILE GL</string>
    <string name="pubg_kr">PUBG MOBILE KR</string>
    <string name="pubg_tw">PUBG MOBILE TW</string>
    <string name="pubg_vn">PUBG MOBILE VN</string>
    <string name="pull_down_to_refresh_or_check_your_connection">Pull down to refresh or check your connection</string>
    <string name="register">Register</string>
    <string name="registration_date">Registration date: %1$s</string>
    <string name="release_date_format">Released: %1$s</string>
    <string name="released">Released: %1$s</string>
    <string name="remember_me">Remember me</string>
    <string name="reset_config">Reset Configuration</string>
    <string name="reset_config_confirm">Are you sure you want to reset the configuration? This will restore all settings to their default values.</string>
    <string name="resume">Resume</string>
    <string name="retry">Retry</string>
    <string name="root_mode">Root</string>
    <string name="scan_offsets">Scan Offsets</string>
    <string name="scanning_offsets">Scanning Offsets</string>
    <string name="scanning_offsets_for">Scanning offsets for %1$s</string>
    <string name="search_releases">Search releases…</string>
    <string name="select_release_first">Please select a release first</string>
    <string name="select_target">Select Target</string>
    <string name="select_version">Select a version to patch</string>
    <string name="settings_title">Settings</string>
    <string name="sort_by_date">Sort by date</string>
    <string name="sort_by_size">Sort by size</string>
    <string name="start_patching">Start Patching</string>
    <string name="stop_patching">Stop Patching</string>
    <string name="success">Success</string>
    <string name="sync_error">Sync error: %1$s</string>
    <string name="target_app">Target App</string>
    <string name="terms_of_service">Terms of Service</string>
    <string name="theme">Theme</string>
    <string name="theme_dark">Dark</string>
    <string name="theme_light">Light</string>
    <string name="theme_system">System default</string>
    <string name="toggle_stealth">Toggle Stealth Mode</string>
    <string name="total_size">Total Size: %1$s</string>
    <string name="tw_version_desc">Taiwan version patch</string>
    <string name="unknown_error">Unknown error occurred</string>
    <string name="up_to_date">Up to Date</string>
    <string name="update_available">Update Available</string>
    <string name="updated">Updated: %1$s</string>
    <string name="username">Username</string>
    <string name="version">Version</string>
    <string name="version_format">Version %s</string>
    <string name="version_info">Version %1$s</string>
    <string name="vn_version_desc">Vietnam version patch</string>
    <string name="warning">Warning</string>
    <string name="yes">Yes</string>
    <style name="Animation.BearLoader.Activity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right</item>
    </style>
    <style name="ShapeAppearance.BearLoader.LargeComponent" parent="ShapeAppearance.Material3.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">28dp</item>
    </style>
    <style name="ShapeAppearance.BearLoader.MediumComponent" parent="ShapeAppearance.Material3.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">24dp</item>
    </style>
    <style name="ShapeAppearance.BearLoader.SmallComponent" parent="ShapeAppearance.Material3.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>
    <style name="TextAppearance.BearLoader.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">@dimen/text_size_medium</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.BearLoader.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">@dimen/text_size_small</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="TextAppearance.BearLoader.Caption" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:textSize">@dimen/text_size_caption</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="TextAppearance.BearLoader.Headline6" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="Theme.BearLoader" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/primary_light</item>
        <item name="colorOnPrimaryContainer">@color/background</item>

        
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_dark</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorSecondaryContainer">@color/accent_light</item>
        <item name="colorOnSecondaryContainer">@color/background</item>

        
        <item name="colorSurface">@color/surface_container</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorOnBackground">@color/text_primary</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowBackground">@color/background</item>

        
        <item name="android:windowSplashScreenBackground">@color/background</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_launcher_foreground</item>
        <item name="android:windowSplashScreenIconBackgroundColor">@color/primary</item>
        <item name="android:windowSplashScreenBrandingImage">@drawable/splash_branding</item>

        
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.BearLoader.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.BearLoader.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.BearLoader.LargeComponent</item>
    </style>
    <style name="Theme.BearLoader.AppBarOverlay" parent="ThemeOverlay.Material3.Dark.ActionBar"/>
    <style name="Theme.BearLoader.Dialog" parent="ThemeOverlay.Material3.Dialog.Alert">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorAccent">@color/accent</item>
        <item name="android:background">@color/background</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
    </style>
    <style name="Theme.BearLoader.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="Theme.BearLoader.PopupOverlay" parent="ThemeOverlay.Material3.Light"/>
    <style name="Theme.BearLoader.Splash" parent="Theme.BearLoader.NoActionBar">
        <item name="android:windowBackground">@color/background</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
    <style name="Theme.BearMod" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryContainer">@color/primary_container</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryContainer">@color/secondary_container</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="Widget.BearLoader.Button" parent="Widget.Material3.Button">
        
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.025</item>
        <item name="android:textAllCaps">false</item>

        
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>

        
        <item name="backgroundTint">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
        
        
        <item name="android:alpha">1.0</item>
        <item name="android:enabled">true</item>
    </style>
    <style name="Widget.BearLoader.Button.Icon" parent="Widget.Material3.Button.IconButton">
        <item name="iconTint">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="cornerRadius">24dp</item>
    </style>
    <style name="Widget.BearLoader.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.025</item>
        <item name="android:textAllCaps">false</item>

        
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>

        
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">2dp</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="android:background">@android:color/transparent</item>
        
        
        <item name="android:alpha">1.0</item>
        <item name="android:enabled">true</item>
    </style>
    <style name="Widget.BearLoader.Button.Small" parent="Widget.Material3.Button">
        
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.025</item>
        <item name="android:textAllCaps">false</item>

        
        <item name="cornerRadius">16dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>

        
        <item name="backgroundTint">@color/primary</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="elevation">2dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="Widget.BearLoader.Button.Tonal" parent="Widget.Material3.Button.TonalButton">
        
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.025</item>
        <item name="android:textAllCaps">false</item>

        
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>

        
        <item name="backgroundTint">@color/surface_container_highest</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="elevation">2dp</item>
        <item name="android:stateListAnimator">@null</item>
        
        
        <item name="android:alpha">1.0</item>
        <item name="android:enabled">true</item>
    </style>
</resources>