<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="135" endOffset="53"/></Target><Target id="@+id/logoContainer" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="20" startOffset="12" endLine="38" endOffset="63"/></Target><Target id="@+id/logoImage" view="ImageView"><Expressions/><location startLine="30" startOffset="16" endLine="36" endOffset="44"/></Target><Target id="@+id/appNameText" view="TextView"><Expressions/><location startLine="40" startOffset="12" endLine="50" endOffset="45"/></Target><Target id="@+id/textInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="52" startOffset="12" endLine="79" endOffset="67"/></Target><Target id="@+id/editLicenseKey" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="16" endLine="77" endOffset="53"/></Target><Target id="@+id/checkboxRemember" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="81" startOffset="12" endLine="88" endOffset="51"/></Target><Target id="@+id/checkboxAutoLogin" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="90" startOffset="12" endLine="97" endOffset="51"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="99" startOffset="12" endLine="111" endOffset="52"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="113" startOffset="12" endLine="119" endOffset="58"/></Target><Target id="@+id/versionText" view="TextView"><Expressions/><location startLine="121" startOffset="12" endLine="129" endOffset="37"/></Target></Targets></Layout>