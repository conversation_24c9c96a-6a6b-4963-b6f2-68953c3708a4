<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_login_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="142" endOffset="53"/></Target><Target id="@+id/logoContainer" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="21" startOffset="12" endLine="40" endOffset="63"/></Target><Target id="@+id/logoImage" view="ImageView"><Expressions/><location startLine="31" startOffset="16" endLine="38" endOffset="44"/></Target><Target id="@+id/appNameText" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="53" endOffset="46"/></Target><Target id="@+id/textInputLayout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="55" startOffset="12" endLine="83" endOffset="67"/></Target><Target id="@+id/editLicenseKey" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="73" startOffset="16" endLine="81" endOffset="53"/></Target><Target id="@+id/checkboxRemember" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="85" startOffset="12" endLine="93" endOffset="46"/></Target><Target id="@+id/checkboxAutoLogin" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="95" startOffset="12" endLine="103" endOffset="46"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="105" startOffset="12" endLine="118" endOffset="46"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="120" startOffset="12" endLine="126" endOffset="58"/></Target><Target id="@+id/versionText" view="TextView"><Expressions/><location startLine="128" startOffset="12" endLine="136" endOffset="37"/></Target></Targets></Layout>