package com.bearmod.loader.security;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Build;
import android.util.Log;

import java.io.ByteArrayInputStream;
import java.security.MessageDigest;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Enhanced security manager for app signature verification and download security
 */
public class AppSecurityManager {
    
    private static final String TAG = "AppSecurityManager";
    
    // Expected signature fingerprints for your app (add your actual signatures here)
    private static final Set<String> TRUSTED_SIGNATURES = new HashSet<>(Arrays.asList(
        // Add your release signature fingerprint here
        "YOUR_RELEASE_SIGNATURE_SHA256_FINGERPRINT",
        // Add your debug signature fingerprint for testing
        "YOUR_DEBUG_SIGNATURE_SHA256_FINGERPRINT"
    ));
    
    // Trusted PUBG Mobile package signatures (for verification when installing)
    private static final Set<String> TRUSTED_PUBG_SIGNATURES = new HashSet<>(Arrays.asList(
        // Tencent Global signature
        "14:6D:E9:83:C5:73:06:50:D8:EE:B9:95:2F:34:FC:64:16:A0:83:42:E6:1D:BE:A8:8A:04:96:B2:3F:CF:44:E5",
        // PUBG KR signature  
        "B7:27:A5:08:80:92:59:B2:A6:92:26:4A:10:1E:64:59:79:7C:70:11:BC:57:F0:8D:21:93:21:E7:9A:5F:54:BC",
        // Add other region signatures as needed
        "PUBG_TW_SIGNATURE_FINGERPRINT",
        "PUBG_VN_SIGNATURE_FINGERPRINT"
    ));
    
    /**
     * Verify that the current app has a trusted signature
     * @param context Application context
     * @return true if signature is trusted, false otherwise
     */
    public static boolean verifyAppSignature(Context context) {
        try {
            String currentSignature = getAppSignature(context, context.getPackageName());
            boolean isTrusted = TRUSTED_SIGNATURES.contains(currentSignature);
            
            Log.d(TAG, "App signature verification: " + (isTrusted ? "PASSED" : "FAILED"));
            Log.d(TAG, "Current signature: " + currentSignature);
            
            return isTrusted;
        } catch (Exception e) {
            Log.e(TAG, "Failed to verify app signature", e);
            return false;
        }
    }
    
    /**
     * Verify PUBG Mobile app signature before allowing downloads
     * @param context Application context
     * @param packageName PUBG Mobile package name
     * @return true if PUBG signature is trusted, false otherwise
     */
    public static boolean verifyPubgSignature(Context context, String packageName) {
        try {
            // Check if PUBG Mobile is installed
            if (!isPackageInstalled(context, packageName)) {
                Log.d(TAG, "PUBG Mobile not installed: " + packageName);
                return true; // Allow download if not installed
            }
            
            String pubgSignature = getAppSignature(context, packageName);
            boolean isTrusted = TRUSTED_PUBG_SIGNATURES.contains(pubgSignature);
            
            Log.d(TAG, "PUBG signature verification for " + packageName + ": " + (isTrusted ? "PASSED" : "FAILED"));
            Log.d(TAG, "PUBG signature: " + pubgSignature);
            
            return isTrusted;
        } catch (Exception e) {
            Log.e(TAG, "Failed to verify PUBG signature for " + packageName, e);
            return false;
        }
    }
    
    /**
     * Get app signature fingerprint
     * @param context Application context
     * @param packageName Package name to get signature for
     * @return SHA-256 signature fingerprint
     * @throws Exception If signature retrieval fails
     */
    public static String getAppSignature(Context context, String packageName) throws Exception {
        PackageManager pm = context.getPackageManager();
        
        PackageInfo packageInfo;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNING_CERTIFICATES);
            if (packageInfo.signingInfo == null) {
                throw new Exception("No signing info found for " + packageName);
            }
            
            Signature[] signatures = packageInfo.signingInfo.getApkContentsSigners();
            if (signatures.length == 0) {
                throw new Exception("No signatures found for " + packageName);
            }
            
            return getSignatureFingerprint(signatures[0]);
        } else {
            packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNATURES);
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                throw new Exception("No signatures found for " + packageName);
            }
            
            return getSignatureFingerprint(packageInfo.signatures[0]);
        }
    }
    
    /**
     * Get signature fingerprint using SHA-256
     * @param signature Signature to get fingerprint for
     * @return SHA-256 fingerprint string
     * @throws Exception If fingerprint generation fails
     */
    private static String getSignatureFingerprint(Signature signature) throws Exception {
        byte[] signatureBytes = signature.toByteArray();
        
        // Get certificate
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(signatureBytes));
        
        // Get SHA-256 fingerprint
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] fingerprint = md.digest(cert.getEncoded());
        
        // Convert to hex string with colons
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fingerprint.length; i++) {
            if (i > 0) {
                sb.append(":");
            }
            sb.append(String.format("%02X", fingerprint[i] & 0xFF));
        }
        
        return sb.toString();
    }
    
    /**
     * Check if a package is installed
     * @param context Application context
     * @param packageName Package name to check
     * @return true if installed, false otherwise
     */
    public static boolean isPackageInstalled(Context context, String packageName) {
        try {
            context.getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Verify download integrity before installation
     * @param context Application context
     * @param apkPath Path to APK file
     * @param expectedPackageName Expected package name
     * @return true if APK is valid and safe to install, false otherwise
     */
    public static boolean verifyDownloadIntegrity(Context context, String apkPath, String expectedPackageName) {
        try {
            // Get package info from APK file
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageArchiveInfo(apkPath, PackageManager.GET_SIGNATURES);
            
            if (packageInfo == null) {
                Log.e(TAG, "Failed to get package info from APK: " + apkPath);
                return false;
            }
            
            // Verify package name matches expected
            if (!expectedPackageName.equals(packageInfo.packageName)) {
                Log.e(TAG, "Package name mismatch. Expected: " + expectedPackageName + ", Got: " + packageInfo.packageName);
                return false;
            }
            
            // Get signature from APK
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                Log.e(TAG, "No signatures found in APK: " + apkPath);
                return false;
            }
            
            String apkSignature = getSignatureFingerprint(packageInfo.signatures[0]);
            boolean isTrusted = TRUSTED_PUBG_SIGNATURES.contains(apkSignature);
            
            Log.d(TAG, "APK signature verification for " + expectedPackageName + ": " + (isTrusted ? "PASSED" : "FAILED"));
            Log.d(TAG, "APK signature: " + apkSignature);
            
            return isTrusted;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to verify download integrity for " + apkPath, e);
            return false;
        }
    }
    
    /**
     * Security check result
     */
    public static class SecurityCheckResult {
        public final boolean isSecure;
        public final String message;
        public final String details;
        
        public SecurityCheckResult(boolean isSecure, String message, String details) {
            this.isSecure = isSecure;
            this.message = message;
            this.details = details;
        }
        
        public SecurityCheckResult(boolean isSecure, String message) {
            this(isSecure, message, null);
        }
    }
    
    /**
     * Perform comprehensive security check
     * @param context Application context
     * @return Security check result
     */
    public static SecurityCheckResult performSecurityCheck(Context context) {
        try {
            // Check app signature
            if (!verifyAppSignature(context)) {
                return new SecurityCheckResult(false, 
                    "App signature verification failed", 
                    "This app may have been tampered with or is not an official release");
            }
            
            // Additional security checks can be added here
            
            return new SecurityCheckResult(true, "Security check passed");
            
        } catch (Exception e) {
            Log.e(TAG, "Security check failed", e);
            return new SecurityCheckResult(false, 
                "Security check failed", 
                "An error occurred during security verification: " + e.getMessage());
        }
    }
}
