<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#0D1117"
    android:padding="16dp"
    tools:context=".ui.test.DownloadTestActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:tint="#58A6FF"
            android:contentDescription="Back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Download Test"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />

    </LinearLayout>

    <!-- Status Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/game_card_bg"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Download Status"
            android:textColor="#58A6FF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Ready to download"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:layout_marginBottom="8dp" />

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:progressTint="#00D084"
            android:progressBackgroundTint="#21262D"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textColor="#8B949E"
            android:textSize="12sp" />

    </LinearLayout>

    <!-- Storage Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/game_card_bg"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Storage Information"
            android:textColor="#58A6FF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_storage_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Loading storage info..."
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- Download Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/game_card_bg"
        android:padding="16dp"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="PUBG Mobile Downloads"
            android:textColor="#58A6FF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_download_global"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Download PUBG Mobile Global"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:background="@drawable/btn_download_bg"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_download_kr"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Download PUBG Mobile KR"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:background="@drawable/btn_download_bg"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_download_tw"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Download PUBG Mobile TW"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:background="@drawable/btn_download_bg"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_download_vn"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Download PUBG Mobile VN"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:background="@drawable/btn_download_bg" />

    </LinearLayout>

    <!-- Utility Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/game_card_bg"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Utilities"
            android:textColor="#58A6FF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_test_storage"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="Test Storage"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:background="@drawable/btn_update_bg"
                android:layout_marginEnd="8dp" />

            <Button
                android:id="@+id/btn_cleanup"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:text="Cleanup Files"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:background="@drawable/btn_update_bg"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
