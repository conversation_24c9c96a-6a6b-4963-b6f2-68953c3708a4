<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.download.DownloadActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        app:elevation="0dp">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/background"
            app:layout_scrollFlags="scroll|enterAlways"
            app:navigationIcon="@drawable/ic_back"
            app:title="@string/download_title"
            app:titleTextAppearance="@style/TextAppearance.Material3.HeadlineMedium"
            app:titleTextColor="@color/text_primary" />

        <!-- Search Bar -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/search_layout"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="8dp"
            android:hint="@string/search_releases"
            app:boxBackgroundColor="@color/surface_container"
            app:boxCornerRadiusBottomEnd="12dp"
            app:boxCornerRadiusBottomStart="12dp"
            app:boxCornerRadiusTopEnd="12dp"
            app:boxCornerRadiusTopStart="12dp"
            app:boxStrokeColor="@color/surface_container_high"
            app:endIconDrawable="@drawable/ic_search"
            app:endIconMode="custom"
            app:layout_scrollFlags="scroll|enterAlways">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_secondary"
                tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

        </com.google.android.material.textfield.TextInputLayout>

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:scrollbars="none">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_filters"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                app:chipSpacing="8dp"
                app:singleLine="true"
                app:singleSelection="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_filter_all"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="@string/all"
                    app:chipBackgroundColor="@color/primary_light"
                    app:chipCornerRadius="8dp"
                    tools:ignore="TouchTargetSizeCheck" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_filter_latest"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/latest"
                    app:chipBackgroundColor="@color/surface_container_high"
                    app:chipCornerRadius="8dp"
                    tools:ignore="TouchTargetSizeCheck" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_sort_date"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sort_by_date"
                    app:chipBackgroundColor="@color/surface_container_high"
                    app:chipCornerRadius="8dp"
                    app:chipIcon="@drawable/ic_sort"
                    tools:ignore="TouchTargetSizeCheck" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_sort_size"
                    style="@style/Widget.Material3.Chip.Filter"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sort_by_size"
                    app:chipBackgroundColor="@color/surface_container_high"
                    app:chipCornerRadius="8dp"
                    app:chipIcon="@drawable/ic_sort"
                    tools:ignore="TouchTargetSizeCheck" />

            </com.google.android.material.chip.ChipGroup>

        </HorizontalScrollView>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.progressindicator.CircularProgressIndicator
                    android:id="@+id/progressLoading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="64dp"
                    android:visibility="gone"
                    app:indicatorColor="@color/primary"
                    app:trackColor="@color/surface_container_high" />

                <LinearLayout
                    android:id="@+id/layout_empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="64dp"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="32dp"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:alpha="0.6"
                        android:src="@drawable/ic_download"
                        app:tint="@color/text_secondary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/no_releases_available"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center"
                        android:text="@string/pull_down_to_refresh_or_check_your_connection"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvReleases"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clipToPadding="false"
                    android:nestedScrollingEnabled="false"
                    android:paddingBottom="16dp"
                    tools:listitem="@layout/item_release" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardDownloadInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/surface_container"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:strokeColor="@color/surface_container_high"
        app:strokeWidth="1dp"
        style="@style/Widget.Material3.CardView.Elevated">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/download_patches"
                android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                android:textColor="@color/text_primary" />

            <!-- Size Information with Icons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_download"
                        app:tint="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvApkSize"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_weight="1"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/text_secondary"
                        tools:text="APK Size: 25.4 MB" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_download"
                        app:tint="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvObbSize"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_weight="1"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="@color/text_secondary"
                        tools:text="OBB Size: 150.2 MB" />

                </LinearLayout>

            </LinearLayout>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardBackgroundColor="@color/primary_light"
                app:cardCornerRadius="12dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:contentDescription="@string/icon_search"
                        android:src="@drawable/ic_search"
                        app:tint="@color/text_secondary" />

                    <TextView
                        android:id="@+id/tvTotalSize"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_weight="1"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="@color/primary"
                        android:textStyle="bold"
                        tools:text="Total Size: 175.6 MB" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <Button
                android:id="@+id/btnDownload"
                style="@style/Widget.BearLoader.Button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="@string/download_patches" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Download Progress Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardDownloadProgress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:cardBackgroundColor="@color/surface_container"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:strokeColor="@color/surface_container_high"
        app:strokeWidth="1dp"
        style="@style/Widget.Material3.CardView.Elevated">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:id="@+id/tv_download_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/downloading"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="@color/text_primary" />

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progressDownload"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                app:indicatorColor="@color/primary"
                app:trackColor="@color/surface_container_high"
                android:indeterminate="false" />

            <TextView
                android:id="@+id/tv_download_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:text="0%"
                android:textAppearance="@style/TextAppearance.Material3.LabelLarge"
                android:textColor="@color/text_secondary" />

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationDownloadComplete"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:layout_marginTop="12dp"
                android:visibility="gone"
                app:lottie_autoPlay="false"
                app:lottie_loop="false"
                app:lottie_fileName="download_complete.json" />

            <Button
                android:id="@+id/btnCancelDownload"
                style="@style/Widget.BearLoader.Button.Secondary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/cancel_download" />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- No Releases TextView (for empty/error state) -->
    <TextView
        android:id="@+id/tvNoReleases"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="32dp"
        android:text="@string/no_releases_available"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="@color/text_secondary"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
