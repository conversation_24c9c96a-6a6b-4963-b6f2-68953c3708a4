{"logs": [{"outputFile": "com.bearmod.loader.app-mergeDebugResources-59:/values-is/values-is.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1817,1943,2067,2142,2223,2296,2365,2448,2530,2595,2675,2728,2789,2839,2900,2959,3029,3092,3154,3218,3278,3344,3409,3479,3531,3591,3665,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1812,1938,2062,2137,2218,2291,2360,2443,2525,2590,2670,2723,2784,2834,2895,2954,3024,3087,3149,3213,3273,3339,3404,3474,3526,3586,3660,3734,3787"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,583,5149,5234,5318,5395,5484,5581,5650,5714,5805,5896,5959,6023,6085,6153,6277,6403,6527,6602,6683,6756,6825,6908,6990,7055,7765,7818,7879,7929,7990,8049,8119,8182,8244,8308,8368,8434,8499,8569,8621,8681,8755,8829", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52", "endOffsets": "378,578,779,5229,5313,5390,5479,5576,5645,5709,5800,5891,5954,6018,6080,6148,6272,6398,6522,6597,6678,6751,6820,6903,6985,7050,7130,7813,7874,7924,7985,8044,8114,8177,8239,8303,8363,8429,8494,8564,8616,8676,8750,8824,8877"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "954,1054,1151,1263,1348,1449,1563,1644,1723,1814,1907,2000,2094,2200,2293,2388,2483,2574,2668,2749,2859,2966,3063,3172,3272,3375,3530,13967", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "1049,1146,1258,1343,1444,1558,1639,1718,1809,1902,1995,2089,2195,2288,2383,2478,2569,2663,2744,2854,2961,3058,3167,3267,3370,3525,3623,14043"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4015,4110,4217,4314,4414,4517,4621,14286", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "4105,4212,4309,4409,4512,4616,4727,14382"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1009,1074,1162,1226,1287,1377,1441,1504,1566,1634,1698,1754,1877,1942,2004,2060,2131,2258,2342,2416,2513,2594,2678,2814,2891,2968,3084,3171,3250,3307,3362,3428,3504,3584,3655,3731,3798,3872,3942,4008,4110,4196,4266,4357,4447,4521,4594,4683,4734,4815,4887,4968,5054,5116,5180,5243,5312,5426,5532,5640,5742,5803,5862,5942,6026,6105", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "265,339,411,490,572,652,749,864,946,1004,1069,1157,1221,1282,1372,1436,1499,1561,1629,1693,1749,1872,1937,1999,2055,2126,2253,2337,2411,2508,2589,2673,2809,2886,2963,3079,3166,3245,3302,3357,3423,3499,3579,3650,3726,3793,3867,3937,4003,4105,4191,4261,4352,4442,4516,4589,4678,4729,4810,4882,4963,5049,5111,5175,5238,5307,5421,5527,5635,5737,5798,5857,5937,6021,6100,6175"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3628,3702,3774,3853,3935,4732,4829,4944,5026,5084,8882,8970,9034,9095,9185,9249,9312,9374,9442,9506,9562,9685,9750,9812,9868,9939,10066,10150,10224,10321,10402,10486,10622,10699,10776,10892,10979,11058,11115,11170,11236,11312,11392,11463,11539,11606,11680,11750,11816,11918,12004,12074,12165,12255,12329,12402,12491,12542,12623,12695,12776,12862,12924,12988,13051,13120,13234,13340,13448,13550,13611,13887,14048,14132,14211", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,73,71,78,81,79,96,114,81,57,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,73,96,80,83,135,76,76,115,86,78,56,54,65,75,79,70,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79,83,78,74", "endOffsets": "949,3697,3769,3848,3930,4010,4824,4939,5021,5079,5144,8965,9029,9090,9180,9244,9307,9369,9437,9501,9557,9680,9745,9807,9863,9934,10061,10145,10219,10316,10397,10481,10617,10694,10771,10887,10974,11053,11110,11165,11231,11307,11387,11458,11534,11601,11675,11745,11811,11913,11999,12069,12160,12250,12324,12397,12486,12537,12618,12690,12771,12857,12919,12983,13046,13115,13229,13335,13443,13545,13606,13665,13962,14127,14206,14281"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7135,7202,7261,7320,7386,7462,7525,7614,7696", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "7197,7256,7315,7381,7457,7520,7609,7691,7760"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,113", "endOffsets": "153,267"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "13670,13773", "endColumns": "102,113", "endOffsets": "13768,13882"}}]}]}