package com.bearmod.targetapp;

import android.content.Context;
import android.util.Log;

/**
 * Target App Integration Helper
 * This class provides easy integration methods for target mod apps
 * Copy this to your target mod project and use it in your Application or MainActivity
 */
public class TargetAppIntegration {
    
    private static final String TAG = "TargetAppIntegration";
    
    /**
     * Simple authentication check for target mod apps
     * Call this in your Application.onCreate() or MainActivity.onCreate()
     * 
     * @param context Application context
     * @return AuthenticationResult with success status and method used
     */
    public static AuthenticationResult performAuthentication(Context context) {
        try {
            Log.d(TAG, "Starting target app authentication");
            
            // Initialize BearTrust
            BearTrust.initialize(context);
            
            // Check if launched by trusted BearMod Loader
            if (SignatureVerifier.isLaunchedByTrustedLoader(context)) {
                Log.d(TAG, "Trusted launch detected - skipping KeyAuth");
                return new AuthenticationResult(true, "BearTrust", true, 
                    "Authenticated via trusted BearMod Loader");
            }
            
            // Fallback to standard authentication
            Log.d(TAG, "Not launched by trusted loader - using standard authentication");
            return new AuthenticationResult(true, "Standard", false, 
                "Standard authentication required");
            
        } catch (Exception e) {
            Log.e(TAG, "Authentication error", e);
            return new AuthenticationResult(false, "Error", false, 
                "Authentication error: " + e.getMessage());
        }
    }
    
    /**
     * Check if app should skip KeyAuth authentication
     * 
     * @param context Application context
     * @return true if KeyAuth can be skipped, false otherwise
     */
    public static boolean canSkipKeyAuth(Context context) {
        try {
            return SignatureVerifier.isLaunchedByTrustedLoader(context);
        } catch (Exception e) {
            Log.e(TAG, "Error checking KeyAuth skip", e);
            return false;
        }
    }
    
    /**
     * Get signature information for debugging
     * Call this during development to get signature hashes
     * 
     * @param context Application context
     */
    public static void logDebugInformation(Context context) {
        Log.d(TAG, "=== Target App Debug Information ===");
        
        try {
            // Log signature information
            SignatureVerifier.logSignatureInformation(context);
            
            // Log authentication status
            AuthenticationResult result = performAuthentication(context);
            Log.d(TAG, "Authentication result: " + result.toString());
            
        } catch (Exception e) {
            Log.e(TAG, "Error logging debug information", e);
        }
        
        Log.d(TAG, "=== End Target App Debug Information ===");
    }
    
    /**
     * Initialize target app with authentication
     * This is a complete initialization method you can call from your Application.onCreate()
     * 
     * @param context Application context
     * @return true if initialization successful, false otherwise
     */
    public static boolean initializeTargetApp(Context context) {
        try {
            Log.d(TAG, "Initializing target app");
            
            // Perform authentication
            AuthenticationResult authResult = performAuthentication(context);
            
            if (authResult.isAuthenticated) {
                Log.d(TAG, "Target app initialized successfully: " + authResult.method);
                
                if (authResult.isTrusted) {
                    Log.d(TAG, "Running in trusted mode - enhanced features available");
                } else {
                    Log.d(TAG, "Running in standard mode");
                }
                
                return true;
            } else {
                Log.e(TAG, "Target app initialization failed: " + authResult.message);
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Target app initialization error", e);
            return false;
        }
    }
    
    /**
     * Cleanup target app resources
     * Call this in your Application.onTerminate() or when app is closing
     */
    public static void cleanup() {
        try {
            BearTrust.cleanup();
            Log.d(TAG, "Target app cleanup completed");
        } catch (Exception e) {
            Log.e(TAG, "Error during target app cleanup", e);
        }
    }
    
    /**
     * Authentication result class
     */
    public static class AuthenticationResult {
        public final boolean isAuthenticated;
        public final String method;
        public final boolean isTrusted;
        public final String message;
        
        public AuthenticationResult(boolean isAuthenticated, String method, boolean isTrusted, String message) {
            this.isAuthenticated = isAuthenticated;
            this.method = method;
            this.isTrusted = isTrusted;
            this.message = message;
        }
        
        @Override
        public String toString() {
            return "AuthenticationResult{" +
                    "isAuthenticated=" + isAuthenticated +
                    ", method='" + method + '\'' +
                    ", isTrusted=" + isTrusted +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
}

/*
USAGE EXAMPLE FOR TARGET MOD APPS:

// In your target mod's Application class:
public class YourModApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        
        // Initialize target app with BearTrust
        if (TargetAppIntegration.initializeTargetApp(this)) {
            // Check if we can skip KeyAuth
            if (TargetAppIntegration.canSkipKeyAuth(this)) {
                Log.d("YourMod", "Skipping KeyAuth - launched by trusted loader");
                // Start your mod directly
                startModFunctionality(true); // true = trusted mode
            } else {
                Log.d("YourMod", "Using KeyAuth authentication");
                // Perform KeyAuth authentication
                performKeyAuthThenStartMod();
            }
        } else {
            Log.e("YourMod", "Authentication failed - exiting");
            System.exit(1);
        }
    }
    
    @Override
    public void onTerminate() {
        super.onTerminate();
        TargetAppIntegration.cleanup();
    }
    
    private void startModFunctionality(boolean isTrusted) {
        // Your mod implementation
    }
    
    private void performKeyAuthThenStartMod() {
        // Your KeyAuth implementation
        // After successful KeyAuth, call startModFunctionality(false)
    }
}

// In your target mod's MainActivity:
public class MainActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Simple signature check
        if (!SignatureVerifier.isSignatureValid(this)) {
            Log.e("MainActivity", "Signature verification failed");
            finish();
            return;
        }
        
        // Continue with normal activity
        setContentView(R.layout.activity_main);
    }
}

// For debugging during development:
// TargetAppIntegration.logDebugInformation(this);
*/
