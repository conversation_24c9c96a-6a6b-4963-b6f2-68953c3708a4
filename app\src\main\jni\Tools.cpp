#include <jni.h>
#include <android/log.h>
#include <string>
#include <vector>

#define LOG_TAG "BearTools"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

/**
 * Utility functions for BearMod
 */

/**
 * Convert string to hex
 */
std::string stringToHex(const std::string& input) {
    std::string result;
    char buf[3];
    for (unsigned char c : input) {
        sprintf(buf, "%02x", c);
        result += buf;
    }
    return result;
}

/**
 * Convert hex to string
 */
std::string hexToString(const std::string& hex) {
    std::string result;
    for (size_t i = 0; i < hex.length(); i += 2) {
        std::string byteString = hex.substr(i, 2);
        char byte = (char) strtol(byteString.c_str(), nullptr, 16);
        result += byte;
    }
    return result;
}

/**
 * Simple string obfuscation
 */
std::string obfuscateString(const std::string& input, int key) {
    std::string result = input;
    for (char& c : result) {
        c ^= key;
    }
    return result;
}

/**
 * JNI exports for utility functions
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_Tools_stringToHex(JNIEnv* env, jobject thiz, jstring input) {
    const char* inputStr = env->GetStringUTFChars(input, nullptr);
    std::string result = stringToHex(std::string(inputStr));
    env->ReleaseStringUTFChars(input, inputStr);
    return env->NewStringUTF(result.c_str());
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_Tools_hexToString(JNIEnv* env, jobject thiz, jstring hex) {
    const char* hexStr = env->GetStringUTFChars(hex, nullptr);
    std::string result = hexToString(std::string(hexStr));
    env->ReleaseStringUTFChars(hex, hexStr);
    return env->NewStringUTF(result.c_str());
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_Tools_obfuscateString(JNIEnv* env, jobject thiz, jstring input, jint key) {
    const char* inputStr = env->GetStringUTFChars(input, nullptr);
    std::string result = obfuscateString(std::string(inputStr), key);
    env->ReleaseStringUTFChars(input, inputStr);
    return env->NewStringUTF(result.c_str());
}
