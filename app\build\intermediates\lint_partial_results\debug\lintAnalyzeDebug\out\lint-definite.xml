<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="WrongViewCast"
        severity="error"
        message="Unexpected implicit cast to `ShapeableImageView`: layout tag was `ImageView`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="34"
            column="9"
            startOffset="960"
            endLine="34"
            endColumn="49"
            endOffset="1000"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="782"
            column="26"
            startOffset="31795"
            endLine="782"
            endColumn="37"
            endOffset="31806"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="1019"
            column="26"
            startOffset="41611"
            endLine="1019"
            endColumn="37"
            endOffset="41622"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="1064"
            column="60"
            startOffset="43309"
            endLine="1064"
            endColumn="71"
            endOffset="43320"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="143"
            column="35"
            startOffset="5513"
            endLine="144"
            endColumn="53"
            endOffset="5603"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="147"
            column="33"
            startOffset="5683"
            endLine="147"
            endColumn="73"
            endOffset="5723"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="150"
            column="37"
            startOffset="5834"
            endLine="150"
            endColumn="93"
            endOffset="5890"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="174"
            column="26"
            startOffset="6620"
            endLine="188"
            endColumn="10"
            endOffset="7174"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="226"
            column="30"
            startOffset="8484"
            endLine="227"
            endColumn="76"
            endOffset="8627"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="662"
            column="27"
            startOffset="26718"
            endLine="662"
            endColumn="38"
            endOffset="26729"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="686"
            column="39"
            startOffset="27580"
            endLine="686"
            endColumn="50"
            endOffset="27591"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/StorageManager.java"
            line="216"
            column="20"
            startOffset="7860"
            endLine="217"
            endColumn="78"
            endOffset="8014"/>
    </incident>

    <incident
        id="MissingDefaultResource"
        severity="fatal"
        message="The color &quot;card_bg&quot; in values-night has no declaration in the base `values` folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier">
        <fix-replace
            description="Remove resource override"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
                startOffset="149"
                endOffset="186"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="5"
            column="12"
            startOffset="156"
            endLine="5"
            endColumn="26"
            endOffset="170"/>
    </incident>

    <incident
        id="MissingDefaultResource"
        severity="fatal"
        message="The color &quot;surface_container_lowest&quot; in values-night has no declaration in the base `values` folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier">
        <fix-replace
            description="Remove resource override"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
                startOffset="389"
                endOffset="443"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="11"
            column="12"
            startOffset="396"
            endLine="11"
            endColumn="43"
            endOffset="427"/>
    </incident>

    <incident
        id="MissingDefaultResource"
        severity="fatal"
        message="The color &quot;surface_container_highest&quot; in values-night has no declaration in the base `values` folder; this can lead to crashes when the resource is queried in a configuration that does not match this qualifier">
        <fix-replace
            description="Remove resource override"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
                startOffset="507"
                endOffset="562"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="13"
            column="12"
            startOffset="514"
            endLine="13"
            endColumn="44"
            endOffset="546"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="74"
            column="98"
            startOffset="2395"
            endLine="74"
            endColumn="102"
            endOffset="2399"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-alpha07">
        <fix-replace
            description="Change to 1.1.0-alpha07"
            family="Update versions"
            oldString="1.1.0-alpha06"
            replacement="1.1.0-alpha07"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="107"
            column="20"
            startOffset="2636"
            endLine="107"
            endColumn="69"
            endOffset="2685"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;#58A6FF&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="#58A6FF"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="25"
            column="13"
            startOffset="917"
            endLine="25"
            endColumn="35"
            endOffset="939"/>
    </incident>

    <incident
        id="PluralsCandidate"
        severity="warning"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="5"
            startOffset="891"
            endLine="19"
            endColumn="63"
            endOffset="949"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="136"
            column="30"
            startOffset="5339"
            endLine="136"
            endColumn="147"
            endOffset="5456"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `SERIAL` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="139"
            column="43"
            startOffset="5539"
            endLine="139"
            endColumn="49"
            endOffset="5545"/>
    </incident>

    <incident
        id="InsecureBaseConfiguration"
        severity="warning"
        message="Insecure Base Configuration">
        <fix-replace
            description="Replace with false"
            replacement="false"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
                startOffset="500"
                endOffset="504"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="11"
            column="45"
            startOffset="500"
            endLine="11"
            endColumn="49"
            endOffset="504"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="50"
            column="9"
            startOffset="1647"
            endLine="50"
            endColumn="31"
            endOffset="1669"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
            line="56"
            column="17"
            startOffset="1935"
            endLine="56"
            endColumn="63"
            endOffset="1981"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DownloadManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="39"
            column="13"
            startOffset="1043"
            endLine="39"
            endColumn="19"
            endOffset="1049"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="232"
            column="18"
            startOffset="10513"
            endLine="232"
            endColumn="30"
            endOffset="10525"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="256"
            column="18"
            startOffset="11574"
            endLine="256"
            endColumn="30"
            endOffset="11586"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="291"
            column="18"
            startOffset="13081"
            endLine="291"
            endColumn="30"
            endOffset="13093"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="161"
            column="14"
            startOffset="7019"
            endLine="161"
            endColumn="26"
            endOffset="7031"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_settings.xml"
            line="9"
            column="27"
            startOffset="301"
            endLine="9"
            endColumn="931"
            endOffset="1205"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_settings.xml"
            line="10"
            column="27"
            startOffset="334"
            endLine="10"
            endColumn="931"
            endOffset="1238"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="27"
            startOffset="293"
            endLine="9"
            endColumn="931"
            endOffset="1197"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="7"
            column="5"
            startOffset="343"
            endLine="7"
            endColumn="43"
            endOffset="381"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#0D1117` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="7"
            column="5"
            startOffset="283"
            endLine="7"
            endColumn="33"
            endOffset="311"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#0D1117` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_fresh_download.xml"
            line="7"
            column="5"
            startOffset="283"
            endLine="7"
            endColumn="33"
            endOffset="311"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_tap_unlock.xml"
            line="6"
            column="5"
            startOffset="255"
            endLine="6"
            endColumn="38"
            endOffset="288"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="5"
            column="5"
            startOffset="190"
            endLine="5"
            endColumn="49"
            endOffset="234"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
                    startOffset="6734"
                    endOffset="7797"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
                    startOffset="6904"
                    endOffset="7332"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
                    startOffset="7346"
                    endOffset="7772"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="199"
            column="14"
            startOffset="6905"
            endLine="199"
            endColumn="20"
            endOffset="6911"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
                    startOffset="6734"
                    endOffset="7797"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
                    startOffset="6904"
                    endOffset="7332"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
                    startOffset="7346"
                    endOffset="7772"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="210"
            column="14"
            startOffset="7347"
            endLine="210"
            endColumn="20"
            endOffset="7353"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-security-security-crypto3"
            robot="true">
            <fix-replace
                description="Replace with androidxSecuritySecurityCrypto = &quot;1.1.0-alpha06&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxSecuritySecurityCrypto = &quot;1.1.0-alpha06&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-security-security-crypto3 = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;androidxSecuritySecurityCrypto&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-security-security-crypto3 = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;androidxSecuritySecurityCrypto&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="867"
                    endOffset="867"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.security.security.crypto3"
                robot="true"
                replacement="libs.androidx.security.security.crypto3"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2636"
                    endOffset="2685"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="107"
            column="20"
            startOffset="2636"
            endLine="107"
            endColumn="69"
            endOffset="2685"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``ShapeableImageView`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="50"
            column="9"
            startOffset="1543"
            endLine="56"
            endColumn="11"
            endOffset="1776"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="50"
            column="38"
            startOffset="1572"
            endLine="56"
            endColumn="10"
            endOffset="1775"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="157"
            column="22"
            startOffset="7244"
            endLine="157"
            endColumn="31"
            endOffset="7253"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="238"
            column="22"
            startOffset="10769"
            endLine="238"
            endColumn="31"
            endOffset="10778"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="263"
            column="22"
            startOffset="11881"
            endLine="263"
            endColumn="31"
            endOffset="11890"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="63"
            column="34"
            startOffset="2252"
            endLine="63"
            endColumn="54"
            endOffset="2272"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="114"
            column="43"
            startOffset="4414"
            endLine="114"
            endColumn="52"
            endOffset="4423"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="129"
            column="57"
            startOffset="5143"
            endLine="129"
            endColumn="78"
            endOffset="5164"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="132"
            column="54"
            startOffset="5298"
            endLine="132"
            endColumn="103"
            endOffset="5347"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="132"
            column="96"
            startOffset="5340"
            endLine="132"
            endColumn="103"
            endOffset="5347"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="136"
            column="56"
            startOffset="5534"
            endLine="136"
            endColumn="106"
            endOffset="5584"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="138"
            column="56"
            startOffset="5702"
            endLine="138"
            endColumn="79"
            endOffset="5725"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="124"
            column="25"
            startOffset="3913"
            endLine="124"
            endColumn="54"
            endOffset="3942"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="124"
            column="25"
            startOffset="3913"
            endLine="124"
            endColumn="39"
            endOffset="3927"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="127"
            column="25"
            startOffset="4041"
            endLine="127"
            endColumn="33"
            endOffset="4049"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="128"
            column="23"
            startOffset="4074"
            endLine="128"
            endColumn="39"
            endOffset="4090"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="129"
            column="28"
            startOffset="4120"
            endLine="129"
            endColumn="38"
            endOffset="4130"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="149"
            column="28"
            startOffset="4812"
            endLine="149"
            endColumn="42"
            endOffset="4826"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="153"
            column="29"
            startOffset="4919"
            endLine="153"
            endColumn="71"
            endOffset="4961"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="153"
            column="64"
            startOffset="4954"
            endLine="153"
            endColumn="71"
            endOffset="4961"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="155"
            column="29"
            startOffset="5009"
            endLine="155"
            endColumn="37"
            endOffset="5017"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="161"
            column="31"
            startOffset="5173"
            endLine="161"
            endColumn="67"
            endOffset="5209"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="163"
            column="31"
            startOffset="5263"
            endLine="163"
            endColumn="47"
            endOffset="5279"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="170"
            column="28"
            startOffset="5420"
            endLine="171"
            endColumn="69"
            endOffset="5535"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="171"
            column="64"
            startOffset="5530"
            endLine="171"
            endColumn="69"
            endOffset="5535"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="181"
            column="29"
            startOffset="5809"
            endLine="181"
            endColumn="48"
            endOffset="5828"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="182"
            column="32"
            startOffset="5862"
            endLine="182"
            endColumn="38"
            endOffset="5868"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="184"
            column="29"
            startOffset="5941"
            endLine="184"
            endColumn="39"
            endOffset="5951"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="185"
            column="27"
            startOffset="5980"
            endLine="185"
            endColumn="33"
            endOffset="5986"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="186"
            column="31"
            startOffset="6019"
            endLine="186"
            endColumn="38"
            endOffset="6026"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="188"
            column="29"
            startOffset="6074"
            endLine="188"
            endColumn="46"
            endOffset="6091"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="189"
            column="32"
            startOffset="6125"
            endLine="189"
            endColumn="40"
            endOffset="6133"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="190"
            column="29"
            startOffset="6164"
            endLine="190"
            endColumn="36"
            endOffset="6171"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="192"
            column="31"
            startOffset="6240"
            endLine="192"
            endColumn="38"
            endOffset="6247"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="70"
            column="26"
            startOffset="2387"
            endLine="70"
            endColumn="45"
            endOffset="2406"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="98"
            column="26"
            startOffset="3474"
            endLine="98"
            endColumn="69"
            endOffset="3517"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="98"
            column="26"
            startOffset="3474"
            endLine="98"
            endColumn="50"
            endOffset="3498"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="108"
            column="42"
            startOffset="3985"
            endLine="108"
            endColumn="75"
            endOffset="4018"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="108"
            column="42"
            startOffset="3985"
            endLine="108"
            endColumn="64"
            endOffset="4007"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="109"
            column="44"
            startOffset="4064"
            endLine="109"
            endColumn="50"
            endOffset="4070"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="125"
            column="42"
            startOffset="4748"
            endLine="125"
            endColumn="69"
            endOffset="4775"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="125"
            column="42"
            startOffset="4748"
            endLine="125"
            endColumn="61"
            endOffset="4767"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="126"
            column="44"
            startOffset="4821"
            endLine="126"
            endColumn="52"
            endOffset="4829"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="190"
            column="26"
            startOffset="7210"
            endLine="190"
            endColumn="50"
            endOffset="7234"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="204"
            column="26"
            startOffset="7611"
            endLine="204"
            endColumn="52"
            endOffset="7637"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="211"
            column="38"
            startOffset="7884"
            endLine="211"
            endColumn="70"
            endOffset="7916"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/test/DownloadTestActivity.java"
            line="214"
            column="38"
            startOffset="8071"
            endLine="214"
            endColumn="77"
            endOffset="8110"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="375"
            column="17"
            startOffset="16661"
            endLine="375"
            endColumn="34"
            endOffset="16678"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Back&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="26"
            column="13"
            startOffset="952"
            endLine="26"
            endColumn="46"
            endOffset="985"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Download Test&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="32"
            column="13"
            startOffset="1146"
            endLine="32"
            endColumn="41"
            endOffset="1174"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Download Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="56"
            column="13"
            startOffset="1878"
            endLine="56"
            endColumn="43"
            endOffset="1908"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Ready to download&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="66"
            column="13"
            startOffset="2239"
            endLine="66"
            endColumn="45"
            endOffset="2271"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="84"
            column="13"
            startOffset="2926"
            endLine="84"
            endColumn="30"
            endOffset="2943"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Storage Information&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="102"
            column="13"
            startOffset="3472"
            endLine="102"
            endColumn="47"
            endOffset="3506"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Loading storage info...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="112"
            column="13"
            startOffset="3843"
            endLine="112"
            endColumn="51"
            endOffset="3881"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;PUBG Mobile Downloads&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="130"
            column="13"
            startOffset="4414"
            endLine="130"
            endColumn="49"
            endOffset="4450"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Download PUBG Mobile Global&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="140"
            column="13"
            startOffset="4782"
            endLine="140"
            endColumn="55"
            endOffset="4824"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Download PUBG Mobile KR&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="150"
            column="13"
            startOffset="5174"
            endLine="150"
            endColumn="51"
            endOffset="5212"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Download PUBG Mobile TW&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="160"
            column="13"
            startOffset="5562"
            endLine="160"
            endColumn="51"
            endOffset="5600"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Download PUBG Mobile VN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="170"
            column="13"
            startOffset="5950"
            endLine="170"
            endColumn="51"
            endOffset="5988"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Utilities&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="188"
            column="13"
            startOffset="6537"
            endLine="188"
            endColumn="37"
            endOffset="6561"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Test Storage&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="204"
            column="17"
            startOffset="7109"
            endLine="204"
            endColumn="44"
            endOffset="7136"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cleanup Files&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_test.xml"
            line="215"
            column="17"
            startOffset="7546"
            endLine="215"
            endColumn="45"
            endOffset="7574"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;•&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="74"
            column="13"
            startOffset="3463"
            endLine="74"
            endColumn="29"
            endOffset="3479"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="129"
            column="21"
            startOffset="6026"
            endLine="129"
            endColumn="38"
            endOffset="6043"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 MB/s&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="138"
            column="21"
            startOffset="6473"
            endLine="138"
            endColumn="42"
            endOffset="6494"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;--&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="147"
            column="21"
            startOffset="6922"
            endLine="147"
            endColumn="38"
            endOffset="6939"/>
    </incident>

</incidents>
