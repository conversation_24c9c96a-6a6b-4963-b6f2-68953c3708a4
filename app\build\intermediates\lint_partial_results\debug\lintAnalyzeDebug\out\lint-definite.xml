<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="WrongViewCast"
        severity="error"
        message="Unexpected implicit cast to `ShapeableImageView`: layout tag was `ImageView`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="34"
            column="9"
            startOffset="960"
            endLine="34"
            endColumn="49"
            endOffset="1000"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="562"
            column="60"
            startOffset="21968"
            endLine="562"
            endColumn="71"
            endOffset="21979"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="662"
            column="27"
            startOffset="26718"
            endLine="662"
            endColumn="38"
            endOffset="26729"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="686"
            column="39"
            startOffset="27580"
            endLine="686"
            endColumn="50"
            endOffset="27591"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="74"
            column="98"
            startOffset="2390"
            endLine="74"
            endColumn="102"
            endOffset="2394"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-alpha07">
        <fix-replace
            description="Change to 1.1.0-alpha07"
            family="Update versions"
            oldString="1.1.0-alpha06"
            replacement="1.1.0-alpha07"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="110"
            column="20"
            startOffset="2719"
            endLine="110"
            endColumn="69"
            endOffset="2768"/>
    </incident>

    <incident
        id="PluralsCandidate"
        severity="warning"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="5"
            startOffset="891"
            endLine="19"
            endColumn="63"
            endOffset="949"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="136"
            column="30"
            startOffset="5339"
            endLine="136"
            endColumn="147"
            endOffset="5456"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `SERIAL` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="139"
            column="43"
            startOffset="5539"
            endLine="139"
            endColumn="49"
            endOffset="5545"/>
    </incident>

    <incident
        id="InsecureBaseConfiguration"
        severity="warning"
        message="Insecure Base Configuration">
        <fix-replace
            description="Replace with false"
            replacement="false"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
                startOffset="500"
                endOffset="504"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="11"
            column="45"
            startOffset="500"
            endLine="11"
            endColumn="49"
            endOffset="504"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="50"
            column="9"
            startOffset="1647"
            endLine="50"
            endColumn="31"
            endOffset="1669"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
            line="56"
            column="17"
            startOffset="1935"
            endLine="56"
            endColumn="63"
            endOffset="1981"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DockerManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/DockerManager.java"
            line="16"
            column="13"
            startOffset="330"
            endLine="16"
            endColumn="19"
            endOffset="336"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DownloadManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="38"
            column="13"
            startOffset="995"
            endLine="38"
            endColumn="19"
            endOffset="1001"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `FridaManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/FridaManager.java"
            line="16"
            column="13"
            startOffset="317"
            endLine="16"
            endColumn="19"
            endOffset="323"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="232"
            column="18"
            startOffset="10503"
            endLine="232"
            endColumn="30"
            endOffset="10515"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="256"
            column="18"
            startOffset="11564"
            endLine="256"
            endColumn="30"
            endOffset="11576"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="291"
            column="18"
            startOffset="13071"
            endLine="291"
            endColumn="30"
            endOffset="13083"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_settings.xml"
            line="9"
            column="27"
            startOffset="301"
            endLine="9"
            endColumn="931"
            endOffset="1205"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_settings.xml"
            line="10"
            column="27"
            startOffset="334"
            endLine="10"
            endColumn="931"
            endOffset="1238"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="27"
            startOffset="293"
            endLine="9"
            endColumn="931"
            endOffset="1197"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="7"
            column="5"
            startOffset="343"
            endLine="7"
            endColumn="43"
            endOffset="381"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#0D1117` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_fresh_download.xml"
            line="7"
            column="5"
            startOffset="283"
            endLine="7"
            endColumn="33"
            endOffset="311"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_patch_execution.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_tap_unlock.xml"
            line="6"
            column="5"
            startOffset="255"
            endLine="6"
            endColumn="38"
            endOffset="288"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="5"
            column="5"
            startOffset="190"
            endLine="5"
            endColumn="49"
            endOffset="234"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_gl.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_gl.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_kr.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_kr.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_tw.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_tw.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_vn.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_vn.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-security-crypto"
            robot="true">
            <fix-replace
                description="Replace with securityCryptoVersion = &quot;1.1.0-alpha06&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="securityCryptoVersion = &quot;1.1.0-alpha06&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="541"
                    endOffset="541"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-security-crypto = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;securityCryptoVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-security-crypto = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;securityCryptoVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="867"
                    endOffset="867"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.security.crypto"
                robot="true"
                replacement="libs.androidx.security.crypto"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2719"
                    endOffset="2768"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="110"
            column="20"
            startOffset="2719"
            endLine="110"
            endColumn="69"
            endOffset="2768"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``ShapeableImageView`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="50"
            column="9"
            startOffset="1543"
            endLine="56"
            endColumn="11"
            endOffset="1776"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="50"
            column="38"
            startOffset="1572"
            endLine="56"
            endColumn="10"
            endOffset="1775"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="157"
            column="22"
            startOffset="7234"
            endLine="157"
            endColumn="31"
            endOffset="7243"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="238"
            column="22"
            startOffset="10759"
            endLine="238"
            endColumn="31"
            endOffset="10768"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="263"
            column="22"
            startOffset="11871"
            endLine="263"
            endColumn="31"
            endOffset="11880"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-v26/item_patch_release.xml"
            line="17"
            column="10"
            startOffset="640"
            endLine="17"
            endColumn="19"
            endOffset="649"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="63"
            column="34"
            startOffset="2252"
            endLine="63"
            endColumn="54"
            endOffset="2272"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="114"
            column="43"
            startOffset="4414"
            endLine="114"
            endColumn="52"
            endOffset="4423"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="129"
            column="57"
            startOffset="5143"
            endLine="129"
            endColumn="78"
            endOffset="5164"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="132"
            column="54"
            startOffset="5298"
            endLine="132"
            endColumn="103"
            endOffset="5347"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="132"
            column="96"
            startOffset="5340"
            endLine="132"
            endColumn="103"
            endOffset="5347"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="136"
            column="56"
            startOffset="5534"
            endLine="136"
            endColumn="106"
            endOffset="5584"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="138"
            column="56"
            startOffset="5702"
            endLine="138"
            endColumn="79"
            endOffset="5725"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="124"
            column="25"
            startOffset="3908"
            endLine="124"
            endColumn="54"
            endOffset="3937"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="124"
            column="25"
            startOffset="3908"
            endLine="124"
            endColumn="39"
            endOffset="3922"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="127"
            column="25"
            startOffset="4036"
            endLine="127"
            endColumn="33"
            endOffset="4044"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="128"
            column="23"
            startOffset="4069"
            endLine="128"
            endColumn="39"
            endOffset="4085"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="129"
            column="28"
            startOffset="4115"
            endLine="129"
            endColumn="38"
            endOffset="4125"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="149"
            column="28"
            startOffset="4807"
            endLine="149"
            endColumn="42"
            endOffset="4821"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="153"
            column="29"
            startOffset="4914"
            endLine="153"
            endColumn="71"
            endOffset="4956"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="153"
            column="64"
            startOffset="4949"
            endLine="153"
            endColumn="71"
            endOffset="4956"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="155"
            column="29"
            startOffset="5004"
            endLine="155"
            endColumn="37"
            endOffset="5012"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="161"
            column="31"
            startOffset="5168"
            endLine="161"
            endColumn="67"
            endOffset="5204"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="163"
            column="31"
            startOffset="5258"
            endLine="163"
            endColumn="47"
            endOffset="5274"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="170"
            column="28"
            startOffset="5415"
            endLine="171"
            endColumn="69"
            endOffset="5530"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="171"
            column="64"
            startOffset="5525"
            endLine="171"
            endColumn="69"
            endOffset="5530"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="181"
            column="29"
            startOffset="5804"
            endLine="181"
            endColumn="48"
            endOffset="5823"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="182"
            column="32"
            startOffset="5857"
            endLine="182"
            endColumn="38"
            endOffset="5863"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="184"
            column="29"
            startOffset="5936"
            endLine="184"
            endColumn="39"
            endOffset="5946"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="185"
            column="27"
            startOffset="5975"
            endLine="185"
            endColumn="33"
            endOffset="5981"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="186"
            column="31"
            startOffset="6014"
            endLine="186"
            endColumn="38"
            endOffset="6021"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="188"
            column="29"
            startOffset="6069"
            endLine="188"
            endColumn="46"
            endOffset="6086"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="189"
            column="32"
            startOffset="6120"
            endLine="189"
            endColumn="40"
            endOffset="6128"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="190"
            column="29"
            startOffset="6159"
            endLine="190"
            endColumn="36"
            endOffset="6166"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="192"
            column="31"
            startOffset="6235"
            endLine="192"
            endColumn="38"
            endOffset="6242"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="375"
            column="17"
            startOffset="16649"
            endLine="375"
            endColumn="34"
            endOffset="16666"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;•&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="74"
            column="13"
            startOffset="3463"
            endLine="74"
            endColumn="29"
            endOffset="3479"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="129"
            column="21"
            startOffset="6026"
            endLine="129"
            endColumn="38"
            endOffset="6043"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 MB/s&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="138"
            column="21"
            startOffset="6473"
            endLine="138"
            endColumn="42"
            endOffset="6494"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;--&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="147"
            column="21"
            startOffset="6922"
            endLine="147"
            endColumn="38"
            endOffset="6939"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Game v1.0.5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="112"
            column="17"
            startOffset="4999"
            endLine="112"
            endColumn="43"
            endOffset="5025"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2023-06-15&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="125"
            column="17"
            startOffset="5607"
            endLine="125"
            endColumn="42"
            endOffset="5632"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;25.4 MB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="138"
            column="17"
            startOffset="6214"
            endLine="138"
            endColumn="39"
            endOffset="6236"/>
    </incident>

</incidents>
