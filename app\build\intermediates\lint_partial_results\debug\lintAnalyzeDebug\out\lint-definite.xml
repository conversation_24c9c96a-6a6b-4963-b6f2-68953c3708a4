<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.0" type="incidents">

    <incident
        id="WrongViewCast"
        severity="error"
        message="Unexpected implicit cast to `ShapeableImageView`: layout tag was `ImageView`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="34"
            column="9"
            startOffset="1007"
            endLine="34"
            endColumn="49"
            endOffset="1047"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="559"
            column="67"
            startOffset="22298"
            endLine="559"
            endColumn="78"
            endOffset="22309"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="662"
            column="27"
            startOffset="26718"
            endLine="662"
            endColumn="38"
            endOffset="26729"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="686"
            column="39"
            startOffset="27580"
            endLine="686"
            endColumn="50"
            endOffset="27591"/>
    </incident>

    <incident
        id="NotSibling"
        severity="fatal"
        message="`@+id/bottom_navigation` is not a sibling in the same `ConstraintLayout`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="110"
            column="17"
            startOffset="4850"
            endLine="110"
            endColumn="77"
            endOffset="4910"/>
    </incident>

    <incident
        id="NotSibling"
        severity="fatal"
        message="`@+id/bottom_navigation` is not a sibling in the same `ConstraintLayout`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="133"
            column="17"
            startOffset="5906"
            endLine="133"
            endColumn="77"
            endOffset="5966"/>
    </incident>

    <incident
        id="NotSibling"
        severity="fatal"
        message="`@+id/bottom_navigation` is not a sibling in the same `ConstraintLayout`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="161"
            column="17"
            startOffset="7084"
            endLine="161"
            endColumn="77"
            endOffset="7144"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="58"
            column="98"
            startOffset="1760"
            endLine="58"
            endColumn="102"
            endOffset="1764"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.0 is available: 8.10.1">
        <fix-replace
            description="Change to 8.10.1"
            family="Update versions"
            robot="true"
            independent="true"
            oldString="8.10.0"
            replacement="8.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.library than 8.10.0 is available: 8.10.1">
        <fix-replace
            description="Change to 8.10.1"
            family="Update versions"
            robot="true"
            independent="true"
            oldString="8.10.0"
            replacement="8.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-alpha07">
        <fix-replace
            description="Change to 1.1.0-alpha07"
            family="Update versions"
            oldString="1.1.0-alpha06"
            replacement="1.1.0-alpha07"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="110"
            column="20"
            startOffset="2719"
            endLine="110"
            endColumn="69"
            endOffset="2768"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="54"
            column="13"
            startOffset="2370"
            endLine="54"
            endColumn="52"
            endOffset="2409"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="99"
            column="13"
            startOffset="4067"
            endLine="99"
            endColumn="52"
            endOffset="4106"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="106"
            column="13"
            startOffset="4334"
            endLine="106"
            endColumn="52"
            endOffset="4373"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="303"
            column="25"
            startOffset="13640"
            endLine="303"
            endColumn="61"
            endOffset="13676"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_secondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_secondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="312"
            column="25"
            startOffset="13673"
            endLine="312"
            endColumn="61"
            endOffset="13709"/>
    </incident>

    <incident
        id="PluralsCandidate"
        severity="warning"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="5"
            startOffset="891"
            endLine="19"
            endColumn="63"
            endOffset="949"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `getString` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="136"
            column="30"
            startOffset="5339"
            endLine="136"
            endColumn="147"
            endOffset="5456"/>
    </incident>

    <incident
        id="HardwareIds"
        severity="warning"
        message="Using `SERIAL` to get device identifiers is not recommended">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="139"
            column="43"
            startOffset="5539"
            endLine="139"
            endColumn="49"
            endOffset="5545"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="36"
            column="9"
            startOffset="1173"
            endLine="36"
            endColumn="31"
            endOffset="1195"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/utils/ApkInstaller.java"
            line="56"
            column="17"
            startOffset="1935"
            endLine="56"
            endColumn="63"
            endOffset="1981"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `CloudSyncManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/cloud/CloudSyncManager.java"
            line="24"
            column="13"
            startOffset="571"
            endLine="24"
            endColumn="19"
            endOffset="577"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DockerManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/DockerManager.java"
            line="16"
            column="13"
            startOffset="330"
            endLine="16"
            endColumn="19"
            endOffset="336"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `DownloadManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/download/DownloadManager.java"
            line="40"
            column="13"
            startOffset="1091"
            endLine="40"
            endColumn="19"
            endOffset="1097"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `FridaManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/FridaManager.java"
            line="16"
            column="13"
            startOffset="317"
            endLine="16"
            endColumn="19"
            endOffset="323"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `PatchManager` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/patch/PatchManager.java"
            line="20"
            column="13"
            startOffset="467"
            endLine="20"
            endColumn="19"
            endOffset="473"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="232"
            column="18"
            startOffset="10503"
            endLine="232"
            endColumn="30"
            endOffset="10515"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="256"
            column="18"
            startOffset="11564"
            endLine="256"
            endColumn="30"
            endOffset="11576"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="291"
            column="18"
            startOffset="13071"
            endLine="291"
            endColumn="30"
            endOffset="13083"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="240"
            column="18"
            startOffset="10531"
            endLine="240"
            endColumn="30"
            endOffset="10543"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="264"
            column="18"
            startOffset="11594"
            endLine="264"
            endColumn="30"
            endOffset="11606"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="300"
            column="18"
            startOffset="13104"
            endLine="300"
            endColumn="30"
            endOffset="13116"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_nav_settings.xml"
            line="9"
            column="27"
            startOffset="301"
            endLine="9"
            endColumn="931"
            endOffset="1205"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml"
            line="9"
            column="27"
            startOffset="293"
            endLine="9"
            endColumn="931"
            endOffset="1197"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="7"
            column="5"
            startOffset="343"
            endLine="7"
            endColumn="43"
            endOffset="381"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="7"
            column="5"
            startOffset="343"
            endLine="7"
            endColumn="43"
            endOffset="381"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_patch_execution.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="43"
            endOffset="379"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_tap_unlock.xml"
            line="5"
            column="5"
            startOffset="203"
            endLine="5"
            endColumn="38"
            endOffset="236"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="5"
            column="5"
            startOffset="190"
            endLine="5"
            endColumn="49"
            endOffset="234"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_gl.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_gl.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_kr.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_kr.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_tw.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_tw.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_pubg_vn.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pubg_vn.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/logo.png"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-security-security-crypto4"
            robot="true">
            <fix-replace
                description="Replace with androidxSecuritySecurityCrypto2 = &quot;1.1.0-alpha06&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidxSecuritySecurityCrypto2 = &quot;1.1.0-alpha06&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-security-security-crypto4 = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;androidxSecuritySecurityCrypto2&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-security-security-crypto4 = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;androidxSecuritySecurityCrypto2&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="867"
                    endOffset="867"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.security.security.crypto4"
                robot="true"
                replacement="libs.androidx.security.security.crypto4"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2719"
                    endOffset="2768"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="110"
            column="20"
            startOffset="2719"
            endLine="110"
            endColumn="69"
            endOffset="2768"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``ShapeableImageView`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="50"
            column="9"
            startOffset="1590"
            endLine="56"
            endColumn="11"
            endOffset="1823"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/auth/TapToUnlockActivity.java"
            line="50"
            column="38"
            startOffset="1619"
            endLine="56"
            endColumn="10"
            endOffset="1822"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="157"
            column="22"
            startOffset="7234"
            endLine="157"
            endColumn="31"
            endOffset="7243"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="238"
            column="22"
            startOffset="10759"
            endLine="238"
            endColumn="31"
            endOffset="10768"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="263"
            column="22"
            startOffset="11871"
            endLine="263"
            endColumn="31"
            endOffset="11880"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="162"
            column="22"
            startOffset="7240"
            endLine="162"
            endColumn="31"
            endOffset="7249"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="246"
            column="22"
            startOffset="10787"
            endLine="246"
            endColumn="31"
            endOffset="10796"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_modern.xml"
            line="271"
            column="22"
            startOffset="11901"
            endLine="271"
            endColumn="31"
            endOffset="11910"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="31"
            column="18"
            startOffset="1239"
            endLine="31"
            endColumn="27"
            endOffset="1248"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout-v26/item_patch_release.xml"
            line="17"
            column="10"
            startOffset="640"
            endLine="17"
            endColumn="19"
            endOffset="649"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="49"
            column="34"
            startOffset="1778"
            endLine="49"
            endColumn="54"
            endOffset="1798"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/main/AppVersionAdapter.java"
            line="96"
            column="43"
            startOffset="3771"
            endLine="96"
            endColumn="52"
            endOffset="3780"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="35"
            column="27"
            startOffset="1037"
            endLine="35"
            endColumn="53"
            endOffset="1063"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="42"
            column="33"
            startOffset="1298"
            endLine="42"
            endColumn="148"
            endOffset="1413"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="48"
            column="32"
            startOffset="1635"
            endLine="48"
            endColumn="50"
            endOffset="1653"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="54"
            column="38"
            startOffset="1883"
            endLine="54"
            endColumn="109"
            endOffset="1954"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="60"
            column="36"
            startOffset="2183"
            endLine="60"
            endColumn="56"
            endOffset="2203"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="66"
            column="38"
            startOffset="2462"
            endLine="66"
            endColumn="61"
            endOffset="2485"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadActivity.java"
            line="72"
            column="32"
            startOffset="2729"
            endLine="72"
            endColumn="46"
            endOffset="2743"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="94"
            column="25"
            startOffset="2821"
            endLine="94"
            endColumn="54"
            endOffset="2850"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="94"
            column="25"
            startOffset="2821"
            endLine="94"
            endColumn="39"
            endOffset="2835"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="97"
            column="25"
            startOffset="2949"
            endLine="97"
            endColumn="33"
            endOffset="2957"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="98"
            column="23"
            startOffset="2982"
            endLine="98"
            endColumn="39"
            endOffset="2998"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="99"
            column="28"
            startOffset="3028"
            endLine="99"
            endColumn="38"
            endOffset="3038"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="119"
            column="28"
            startOffset="3720"
            endLine="119"
            endColumn="42"
            endOffset="3734"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="123"
            column="29"
            startOffset="3827"
            endLine="123"
            endColumn="71"
            endOffset="3869"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="123"
            column="64"
            startOffset="3862"
            endLine="123"
            endColumn="71"
            endOffset="3869"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="125"
            column="29"
            startOffset="3917"
            endLine="125"
            endColumn="37"
            endOffset="3925"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="131"
            column="31"
            startOffset="4081"
            endLine="131"
            endColumn="77"
            endOffset="4127"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="131"
            column="64"
            startOffset="4114"
            endLine="131"
            endColumn="77"
            endOffset="4127"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="133"
            column="31"
            startOffset="4181"
            endLine="133"
            endColumn="57"
            endOffset="4207"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="133"
            column="44"
            startOffset="4194"
            endLine="133"
            endColumn="57"
            endOffset="4207"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="136"
            column="27"
            startOffset="4267"
            endLine="136"
            endColumn="43"
            endOffset="4283"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="140"
            column="28"
            startOffset="4360"
            endLine="141"
            endColumn="69"
            endOffset="4475"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="141"
            column="64"
            startOffset="4470"
            endLine="141"
            endColumn="69"
            endOffset="4475"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="151"
            column="29"
            startOffset="4749"
            endLine="151"
            endColumn="48"
            endOffset="4768"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="152"
            column="32"
            startOffset="4802"
            endLine="152"
            endColumn="38"
            endOffset="4808"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="154"
            column="29"
            startOffset="4881"
            endLine="154"
            endColumn="39"
            endOffset="4891"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="155"
            column="27"
            startOffset="4920"
            endLine="155"
            endColumn="33"
            endOffset="4926"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="156"
            column="31"
            startOffset="4959"
            endLine="156"
            endColumn="38"
            endOffset="4966"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="158"
            column="29"
            startOffset="5014"
            endLine="158"
            endColumn="46"
            endOffset="5031"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="159"
            column="32"
            startOffset="5065"
            endLine="159"
            endColumn="40"
            endOffset="5073"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="160"
            column="29"
            startOffset="5104"
            endLine="160"
            endColumn="36"
            endOffset="5111"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/DownloadProgressDialog.java"
            line="162"
            column="31"
            startOffset="5180"
            endLine="162"
            endColumn="38"
            endOffset="5187"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/download/ReleaseAdapter.java"
            line="53"
            column="34"
            startOffset="1804"
            endLine="53"
            endColumn="62"
            endOffset="1832"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download.xml"
            line="375"
            column="17"
            startOffset="16653"
            endLine="375"
            endColumn="34"
            endOffset="16670"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;BEAR-MOD&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="45"
            column="17"
            startOffset="1798"
            endLine="45"
            endColumn="40"
            endOffset="1821"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;License Key&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="59"
            column="17"
            startOffset="2468"
            endLine="59"
            endColumn="43"
            endOffset="2494"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Remember Key&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="86"
            column="17"
            startOffset="3768"
            endLine="86"
            endColumn="44"
            endOffset="3795"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Automatic Login&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="95"
            column="17"
            startOffset="4187"
            endLine="95"
            endColumn="47"
            endOffset="4217"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LOGIN&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml"
            line="104"
            column="17"
            startOffset="4587"
            endLine="104"
            endColumn="37"
            endOffset="4607"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;BEAR-MOD&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="42"
            column="9"
            startOffset="1671"
            endLine="42"
            endColumn="32"
            endOffset="1694"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Advanced Mod Loader&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="63"
            column="9"
            startOffset="2468"
            endLine="63"
            endColumn="43"
            endOffset="2502"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Initializing...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_splash.xml"
            line="102"
            column="13"
            startOffset="4013"
            endLine="102"
            endColumn="43"
            endOffset="4043"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Locked Glyph&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_tap_unlock.xml"
            line="13"
            column="9"
            startOffset="472"
            endLine="13"
            endColumn="50"
            endOffset="513"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Tap to unlock&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_tap_unlock.xml"
            line="19"
            column="9"
            startOffset="669"
            endLine="19"
            endColumn="37"
            endOffset="697"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Downloading APK + OBB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="14"
            column="9"
            startOffset="496"
            endLine="14"
            endColumn="45"
            endOffset="532"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;pubg-mobile-gl.apk&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="26"
            column="9"
            startOffset="899"
            endLine="26"
            endColumn="42"
            endOffset="932"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="57"
            column="13"
            startOffset="1990"
            endLine="57"
            endColumn="30"
            endOffset="2007"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 MB/s&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="67"
            column="13"
            startOffset="2335"
            endLine="67"
            endColumn="34"
            endOffset="2356"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0 / 0 MB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="86"
            column="13"
            startOffset="2932"
            endLine="86"
            endColumn="36"
            endOffset="2955"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Calculating...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="95"
            column="13"
            startOffset="3235"
            endLine="95"
            endColumn="42"
            endOffset="3264"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cancel&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_download_progress.xml"
            line="107"
            column="9"
            startOffset="3601"
            endLine="107"
            endColumn="30"
            endOffset="3622"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;•&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_app_version.xml"
            line="74"
            column="13"
            startOffset="3463"
            endLine="74"
            endColumn="29"
            endOffset="3479"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Game v1.0.5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="112"
            column="17"
            startOffset="4999"
            endLine="112"
            endColumn="43"
            endOffset="5025"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2023-06-15&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="125"
            column="17"
            startOffset="5607"
            endLine="125"
            endColumn="42"
            endOffset="5632"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;25.4 MB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_release.xml"
            line="138"
            column="17"
            startOffset="6214"
            endLine="138"
            endColumn="39"
            endOffset="6236"/>
    </incident>

</incidents>
