// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTapUnlockBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView glyphView;

  @NonNull
  public final TextView tapText;

  private ActivityTapUnlockBinding(@NonNull RelativeLayout rootView, @NonNull ImageView glyphView,
      @NonNull TextView tapText) {
    this.rootView = rootView;
    this.glyphView = glyphView;
    this.tapText = tapText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTapUnlockBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTapUnlockBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_tap_unlock, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTapUnlockBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.glyphView;
      ImageView glyphView = ViewBindings.findChildViewById(rootView, id);
      if (glyphView == null) {
        break missingId;
      }

      id = R.id.tapText;
      TextView tapText = ViewBindings.findChildViewById(rootView, id);
      if (tapText == null) {
        break missingId;
      }

      return new ActivityTapUnlockBinding((RelativeLayout) rootView, glyphView, tapText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
