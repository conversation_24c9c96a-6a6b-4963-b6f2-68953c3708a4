@echo off
"D:\\AndroidBuildEnv\\SDK\\ndk\\27.1.12297006\\ndk-build.cmd" ^
  "NDK_PROJECT_PATH=null" ^
  "APP_BUILD_SCRIPT=D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Android.mk" ^
  "NDK_APPLICATION_MK=D:\\Augment_Code\\BearMod-Loader\\app\\src\\main\\jni\\Application.mk" ^
  "APP_ABI=arm64-v8a" ^
  "NDK_ALL_ABIS=arm64-v8a" ^
  "NDK_DEBUG=1" ^
  "APP_PLATFORM=android-30" ^
  "NDK_OUT=D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx\\Debug\\2129i685/obj" ^
  "NDK_LIBS_OUT=D:\\Augment_Code\\BearMod-Loader\\app\\build\\intermediates\\cxx\\Debug\\2129i685/lib" ^
  "APP_SHORT_COMMANDS=false" ^
  "LOCAL_SHORT_COMMANDS=false" ^
  -B ^
  -n
