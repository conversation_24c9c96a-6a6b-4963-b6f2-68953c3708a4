package com.bearmod.loader.utils;

import android.content.Context;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Storage management utility for handling download space verification and file management
 */
public class StorageManager {
    
    private static final String TAG = "StorageManager";
    private static final long SAFETY_BUFFER_MB = 100; // 100MB safety buffer
    
    /**
     * Check if there's enough storage space for download
     * @param requiredSizeMB Required size in MB
     * @return true if enough space available, false otherwise
     */
    public static boolean hasEnoughSpace(double requiredSizeMB) {
        try {
            long availableSpaceMB = getAvailableSpaceMB();
            long requiredSpaceWithBuffer = (long) (requiredSizeMB + SAFETY_BUFFER_MB);
            
            Log.d(TAG, "Available space: " + availableSpaceMB + "MB, Required: " + requiredSpaceWithBuffer + "MB");
            
            return availableSpaceMB >= requiredSpaceWithBuffer;
        } catch (Exception e) {
            Log.e(TAG, "Error checking storage space", e);
            return false;
        }
    }
    
    /**
     * Get available storage space in MB
     * @return Available space in MB
     */
    public static long getAvailableSpaceMB() {
        try {
            File externalStorage = Environment.getExternalStorageDirectory();
            StatFs stat = new StatFs(externalStorage.getPath());
            
            long availableBytes = stat.getAvailableBytes();
            return availableBytes / (1024 * 1024); // Convert to MB
        } catch (Exception e) {
            Log.e(TAG, "Error getting available space", e);
            return 0;
        }
    }
    
    /**
     * Get total storage space in MB
     * @return Total space in MB
     */
    public static long getTotalSpaceMB() {
        try {
            File externalStorage = Environment.getExternalStorageDirectory();
            StatFs stat = new StatFs(externalStorage.getPath());
            
            long totalBytes = stat.getTotalBytes();
            return totalBytes / (1024 * 1024); // Convert to MB
        } catch (Exception e) {
            Log.e(TAG, "Error getting total space", e);
            return 0;
        }
    }
    
    /**
     * Clean up old download files to free space
     * @param context Application context
     * @param requiredSpaceMB Required space in MB
     * @return true if enough space was freed, false otherwise
     */
    public static boolean cleanupForSpace(Context context, double requiredSpaceMB) {
        try {
            Log.d(TAG, "Attempting to cleanup space for " + requiredSpaceMB + "MB");
            
            // Get download directories
            File bearModDir = new File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOWNLOADS), "BearMod");
            File appDownloadsDir = new File(context.getExternalFilesDir(null), "downloads");
            
            List<File> filesToDelete = new ArrayList<>();
            
            // Find old files in BearMod directory
            if (bearModDir.exists()) {
                findOldFiles(bearModDir, filesToDelete);
            }
            
            // Find old files in app downloads directory
            if (appDownloadsDir.exists()) {
                findOldFiles(appDownloadsDir, filesToDelete);
            }
            
            // Sort files by last modified (oldest first)
            filesToDelete.sort((f1, f2) -> Long.compare(f1.lastModified(), f2.lastModified()));
            
            // Delete files until we have enough space
            long freedSpace = 0;
            long requiredBytes = (long) (requiredSpaceMB * 1024 * 1024);
            
            for (File file : filesToDelete) {
                if (freedSpace >= requiredBytes) {
                    break;
                }
                
                long fileSize = file.length();
                if (file.delete()) {
                    freedSpace += fileSize;
                    Log.d(TAG, "Deleted file: " + file.getName() + " (" + (fileSize / (1024 * 1024)) + "MB)");
                }
            }
            
            Log.d(TAG, "Freed " + (freedSpace / (1024 * 1024)) + "MB of space");
            
            // Check if we now have enough space
            return hasEnoughSpace(requiredSpaceMB);
            
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup", e);
            return false;
        }
    }
    
    /**
     * Find old files in directory (older than 7 days)
     * @param directory Directory to search
     * @param filesToDelete List to add old files to
     */
    private static void findOldFiles(File directory, List<File> filesToDelete) {
        if (!directory.exists() || !directory.isDirectory()) {
            return;
        }
        
        long sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L);
        
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile() && file.lastModified() < sevenDaysAgo) {
                    // Skip currently downloading files
                    if (!file.getName().endsWith(".tmp") && !file.getName().endsWith(".downloading")) {
                        filesToDelete.add(file);
                    }
                }
            }
        }
    }
    
    /**
     * Create necessary download directories
     * @param context Application context
     * @return true if directories were created successfully, false otherwise
     */
    public static boolean createDownloadDirectories(Context context) {
        try {
            // Create BearMod download directory
            File bearModDir = new File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOWNLOADS), "BearMod");
            if (!bearModDir.exists() && !bearModDir.mkdirs()) {
                Log.e(TAG, "Failed to create BearMod directory");
                return false;
            }
            
            // Create app downloads directory
            File appDownloadsDir = new File(context.getExternalFilesDir(null), "downloads");
            if (!appDownloadsDir.exists() && !appDownloadsDir.mkdirs()) {
                Log.e(TAG, "Failed to create app downloads directory");
                return false;
            }
            
            Log.d(TAG, "Download directories created successfully");
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error creating download directories", e);
            return false;
        }
    }
    
    /**
     * Get storage info for display
     * @return Storage info object
     */
    public static StorageInfo getStorageInfo() {
        long totalSpace = getTotalSpaceMB();
        long availableSpace = getAvailableSpaceMB();
        long usedSpace = totalSpace - availableSpace;
        
        return new StorageInfo(totalSpace, availableSpace, usedSpace);
    }
    
    /**
     * Storage information class
     */
    public static class StorageInfo {
        public final long totalSpaceMB;
        public final long availableSpaceMB;
        public final long usedSpaceMB;
        public final int usagePercentage;
        
        StorageInfo(long totalSpaceMB, long availableSpaceMB, long usedSpaceMB) {
            this.totalSpaceMB = totalSpaceMB;
            this.availableSpaceMB = availableSpaceMB;
            this.usedSpaceMB = usedSpaceMB;
            this.usagePercentage = totalSpaceMB > 0 ? (int) ((usedSpaceMB * 100) / totalSpaceMB) : 0;
        }
        
        @Override
        public String toString() {
            return String.format("Storage: %dMB used / %dMB total (%d%% used), %dMB available",
                usedSpaceMB, totalSpaceMB, usagePercentage, availableSpaceMB);
        }
    }
}
