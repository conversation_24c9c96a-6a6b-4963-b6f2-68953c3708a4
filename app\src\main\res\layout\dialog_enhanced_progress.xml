<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/bg_enhanced_dialog">

    <!-- Header Section with Game Icon and Download Icon -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp">

        <!-- Game Icon -->
        <ImageView
            android:id="@+id/iv_game_icon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_pubg_mobile_global_logo"
            android:contentDescription="Game Icon"
            android:background="@drawable/bg_icon_circle"
            android:padding="8dp"
            android:elevation="4dp"
            tools:ignore="HardcodedText" />

        <!-- Download Icon (Animated) -->
        <ImageView
            android:id="@+id/iv_download_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_download"
            android:contentDescription="Download Icon"
            android:tint="@color/accent"
            tools:ignore="HardcodedText" />

        <!-- Title and File Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/iv_game_icon"
            android:layout_toStartOf="@id/iv_download_icon"
            android:layout_centerVertical="true"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">

            <!-- Download Title -->
            <TextView
                android:id="@+id/tv_download_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Downloading APK + OBB"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:ignore="HardcodedText" />

            <!-- File Name -->
            <TextView
                android:id="@+id/tv_file_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="pubg-mobile-gl.apk"
                android:textColor="@color/text_secondary"
                android:textSize="13sp"
                android:layout_marginTop="2dp"
                android:maxLines="1"
                android:ellipsize="middle"
                tools:ignore="HardcodedText" />

            <!-- Status Message -->
            <TextView
                android:id="@+id/tv_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Preparing download..."
                android:textColor="@color/accent"
                android:textSize="11sp"
                android:layout_marginTop="2dp"
                android:maxLines="1"
                android:ellipsize="end"
                tools:ignore="HardcodedText" />

        </LinearLayout>

    </RelativeLayout>

    <!-- Progress Section with Beautiful Container -->
    <LinearLayout
        android:id="@+id/progress_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bg_progress_enhanced"
        android:padding="20dp"
        android:layout_marginBottom="20dp"
        android:elevation="2dp">

        <!-- Large Progress Percentage -->
        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="0%"
            android:textColor="@color/text_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp"
            android:fontFamily="sans-serif-medium"
            tools:ignore="HardcodedText" />

        <!-- Enhanced Progress Bar -->
        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:layout_marginBottom="16dp"
            android:progressDrawable="@drawable/progress_bar_enhanced"
            android:max="100"
            android:progress="0"
            android:elevation="1dp" />

        <!-- Progress Bar Background Glow -->
        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@drawable/progress_glow"
            android:layout_marginTop="-18dp"
            android:layout_marginBottom="16dp"
            android:alpha="0.6" />

    </LinearLayout>

    <!-- Download Statistics Grid -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:baselineAligned="false">

        <!-- Speed Card -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/bg_stat_card_enhanced"
            android:padding="16dp"
            android:layout_marginEnd="6dp"
            android:elevation="1dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_speed"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="8dp"
                android:tint="@color/accent"
                android:contentDescription="Speed Icon"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Speed"
                android:textColor="@color/text_secondary"
                android:textSize="11sp"
                android:gravity="center"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_speed"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 MB/s"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <!-- Downloaded Size Card -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/bg_stat_card_enhanced"
            android:padding="16dp"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:elevation="1dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_storage"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="8dp"
                android:tint="@color/accent"
                android:contentDescription="Storage Icon"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Downloaded"
                android:textColor="@color/text_secondary"
                android:textSize="11sp"
                android:gravity="center"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_file_size"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="0 / 0 MB"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <!-- ETA Card -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@drawable/bg_stat_card_enhanced"
            android:padding="16dp"
            android:layout_marginStart="6dp"
            android:elevation="1dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_time"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="8dp"
                android:tint="@color/accent"
                android:contentDescription="Time Icon"
                tools:ignore="HardcodedText" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Time Left"
                android:textColor="@color/text_secondary"
                android:textSize="11sp"
                android:gravity="center"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_eta"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="--"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium"
                tools:ignore="HardcodedText" />

        </LinearLayout>

    </LinearLayout>

    <!-- Action Button -->
    <Button
        android:id="@+id/btn_cancel"
        style="@style/Widget.Material3.Button.OutlinedButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Cancel Download"
        android:textColor="@color/text_primary"
        android:textStyle="bold"
        android:padding="16dp"
        android:background="@drawable/btn_cancel_enhanced"
        android:elevation="2dp"
        tools:ignore="HardcodedText" />

</LinearLayout>
