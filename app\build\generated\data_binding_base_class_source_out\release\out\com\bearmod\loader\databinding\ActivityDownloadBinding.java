// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airbnb.lottie.LottieAnimationView;
import com.bearmod.loader.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDownloadBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final LottieAnimationView animationDownloadComplete;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final Button btnCancelDownload;

  @NonNull
  public final Button btnDownload;

  @NonNull
  public final MaterialCardView cardDownloadInfo;

  @NonNull
  public final MaterialCardView cardDownloadProgress;

  @NonNull
  public final Chip chipFilterAll;

  @NonNull
  public final Chip chipFilterLatest;

  @NonNull
  public final ChipGroup chipGroupFilters;

  @NonNull
  public final Chip chipSortDate;

  @NonNull
  public final Chip chipSortSize;

  @NonNull
  public final TextInputEditText etSearch;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final LinearProgressIndicator progressDownload;

  @NonNull
  public final CircularProgressIndicator progressLoading;

  @NonNull
  public final RecyclerView rvReleases;

  @NonNull
  public final TextInputLayout searchLayout;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvApkSize;

  @NonNull
  public final TextView tvDownloadPercentage;

  @NonNull
  public final TextView tvDownloadStatus;

  @NonNull
  public final TextView tvNoReleases;

  @NonNull
  public final TextView tvObbSize;

  @NonNull
  public final TextView tvTotalSize;

  private ActivityDownloadBinding(@NonNull CoordinatorLayout rootView,
      @NonNull LottieAnimationView animationDownloadComplete, @NonNull AppBarLayout appBarLayout,
      @NonNull Button btnCancelDownload, @NonNull Button btnDownload,
      @NonNull MaterialCardView cardDownloadInfo, @NonNull MaterialCardView cardDownloadProgress,
      @NonNull Chip chipFilterAll, @NonNull Chip chipFilterLatest,
      @NonNull ChipGroup chipGroupFilters, @NonNull Chip chipSortDate, @NonNull Chip chipSortSize,
      @NonNull TextInputEditText etSearch, @NonNull LinearLayout layoutEmptyState,
      @NonNull LinearProgressIndicator progressDownload,
      @NonNull CircularProgressIndicator progressLoading, @NonNull RecyclerView rvReleases,
      @NonNull TextInputLayout searchLayout, @NonNull SwipeRefreshLayout swipeRefresh,
      @NonNull MaterialToolbar toolbar, @NonNull TextView tvApkSize,
      @NonNull TextView tvDownloadPercentage, @NonNull TextView tvDownloadStatus,
      @NonNull TextView tvNoReleases, @NonNull TextView tvObbSize, @NonNull TextView tvTotalSize) {
    this.rootView = rootView;
    this.animationDownloadComplete = animationDownloadComplete;
    this.appBarLayout = appBarLayout;
    this.btnCancelDownload = btnCancelDownload;
    this.btnDownload = btnDownload;
    this.cardDownloadInfo = cardDownloadInfo;
    this.cardDownloadProgress = cardDownloadProgress;
    this.chipFilterAll = chipFilterAll;
    this.chipFilterLatest = chipFilterLatest;
    this.chipGroupFilters = chipGroupFilters;
    this.chipSortDate = chipSortDate;
    this.chipSortSize = chipSortSize;
    this.etSearch = etSearch;
    this.layoutEmptyState = layoutEmptyState;
    this.progressDownload = progressDownload;
    this.progressLoading = progressLoading;
    this.rvReleases = rvReleases;
    this.searchLayout = searchLayout;
    this.swipeRefresh = swipeRefresh;
    this.toolbar = toolbar;
    this.tvApkSize = tvApkSize;
    this.tvDownloadPercentage = tvDownloadPercentage;
    this.tvDownloadStatus = tvDownloadStatus;
    this.tvNoReleases = tvNoReleases;
    this.tvObbSize = tvObbSize;
    this.tvTotalSize = tvTotalSize;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDownloadBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDownloadBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_download, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDownloadBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.animationDownloadComplete;
      LottieAnimationView animationDownloadComplete = ViewBindings.findChildViewById(rootView, id);
      if (animationDownloadComplete == null) {
        break missingId;
      }

      id = R.id.app_bar_layout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btnCancelDownload;
      Button btnCancelDownload = ViewBindings.findChildViewById(rootView, id);
      if (btnCancelDownload == null) {
        break missingId;
      }

      id = R.id.btnDownload;
      Button btnDownload = ViewBindings.findChildViewById(rootView, id);
      if (btnDownload == null) {
        break missingId;
      }

      id = R.id.cardDownloadInfo;
      MaterialCardView cardDownloadInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardDownloadInfo == null) {
        break missingId;
      }

      id = R.id.cardDownloadProgress;
      MaterialCardView cardDownloadProgress = ViewBindings.findChildViewById(rootView, id);
      if (cardDownloadProgress == null) {
        break missingId;
      }

      id = R.id.chip_filter_all;
      Chip chipFilterAll = ViewBindings.findChildViewById(rootView, id);
      if (chipFilterAll == null) {
        break missingId;
      }

      id = R.id.chip_filter_latest;
      Chip chipFilterLatest = ViewBindings.findChildViewById(rootView, id);
      if (chipFilterLatest == null) {
        break missingId;
      }

      id = R.id.chip_group_filters;
      ChipGroup chipGroupFilters = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupFilters == null) {
        break missingId;
      }

      id = R.id.chip_sort_date;
      Chip chipSortDate = ViewBindings.findChildViewById(rootView, id);
      if (chipSortDate == null) {
        break missingId;
      }

      id = R.id.chip_sort_size;
      Chip chipSortSize = ViewBindings.findChildViewById(rootView, id);
      if (chipSortSize == null) {
        break missingId;
      }

      id = R.id.et_search;
      TextInputEditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.layout_empty_state;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.progressDownload;
      LinearProgressIndicator progressDownload = ViewBindings.findChildViewById(rootView, id);
      if (progressDownload == null) {
        break missingId;
      }

      id = R.id.progressLoading;
      CircularProgressIndicator progressLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressLoading == null) {
        break missingId;
      }

      id = R.id.rvReleases;
      RecyclerView rvReleases = ViewBindings.findChildViewById(rootView, id);
      if (rvReleases == null) {
        break missingId;
      }

      id = R.id.search_layout;
      TextInputLayout searchLayout = ViewBindings.findChildViewById(rootView, id);
      if (searchLayout == null) {
        break missingId;
      }

      id = R.id.swipe_refresh;
      SwipeRefreshLayout swipeRefresh = ViewBindings.findChildViewById(rootView, id);
      if (swipeRefresh == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvApkSize;
      TextView tvApkSize = ViewBindings.findChildViewById(rootView, id);
      if (tvApkSize == null) {
        break missingId;
      }

      id = R.id.tv_download_percentage;
      TextView tvDownloadPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadPercentage == null) {
        break missingId;
      }

      id = R.id.tv_download_status;
      TextView tvDownloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadStatus == null) {
        break missingId;
      }

      id = R.id.tvNoReleases;
      TextView tvNoReleases = ViewBindings.findChildViewById(rootView, id);
      if (tvNoReleases == null) {
        break missingId;
      }

      id = R.id.tvObbSize;
      TextView tvObbSize = ViewBindings.findChildViewById(rootView, id);
      if (tvObbSize == null) {
        break missingId;
      }

      id = R.id.tvTotalSize;
      TextView tvTotalSize = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSize == null) {
        break missingId;
      }

      return new ActivityDownloadBinding((CoordinatorLayout) rootView, animationDownloadComplete,
          appBarLayout, btnCancelDownload, btnDownload, cardDownloadInfo, cardDownloadProgress,
          chipFilterAll, chipFilterLatest, chipGroupFilters, chipSortDate, chipSortSize, etSearch,
          layoutEmptyState, progressDownload, progressLoading, rvReleases, searchLayout,
          swipeRefresh, toolbar, tvApkSize, tvDownloadPercentage, tvDownloadStatus, tvNoReleases,
          tvObbSize, tvTotalSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
