#include <jni.h>
#include <string>
#include <vector>
#include <openssl/sha.h>
#include <android/log.h>

#define LOG_TAG "BearTrust"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Trusted package constant
const char* TRUSTED_LOADER_PACKAGE = "com.bearmod.loader";

// Global context reference (set this when your app starts)
static jobject g_context = nullptr;

// Trusted signature hashes (replace with your actual hashes)
static const std::vector<std::string> TRUSTED_LOADER_HASHES = {
    "YOUR_BEARMOD_LOADER_RELEASE_SHA256_HASH",
    "YOUR_BEARMOD_LOADER_DEBUG_SHA256_HASH"
};

static const std::vector<std::string> TRUSTED_TARGET_HASHES = {
    "YOUR_TARGET_APP_RELEASE_SHA256_HASH", 
    "YOUR_TARGET_APP_DEBUG_SHA256_HASH"
};

/**
 * Calculate SHA-256 hash of byte array
 */
std::string calculateSHA256(const unsigned char* data, size_t length) {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(data, length, hash);
    
    std::string result;
    char buf[3];
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        sprintf(buf, "%02x", hash[i]);
        result += buf;
    }
    return result;
}

/**
 * Get signature hash for a package
 */
std::string getPackageSignatureHash(JNIEnv* env, jobject context, const char* packageName) {
    try {
        // Get PackageManager
        jclass contextCls = env->FindClass("android/content/Context");
        if (!contextCls) {
            LOGE("Failed to find Context class");
            return "";
        }
        
        jmethodID getPM = env->GetMethodID(contextCls, "getPackageManager", 
                                          "()Landroid/content/pm/PackageManager;");
        if (!getPM) {
            LOGE("Failed to get getPackageManager method");
            return "";
        }
        
        jobject pm = env->CallObjectMethod(context, getPM);
        if (!pm) {
            LOGE("Failed to get PackageManager instance");
            return "";
        }

        // Get PackageInfo with signatures
        jmethodID getPkgInfo = env->GetMethodID(env->GetObjectClass(pm),
            "getPackageInfo", "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        if (!getPkgInfo) {
            LOGE("Failed to get getPackageInfo method");
            return "";
        }
        
        jstring pkgName = env->NewStringUTF(packageName);
        if (!pkgName) {
            LOGE("Failed to create package name string");
            return "";
        }
        
        // GET_SIGNATURES = 0x40
        jobject info = env->CallObjectMethod(pm, getPkgInfo, pkgName, 0x40);
        env->DeleteLocalRef(pkgName);
        
        if (!info) {
            LOGE("Failed to get PackageInfo for %s", packageName);
            return "";
        }

        // Get signatures field
        jclass pkgInfoCls = env->GetObjectClass(info);
        jfieldID sigField = env->GetFieldID(pkgInfoCls, "signatures", 
                                          "[Landroid/content/pm/Signature;");
        if (!sigField) {
            LOGE("Failed to get signatures field");
            return "";
        }
        
        jobjectArray signatures = (jobjectArray)env->GetObjectField(info, sigField);
        if (!signatures) {
            LOGE("No signatures found for %s", packageName);
            return "";
        }

        // Get first signature
        jobject signature = env->GetObjectArrayElement(signatures, 0);
        if (!signature) {
            LOGE("Failed to get first signature for %s", packageName);
            return "";
        }

        // Get signature bytes
        jclass sigCls = env->GetObjectClass(signature);
        jmethodID toByteArray = env->GetMethodID(sigCls, "toByteArray", "()[B");
        if (!toByteArray) {
            LOGE("Failed to get toByteArray method");
            return "";
        }
        
        jbyteArray sigBytes = (jbyteArray)env->CallObjectMethod(signature, toByteArray);
        if (!sigBytes) {
            LOGE("Failed to get signature bytes for %s", packageName);
            return "";
        }

        // Convert to native bytes and hash
        jsize length = env->GetArrayLength(sigBytes);
        jbyte* bytes = env->GetByteArrayElements(sigBytes, nullptr);
        if (!bytes) {
            LOGE("Failed to get byte array elements for %s", packageName);
            return "";
        }

        // Calculate SHA-256 hash
        std::string hash = calculateSHA256((unsigned char*)bytes, length);
        
        // Release resources
        env->ReleaseByteArrayElements(sigBytes, bytes, JNI_ABORT);
        
        LOGI("Package %s signature hash: %s", packageName, hash.c_str());
        return hash;
        
    } catch (...) {
        LOGE("Exception in getPackageSignatureHash for %s", packageName);
        return "";
    }
}

/**
 * Check if package is installed
 */
bool isPackageInstalled(JNIEnv* env, jobject context, const char* packageName) {
    try {
        jclass contextCls = env->FindClass("android/content/Context");
        jmethodID getPM = env->GetMethodID(contextCls, "getPackageManager", 
                                          "()Landroid/content/pm/PackageManager;");
        jobject pm = env->CallObjectMethod(context, getPM);
        
        jmethodID getPkgInfo = env->GetMethodID(env->GetObjectClass(pm),
            "getPackageInfo", "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        
        jstring pkgName = env->NewStringUTF(packageName);
        jobject info = env->CallObjectMethod(pm, getPkgInfo, pkgName, 0);
        env->DeleteLocalRef(pkgName);
        
        return info != nullptr;
        
    } catch (...) {
        return false;
    }
}

/**
 * Main verification function - checks if app was launched by trusted loader
 * This is the function you call in your target mod: isVerifiedFromLoader(env, context)
 */
bool isVerifiedFromLoader(JNIEnv* env, jobject context) {
    LOGI("=== Starting BearTrust Verification ===");
    
    // 1. Check if BearMod Loader is installed
    if (!isPackageInstalled(env, context, TRUSTED_LOADER_PACKAGE)) {
        LOGE("BearMod Loader not installed: %s", TRUSTED_LOADER_PACKAGE);
        return false;
    }
    
    // 2. Get and verify BearMod Loader signature
    std::string loaderHash = getPackageSignatureHash(env, context, TRUSTED_LOADER_PACKAGE);
    if (loaderHash.empty()) {
        LOGE("Failed to get BearMod Loader signature");
        return false;
    }
    
    // 3. Check against trusted loader hashes
    bool loaderTrusted = false;
    for (const auto& trustedHash : TRUSTED_LOADER_HASHES) {
        if (loaderHash == trustedHash) {
            loaderTrusted = true;
            break;
        }
    }
    
    if (!loaderTrusted) {
        LOGE("BearMod Loader signature not trusted: %s", loaderHash.c_str());
        return false;
    }
    
    LOGI("BearMod Loader signature verified: %s", loaderHash.c_str());
    
    // 4. Verify current app signature
    jclass contextCls = env->FindClass("android/content/Context");
    jmethodID getPkgName = env->GetMethodID(contextCls, "getPackageName", "()Ljava/lang/String;");
    jstring currentPkg = (jstring)env->CallObjectMethod(context, getPkgName);
    
    const char* currentPkgName = env->GetStringUTFChars(currentPkg, nullptr);
    std::string currentHash = getPackageSignatureHash(env, context, currentPkgName);
    env->ReleaseStringUTFChars(currentPkg, currentPkgName);
    
    if (currentHash.empty()) {
        LOGE("Failed to get current app signature");
        return false;
    }
    
    // 5. Check current app against trusted target hashes
    bool targetTrusted = false;
    for (const auto& trustedHash : TRUSTED_TARGET_HASHES) {
        if (currentHash == trustedHash) {
            targetTrusted = true;
            break;
        }
    }
    
    if (!targetTrusted) {
        LOGE("Target app signature not trusted: %s", currentHash.c_str());
        return false;
    }
    
    LOGI("Target app signature verified: %s", currentHash.c_str());
    
    LOGI("=== BearTrust Verification PASSED ===");
    return true;
}

// JNI exports for nativelib integration
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_targetapp_BearTrust_nativeVerifyInternal(JNIEnv* env, jobject thiz, jobject context) {
    return isVerifiedFromLoader(env, context) ? JNI_TRUE : JNI_FALSE;
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_targetapp_BearTrust_nativeGetSignatureHash(
        JNIEnv* env, jobject thiz, jobject context, jstring packageName) {
    
    const char* pkgName = env->GetStringUTFChars(packageName, nullptr);
    std::string hash = getPackageSignatureHash(env, context, pkgName);
    env->ReleaseStringUTFChars(packageName, pkgName);
    
    if (hash.empty()) {
        return nullptr;
    }
    
    return env->NewStringUTF(hash.c_str());
}
