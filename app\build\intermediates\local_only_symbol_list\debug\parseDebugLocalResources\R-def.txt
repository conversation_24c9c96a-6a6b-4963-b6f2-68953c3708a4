R_DEF: Internal format may change without notice
local
anim fade_slide_up
anim pulse
anim slide_in_left
anim slide_in_right
anim slide_out_left
anim slide_out_right
color accent
color accent_dark
color accent_light
color background
color background_light
color black
color card_background
color card_bg
color divider
color error
color info
color nav_item_color
color on_background
color on_primary
color on_secondary
color on_surface
color outline
color outline_variant
color primary
color primary_container
color primary_dark
color primary_light
color progress_background
color purple_200
color purple_500
color purple_700
color ripple
color secondary
color secondary_container
color secondary_dark
color secondary_light
color shimmer_color
color success
color surface
color surface_container
color surface_container_high
color surface_container_highest
color surface_container_low
color surface_container_lowest
color surface_variant
color teal_200
color teal_700
color text_hint
color text_primary
color text_secondary
color transparent
color warning
color white
dimen corner_radius_large
dimen corner_radius_medium
dimen corner_radius_small
dimen elevation_large
dimen elevation_medium
dimen elevation_small
dimen glyph_corner_radius
dimen icon_size_large
dimen icon_size_medium
dimen icon_size_small
dimen margin_large
dimen margin_medium
dimen margin_small
dimen padding_large
dimen padding_medium
dimen padding_small
dimen text_size_caption
dimen text_size_large
dimen text_size_medium
dimen text_size_small
drawable app_logo
drawable bg_bottom_nav
drawable bg_dialog_rounded
drawable bg_main_gradient
drawable bg_progress_container
drawable bg_stat_card
drawable bg_status
drawable bg_status_chip
drawable circle_background
drawable ic_app_icon
drawable ic_back
drawable ic_cancel
drawable ic_check_circle
drawable ic_clear_cache
drawable ic_close
drawable ic_dashboard
drawable ic_download
drawable ic_empty
drawable ic_error
drawable ic_glyph_lock
drawable ic_home
drawable ic_key
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_logout
drawable ic_nav_cloud
drawable ic_nav_home
drawable ic_nav_profile
drawable ic_nav_settings
drawable ic_paste
drawable ic_pause
drawable ic_pubg_gl
drawable ic_pubg_global_vector
drawable ic_pubg_kr
drawable ic_pubg_kr_vector
drawable ic_pubg_tw
drawable ic_pubg_tw_vector
drawable ic_pubg_vn
drawable ic_pubg_vn_vector
drawable ic_reset
drawable ic_search
drawable ic_settings
drawable ic_sort
drawable logo
drawable logo_splash
drawable rounded_image_bg
drawable splash_background
drawable splash_branding
drawable splash_gradient
drawable splash_gradient_modern
id animationDownloadComplete
id appBarLayout
id appNameText
id app_bar_layout
id app_description
id app_icon
id app_name
id app_version
id bottom_navigation
id btnCancelDownload
id btnDownload
id btnLogin
id btn_action
id btn_cancel
id btn_cancel_download
id btn_download
id btn_download_patches
id btn_logout
id btn_pause
id btn_pause_resume
id btn_scan_offsets
id btn_start_patching
id bullet_point
id cardDownloadInfo
id cardDownloadProgress
id card_download_info
id card_download_progress
id card_logs
id card_mode
id card_release
id card_target_app
id checkboxAutoLogin
id checkboxRemember
id chip_download_status
id chip_file_size
id chip_filter_all
id chip_filter_latest
id chip_game_version
id chip_group_filters
id chip_group_info
id chip_release_date
id chip_sort_date
id chip_sort_size
id chip_status
id download_progress_container
id drawer_layout
id editLicenseKey
id et_search
id glyphView
id header_subtitle
id header_title
id ivGameImage
id iv_game_icon
id iv_release_icon
id layout_clear_cache
id layout_empty
id layout_empty_state
id layout_reset_config
id loadingText
id logoContainer
id logoImage
id nav_dashboard
id nav_download
id nav_downloads
id nav_home
id nav_logout
id nav_settings
id nav_view
id progressBar
id progressContainer
id progressDownload
id progressLoading
id progress_bar
id progress_bar_inline
id progress_circular
id progress_download
id progress_loading
id progress_patching
id radio_group_mode
id radio_non_root
id radio_root
id ripple_effect
id rvReleases
id rv_app_versions
id rv_patches
id rv_releases
id search_layout
id shimmer_button
id shimmer_date
id shimmer_description
id shimmer_layout
id shimmer_status
id shimmer_title
id shimmer_version
id spinner_target_app
id status
id subtitleText
id swipe_refresh
id switch_auto_login
id switch_stealth
id tapText
id textInputLayout
id toolbar
id tvApkSize
id tvGameDetails
id tvGameName
id tvNoReleases
id tvObbSize
id tvTotalSize
id tv_apk_size
id tv_available_patches
id tv_download_percentage
id tv_download_speed
id tv_download_status
id tv_download_title
id tv_downloaded_size
id tv_eta
id tv_eta_inline
id tv_file_name
id tv_file_size
id tv_license_info
id tv_logs
id tv_logs_title
id tv_obb_size
id tv_patch_name
id tv_progress
id tv_progress_inline
id tv_release_description
id tv_release_name
id tv_release_version
id tv_speed
id tv_speed_inline
id tv_total_size
id versionText
id view_overlay
layout activity_download
layout activity_download_modern
layout activity_login
layout activity_main
layout activity_main_loader
layout activity_patch_execution
layout activity_settings
layout activity_splash
layout activity_tap_unlock
layout dialog_download_progress
layout item_app_version
layout item_patch_release
layout item_patch_shimmer
layout item_release
layout layout_download_progress_modern
layout nav_header
menu bottom_nav_menu
menu bottom_navigation_menu
menu drawer_menu
mipmap ic_launcher
mipmap ic_launcher_round
raw bear_intro_mobile
raw download_complete
raw success_animation
string about
string about_description
string all
string apk_size
string app_logo
string app_name
string app_settings
string app_version
string apply_patch
string auto_login
string auto_update
string available
string available_patches
string available_releases
string cache_cleared
string cancel
string cancel_download
string cancelled
string clear_cache
string clear_cache_confirm
string clear_filters
string completed
string config_reset
string confirm
string continue_anyway
string continue_download
string dark_mode
string dashboard_title
string days_remaining
string delete
string delete_all_downloads
string delete_download
string download
string download_cancelled
string download_complete
string download_complete_path
string download_failed
string download_in_progress
string download_patches
string download_progress
string download_title
string downloaded
string downloading
string downloading_detailed
string downloading_status
string downloads
string enter_license_key
string error
string eta
string execution_logs
string execution_mode
string exit
string failed
string file_size_format
string filter_by_version
string forgot_password
string game_icon
string game_version
string game_version_format
string global_version_desc
string icon_filter
string icon_search
string icon_sort
string info
string invalid_credentials
string invalid_license_key_format
string keyauth_init_failed
string keyauth_init_warning
string kr_version_desc
string language
string latest
string license_key
string license_valid_until
string licenses
string loading
string login
string login_error
string login_failed
string login_subtitle
string login_success
string logout
string logout_confirm
string nav_downloads
string nav_home
string nav_settings
string navigation_drawer_close
string navigation_drawer_open
string network_error
string no
string no_downloads
string no_patches_available
string no_releases_available
string no_results_found
string non_root_mode
string not_installed
string notification_settings
string notifications
string obb_size
string ok
string password
string patch_execution
string patch_settings
string patch_status
string patches_updated
string patching_complete
string patching_failed
string patching_in_progress
string pause
string paused
string privacy_policy
string pubg_global
string pubg_kr
string pubg_tw
string pubg_vn
string pull_down_to_refresh_or_check_your_connection
string register
string registration_date
string release_date_format
string released
string remember_me
string reset_config
string reset_config_confirm
string resume
string retry
string root_mode
string scan_offsets
string scanning_offsets
string scanning_offsets_for
string search_releases
string select_release_first
string select_target
string select_version
string settings_title
string sort_by_date
string sort_by_size
string start_patching
string stop_patching
string success
string sync_error
string target_app
string terms_of_service
string theme
string theme_dark
string theme_light
string theme_system
string toggle_stealth
string total_size
string tw_version_desc
string unknown_error
string up_to_date
string update_available
string updated
string username
string version
string version_format
string version_info
string vn_version_desc
string warning
string yes
style Animation.BearLoader.Activity
style ShapeAppearance.BearLoader.LargeComponent
style ShapeAppearance.BearLoader.MediumComponent
style ShapeAppearance.BearLoader.SmallComponent
style TextAppearance.BearLoader.Body1
style TextAppearance.BearLoader.Body2
style TextAppearance.BearLoader.Caption
style TextAppearance.BearLoader.Headline6
style Theme.BearLoader
style Theme.BearLoader.AppBarOverlay
style Theme.BearLoader.Dialog
style Theme.BearLoader.NoActionBar
style Theme.BearLoader.PopupOverlay
style Theme.BearLoader.Splash
style Theme.BearMod
style Widget.BearLoader.Button
style Widget.BearLoader.Button.Icon
style Widget.BearLoader.Button.Secondary
style Widget.BearLoader.Button.Small
style Widget.BearLoader.Button.Tonal
xml backup_rules
xml data_extraction_rules
xml file_paths
xml network_security_config
