<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#0A0A0A"
    android:fitsSystemWindows="true">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_pubg_global_vector"
            android:layout_marginEnd="16dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PUBG MOBILE"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Global Version"
                android:textColor="#888888"
                android:textSize="14sp" />

        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="v3.8.0"
            android:textColor="#00FF88"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Download Options -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- APK Only -->
        <LinearLayout
            android:id="@+id/download_apk"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:orientation="horizontal"
            android:background="@drawable/download_card_bg"
            android:padding="20dp"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="APK Only"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="120.5 MB • Auto-install"
                    android:textColor="#888888"
                    android:textSize="14sp" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="DOWNLOAD"
                android:textColor="#00FF88"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- APK + OBB -->
        <LinearLayout
            android:id="@+id/download_full"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:orientation="horizontal"
            android:background="@drawable/download_card_bg"
            android:padding="20dp"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="APK + OBB"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1100.8 MB • Complete package"
                    android:textColor="#888888"
                    android:textSize="14sp" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="DOWNLOAD"
                android:textColor="#00FF88"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- OBB Only -->
        <LinearLayout
            android:id="@+id/download_obb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/download_card_bg"
            android:padding="20dp"
            android:clickable="true"
            android:focusable="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="OBB Only"
                        android:textColor="#FFFFFF"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="980.3 MB • Data files"
                        android:textColor="#888888"
                        android:textSize="14sp" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="DOWNLOAD"
                    android:textColor="#00FF88"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- Progress Section (Initially Hidden) -->
    <LinearLayout
        android:id="@+id/progress_section"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:orientation="vertical"
        android:background="#111111"
        android:padding="20dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/download_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Downloading APK + OBB..."
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/download_cancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="CANCEL"
                android:textColor="#FF4444"
                android:textSize="14sp"
                android:textStyle="bold"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginBottom="12dp"
            android:progressTint="#00FF88"
            android:progressBackgroundTint="#333333"
            android:max="100"
            android:progress="0" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/progress_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="0% • 0 MB/s"
                android:textColor="#888888"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/progress_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0 / 0 MB"
                android:textColor="#888888"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
