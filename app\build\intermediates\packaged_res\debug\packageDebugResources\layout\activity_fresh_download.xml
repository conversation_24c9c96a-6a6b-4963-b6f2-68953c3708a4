<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#0D1117"
    android:fitsSystemWindows="true">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:background="#161B22"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_pubg_back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/bearmod_loader"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginStart="16dp" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_pubg_settings"
            android:layout_marginStart="16dp" />

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_pubg_download"
            android:layout_marginStart="16dp" />

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="16dp"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_pubg_profile"
            android:background="@drawable/bg_status_chip" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center"
        tools:ignore="DisableBaselineAlignment">

        <LinearLayout
            android:id="@+id/tab_global"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_selected_bg"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_mobile_global_logo"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/global"
                android:textColor="#FFFFFF"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_kr"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_unselected_bg"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_mobile_kr_logo"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/kr"
                android:textColor="#8B949E"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_tw"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_unselected_bg"
            android:gravity="center"
            android:layout_marginEnd="8dp"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_mobile_tw_logo"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tw"
                android:textColor="#8B949E"
                android:textSize="14sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tab_vn"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:background="@drawable/tab_unselected_bg"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_mobile_vn_logo"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/vn"
                android:textColor="#8B949E"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Game Cards -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- PUBG MOBILE Global -->
            <LinearLayout
                android:id="@+id/card_global"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/game_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pubg_mobile_global"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="4dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/version_3_8_0_64bit"
                                android:textColor="#8B949E"
                                android:textSize="14sp" />

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:importantForAccessibility="no"
                                android:src="@drawable/pubg_status_safe"
                                android:layout_marginEnd="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/safe"
                                android:textColor="#38FF00"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/download_size_1_08_gb"
                            android:textColor="#656D76"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:importantForAccessibility="no"
                        android:src="@drawable/ic_pubg_mobile_global_logo"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/btn_download_global"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Download"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="@drawable/btn_download_bg"
                        android:layout_marginEnd="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                    <Button
                        android:id="@+id/btn_update_global"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="@string/update"
                        android:textColor="#FF0000"
                        android:textStyle="bold"
                        android:background="@drawable/btn_update_bg"
                        android:layout_marginStart="8dp"
                        tools:ignore="ButtonStyle" />

                </LinearLayout>

            </LinearLayout>

            <!-- PUBG MOBILE KR -->
            <LinearLayout
                android:id="@+id/card_kr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/game_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pubg_mobile_kr"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="4dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Version: 3.8.0 • 64BIT: "
                                android:textColor="#8B949E"
                                android:textSize="14sp"
                                tools:ignore="HardcodedText" />

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:importantForAccessibility="no"
                                android:src="@drawable/pubg_status_safe"
                                android:layout_marginEnd="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Safe"
                                android:textColor="#00D084"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:ignore="HardcodedText" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Download Size: 1.07 GB"
                            android:textColor="#656D76"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"
                            tools:ignore="HardcodedText" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:importantForAccessibility="no"
                        android:src="@drawable/ic_pubg_mobile_kr_logo"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/btn_download_kr"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Download"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="@drawable/btn_download_bg"
                        android:layout_marginEnd="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                    <Button
                        android:id="@+id/btn_update_kr"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Update"
                        android:textColor="#FF0000"
                        android:textStyle="bold"
                        android:background="@drawable/btn_update_bg"
                        android:layout_marginStart="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                </LinearLayout>

            </LinearLayout>

            <!-- PUBG MOBILE TW -->
            <LinearLayout
                android:id="@+id/card_tw"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/game_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pubg_mobile_tw"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="4dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Version: 3.8.0 • 64BIT: "
                                android:textColor="#8B949E"
                                android:textSize="14sp"
                                tools:ignore="HardcodedText" />

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:importantForAccessibility="no"
                                android:src="@drawable/pubg_status_safe"
                                android:layout_marginEnd="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Safe"
                                android:textColor="#38FF00"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:ignore="HardcodedText" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Download Size: 1.10 GB"
                            android:textColor="#656D76"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"
                            tools:ignore="HardcodedText" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:importantForAccessibility="no"
                        android:src="@drawable/ic_pubg_mobile_tw_logo"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/btn_download_tw"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Download"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="@drawable/btn_download_bg"
                        android:layout_marginEnd="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                    <Button
                        android:id="@+id/btn_update_tw"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Update"
                        android:textColor="#00D084"
                        android:textStyle="bold"
                        android:background="@drawable/btn_update_bg"
                        android:layout_marginStart="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                </LinearLayout>

            </LinearLayout>

            <!-- PUBG MOBILE VN -->
            <LinearLayout
                android:id="@+id/card_vn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/game_card_bg"
                android:padding="20dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/pubg_mobile_vn"
                            android:textColor="#FFFFFF"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginTop="4dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Version: 3.8.0 • 64BIT: "
                                android:textColor="#8B949E"
                                android:textSize="14sp"
                                tools:ignore="HardcodedText" />

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:importantForAccessibility="no"
                                android:src="@drawable/pubg_status_safe"
                                android:layout_marginEnd="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Safe"
                                android:textColor="#00D084"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                tools:ignore="HardcodedText" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Download Size: 1.13 GB"
                            android:textColor="#656D76"
                            android:textSize="14sp"
                            android:layout_marginTop="4dp"
                            tools:ignore="HardcodedText" />

                    </LinearLayout>

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:importantForAccessibility="no"
                        android:src="@drawable/ic_pubg_mobile_vn_logo"
                        android:layout_marginStart="16dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="16dp">

                    <Button
                        android:id="@+id/btn_download_vn"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Download"
                        android:textColor="#FFFFFF"
                        android:textStyle="bold"
                        android:background="@drawable/btn_download_bg"
                        android:layout_marginEnd="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                    <Button
                        android:id="@+id/btn_update_vn"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_weight="1"
                        android:text="Update"
                        android:textColor="#00D084"
                        android:textStyle="bold"
                        android:background="@drawable/btn_update_bg"
                        android:layout_marginStart="8dp"
                        tools:ignore="ButtonStyle,HardcodedText" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- PUBG Tactical Bottom Action Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="72dp"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:background="#0D1117"
        android:gravity="center"
        android:paddingHorizontal="24dp"
        android:paddingVertical="8dp">

        <LinearLayout
            android:id="@+id/action_logout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:layout_marginEnd="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_logout"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Logout"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/action_clear_data"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:layout_marginHorizontal="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_clear_data"
                android:layout_marginBottom="4dp"
                tools:ignore="TooManyViews" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/clear_data"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/action_help"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:layout_marginStart="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_pubg_help"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/support"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
