package com.bearmod.loader.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import java.io.File;

/**
 * Utility class for installing APK files
 * Handles auto-installation with proper permissions and file providers
 */
public class ApkInstaller {
    
    private static final String TAG = "ApkInstaller";
    private static final String FILE_PROVIDER_AUTHORITY = "com.bearmod.loader.fileprovider";
    
    /**
     * Install APK file with auto-installation
     * @param context Application context
     * @param apkFile APK file to install
     * @param callback Installation callback
     */
    public static void installApk(Context context, File apkFile, InstallationCallback callback) {
        if (!apkFile.exists()) {
            Log.e(TAG, "APK file does not exist: " + apkFile.getAbsolutePath());
            callback.onError("APK file not found");
            return;
        }
        
        Log.d(TAG, "Installing APK: " + apkFile.getAbsolutePath());
        
        // Check if we can install unknown apps
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (!context.getPackageManager().canRequestPackageInstalls()) {
                Log.w(TAG, "Cannot install unknown apps, requesting permission");
                requestInstallPermission(context, callback);
                return;
            }
        }
        
        try {
            // Create intent for APK installation
            Intent installIntent = new Intent(Intent.ACTION_VIEW);
            installIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            
            Uri apkUri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Use FileProvider for Android 7.0+
                apkUri = FileProvider.getUriForFile(context, FILE_PROVIDER_AUTHORITY, apkFile);
            } else {
                // Direct file URI for older versions
                apkUri = Uri.fromFile(apkFile);
            }
            
            installIntent.setDataAndType(apkUri, "application/vnd.android.package-archive");
            
            Log.d(TAG, "Starting APK installation intent");
            context.startActivity(installIntent);
            
            // Show success message
            Toast.makeText(context, "APK installation started", Toast.LENGTH_SHORT).show();
            callback.onSuccess("APK installation started");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to install APK", e);
            callback.onError("Failed to install APK: " + e.getMessage());
        }
    }
    
    /**
     * Request permission to install unknown apps (Android 8.0+)
     * @param context Application context
     * @param callback Installation callback
     */
    private static void requestInstallPermission(Context context, InstallationCallback callback) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                
                Toast.makeText(context, 
                    "Please enable 'Install unknown apps' permission and try again", 
                    Toast.LENGTH_LONG).show();
                    
                callback.onError("Install permission required");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to request install permission", e);
            callback.onError("Failed to request install permission: " + e.getMessage());
        }
    }
    
    /**
     * Check if APK installation is supported
     * @param context Application context
     * @return True if installation is supported
     */
    public static boolean isInstallationSupported(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return context.getPackageManager().canRequestPackageInstalls();
        }
        return true; // Older versions don't need special permission
    }
    
    /**
     * Get package name from APK file
     * @param context Application context
     * @param apkFile APK file
     * @return Package name or null if failed
     */
    public static String getPackageName(Context context, File apkFile) {
        try {
            PackageManager pm = context.getPackageManager();
            android.content.pm.PackageInfo packageInfo = pm.getPackageArchiveInfo(
                apkFile.getAbsolutePath(), PackageManager.GET_ACTIVITIES);
            
            if (packageInfo != null) {
                return packageInfo.packageName;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get package name from APK", e);
        }
        return null;
    }
    
    /**
     * Check if package is already installed
     * @param context Application context
     * @param packageName Package name to check
     * @return True if package is installed
     */
    public static boolean isPackageInstalled(Context context, String packageName) {
        try {
            context.getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Installation callback interface
     */
    public interface InstallationCallback {
        void onSuccess(String message);
        void onError(String error);
    }
}
