<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_app_version" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\item_app_version.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_app_version_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="176" endOffset="51"/></Target><Target id="@+id/app_icon" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="57"/></Target><Target id="@+id/app_name" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="38" endOffset="41"/></Target><Target id="@+id/status" view="TextView"><Expressions/><location startLine="40" startOffset="8" endLine="53" endOffset="45"/></Target><Target id="@+id/app_version" view="TextView"><Expressions/><location startLine="55" startOffset="8" endLine="65" endOffset="40"/></Target><Target id="@+id/bullet_point" view="TextView"><Expressions/><location startLine="67" startOffset="8" endLine="77" endOffset="65"/></Target><Target id="@+id/app_description" view="TextView"><Expressions/><location startLine="79" startOffset="8" endLine="89" endOffset="47"/></Target><Target id="@+id/download_progress_container" view="LinearLayout"><Expressions/><location startLine="92" startOffset="8" endLine="152" endOffset="22"/></Target><Target id="@+id/progress_bar_inline" view="ProgressBar"><Expressions/><location startLine="106" startOffset="12" endLine="115" endOffset="74"/></Target><Target id="@+id/tv_progress_inline" view="TextView"><Expressions/><location startLine="123" startOffset="16" endLine="130" endOffset="63"/></Target><Target id="@+id/tv_speed_inline" view="TextView"><Expressions/><location startLine="132" startOffset="16" endLine="139" endOffset="63"/></Target><Target id="@+id/tv_eta_inline" view="TextView"><Expressions/><location startLine="141" startOffset="16" endLine="148" endOffset="63"/></Target><Target id="@+id/btn_action" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="154" startOffset="8" endLine="172" endOffset="55"/></Target></Targets></Layout>