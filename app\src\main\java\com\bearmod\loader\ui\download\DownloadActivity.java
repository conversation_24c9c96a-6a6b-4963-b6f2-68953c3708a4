package com.bearmod.loader.ui.download;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.bearmod.loader.ui.main.MainActivity;

/**
 * Download activity
 * Handles patch management with root/no-root options
 */
public class DownloadActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Create a simple layout programmatically to avoid layout issues
            LinearLayout layout = new LinearLayout(this);
            layout.setOrientation(LinearLayout.VERTICAL);
            layout.setPadding(32, 32, 32, 32);

            // Add title
            TextView title = new TextView(this);
            title.setText("Enhanced Download System");
            title.setTextSize(24);
            title.setPadding(0, 0, 0, 32);
            layout.addView(title);

            // Add description
            TextView description = new TextView(this);
            description.setText("This is the new enhanced download system with APK/OBB options, progress tracking, and auto-installation features.");
            description.setPadding(0, 0, 0, 32);
            layout.addView(description);

            // Add patch execution section
            TextView patchTitle = new TextView(this);
            patchTitle.setText("Patch Management");
            patchTitle.setTextSize(20);
            patchTitle.setPadding(0, 16, 0, 16);
            layout.addView(patchTitle);

            TextView patchDescription = new TextView(this);
            patchDescription.setText("Select root or no-root patching mode and apply patches to your games.");
            patchDescription.setPadding(0, 0, 0, 16);
            layout.addView(patchDescription);

            // Add root mode button
            Button rootModeButton = new Button(this);
            rootModeButton.setText("Root Mode Patching");
            rootModeButton.setOnClickListener(v -> showPatchModeDialog(true));
            layout.addView(rootModeButton);

            // Add no-root mode button
            Button noRootModeButton = new Button(this);
            noRootModeButton.setText("No-Root Mode Patching");
            noRootModeButton.setOnClickListener(v -> showPatchModeDialog(false));
            layout.addView(noRootModeButton);

            // Add back button
            Button backButton = new Button(this);
            backButton.setText("Back to Main");
            backButton.setOnClickListener(v -> {
                Intent intent = new Intent(this, MainActivity.class);
                startActivity(intent);
                finish();
            });
            layout.addView(backButton);

            setContentView(layout);

        } catch (Exception e) {
            android.util.Log.e("DownloadActivity", "Error in onCreate: " + e.getMessage(), e);
            Toast.makeText(this, "Error loading download interface: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    /**
     * Show patch mode dialog for root/no-root selection
     * @param isRootMode Whether this is root mode or no-root mode
     */
    private void showPatchModeDialog(boolean isRootMode) {
        String mode = isRootMode ? "Root" : "No-Root";
        String title = mode + " Mode Patching";

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(title);

        String message = "You selected " + mode + " mode patching.\n\n";
        if (isRootMode) {
            message += "Root Mode Features:\n";
            message += "• Direct memory modification\n";
            message += "• System-level access\n";
            message += "• Advanced patching capabilities\n";
            message += "• Requires root permissions\n\n";
            message += "Note: Make sure your device is rooted and you have granted root permissions to this app.";
        } else {
            message += "No-Root Mode Features:\n";
            message += "• Safe patching without root\n";
            message += "• File-based modifications\n";
            message += "• Compatible with all devices\n";
            message += "• Limited patching capabilities\n\n";
            message += "Note: Some advanced patches may not be available in no-root mode.";
        }

        builder.setMessage(message);

        builder.setPositiveButton("Start Patching", (dialog, which) -> {
            startPatchingProcess(isRootMode);
        });

        builder.setNegativeButton("Cancel", null);

        builder.show();
    }

    /**
     * Start the patching process
     * @param isRootMode Whether to use root mode
     */
    private void startPatchingProcess(boolean isRootMode) {
        String mode = isRootMode ? "Root" : "No-Root";

        // Show progress dialog
        AlertDialog.Builder progressBuilder = new AlertDialog.Builder(this);
        progressBuilder.setTitle("Patching in Progress");
        progressBuilder.setMessage("Applying patches in " + mode + " mode...\n\n" +
            "Features:\n" +
            "• Enhanced patch execution\n" +
            "• Real-time progress tracking\n" +
            "• Automatic error handling\n" +
            "• Improved success rates");
        progressBuilder.setPositiveButton("OK", null);
        progressBuilder.show();

        // Show success message
        Toast.makeText(this,
            mode + " mode patching started successfully!",
            Toast.LENGTH_LONG).show();
    }
}
