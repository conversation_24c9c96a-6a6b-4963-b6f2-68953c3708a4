package com.bearmod.loader.ui.download;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bearmod.loader.R;
import com.bearmod.loader.api.GitHubApiService;
import com.bearmod.loader.databinding.ActivityDownloadBinding;
import com.bearmod.loader.download.DownloadManager;
import com.bearmod.loader.model.PatchRelease;

import java.io.File;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Objects;

/**
 * Download activity
 * Handles downloading patches from cloud or GitHub
 */
public class DownloadActivity extends AppCompatActivity {

    private ActivityDownloadBinding binding;
    private GitHubApiService gitHubApiService;
    private DownloadManager downloadManager;

    private PatchRelease selectedRelease;
    private ReleaseAdapter releaseAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Create a simple layout programmatically to avoid layout issues
            LinearLayout layout = new LinearLayout(this);
            layout.setOrientation(LinearLayout.VERTICAL);
            layout.setPadding(32, 32, 32, 32);

            // Add title
            TextView title = new TextView(this);
            title.setText("Enhanced Download System");
            title.setTextSize(24);
            title.setPadding(0, 0, 0, 32);
            layout.addView(title);

            // Add description
            TextView description = new TextView(this);
            description.setText("This is the new enhanced download system with APK/OBB options, progress tracking, and auto-installation features.");
            description.setPadding(0, 0, 0, 32);
            layout.addView(description);

            // Add demo button
            Button demoButton = new Button(this);
            demoButton.setText("Show Download Options Demo");
            demoButton.setOnClickListener(v -> showDownloadOptionsDemo());
            layout.addView(demoButton);

            // Add back button
            Button backButton = new Button(this);
            backButton.setText("Back to Main");
            backButton.setOnClickListener(v -> {
                Intent intent = new Intent(this, MainActivity.class);
                startActivity(intent);
                finish();
            });
            layout.addView(backButton);

            setContentView(layout);

        } catch (Exception e) {
            android.util.Log.e("DownloadActivity", "Error in onCreate: " + e.getMessage(), e);
            Toast.makeText(this, "Error loading download interface: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    /**
     * Load releases from GitHub
     */
    private void loadReleases() {
        // Show loading

        binding.progressLoading.setVisibility(View.VISIBLE);
        binding.rvReleases.setVisibility(View.GONE);
        binding.tvNoReleases.setVisibility(View.GONE);

        // Fetch releases
        gitHubApiService.fetchReleases(new GitHubApiService.ReleaseCallback() {
            @Override
            public void onSuccess(List<PatchRelease> releases) {
                // Hide loading
                binding.progressLoading.setVisibility(View.GONE);

                // Check if releases list is empty
                if (releases.isEmpty()) {
                    binding.tvNoReleases.setVisibility(View.VISIBLE);
                    binding.rvReleases.setVisibility(View.GONE);
                } else {
                    binding.tvNoReleases.setVisibility(View.GONE);
                    binding.rvReleases.setVisibility(View.VISIBLE);

                    // Update adapter
                    releaseAdapter.updateReleases(releases);
                }
            }

            @Override
            public void onError(String error) {
                // Hide loading

                binding.progressLoading.setVisibility(View.GONE);

                // Show error
                binding.tvNoReleases.setVisibility(View.VISIBLE);
                binding.tvNoReleases.setText(error);
                binding.rvReleases.setVisibility(View.GONE);

                // Show error message
                Toast.makeText(DownloadActivity.this, error, Toast.LENGTH_LONG).show();
            }
        });
    }

    /**
     * Set up download info
     * @param release Patch release
     */
    private void setupDownloadInfo(PatchRelease release) {
        // Format sizes
        DecimalFormat df = new DecimalFormat("#.#");
        String apkSize = df.format(release.getApkSizeMB());
        String obbSize = df.format(release.getObbSizeMB());
        String totalSize = df.format(release.getTotalSizeMB());

        // Set text
        binding.tvApkSize.setText("APK Size: " + apkSize + " MB");
        binding.tvObbSize.setText("OBB Size: " + obbSize + " MB");
        binding.tvTotalSize.setText("Total Size: " + totalSize + " MB");
    }

    /**
     * Show download options dialog
     * @param release Selected release
     */
    private void showDownloadOptionsDialog(PatchRelease release) {
        android.util.Log.d("DownloadActivity", "Showing download options dialog for: " + release.getName());

        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("Download Options");
        builder.setMessage("Choose what to download for " + release.getName() + ":");

        // Create options
        String[] options = {
            "APK Only (" + new DecimalFormat("#.#").format(release.getApkSizeMB()) + " MB)",
            "APK + OBB (" + new DecimalFormat("#.#").format(release.getTotalSizeMB()) + " MB)",
            "OBB Only (" + new DecimalFormat("#.#").format(release.getObbSizeMB()) + " MB)"
        };

        android.util.Log.d("DownloadActivity", "Download options: " + java.util.Arrays.toString(options));

        builder.setItems(options, (dialog, which) -> {
            android.util.Log.d("DownloadActivity", "User selected option: " + which + " (" + options[which] + ")");
            switch (which) {
                case 0: // APK Only
                    startDownload(release, DownloadType.APK_ONLY);
                    break;
                case 1: // APK + OBB
                    startDownload(release, DownloadType.APK_AND_OBB);
                    break;
                case 2: // OBB Only
                    startDownload(release, DownloadType.OBB_ONLY);
                    break;
            }
        });

        builder.setNegativeButton("Cancel", (dialog, which) -> {
            android.util.Log.d("DownloadActivity", "User cancelled download options dialog");
        });

        android.util.Log.d("DownloadActivity", "About to show download options dialog");
        builder.show();
        android.util.Log.d("DownloadActivity", "Download options dialog shown");
    }

    /**
     * Download type enum
     */
    private enum DownloadType {
        APK_ONLY,
        APK_AND_OBB,
        OBB_ONLY
    }

    /**
     * Start download with specified type
     * @param release Patch release to download
     * @param downloadType Type of download (APK only, APK+OBB, OBB only)
     */
    @SuppressLint("SetTextI18n")
    private void startDownload(PatchRelease release, DownloadType downloadType) {
        // Check if already downloading
        if (downloadManager.isDownloading()) {
            return;
        }

        // Update UI
        binding.cardDownloadProgress.setVisibility(View.VISIBLE);
        binding.btnDownload.setEnabled(false);
        binding.animationDownloadComplete.setVisibility(View.GONE);

        // Update status based on download type
        String statusText = "Preparing download...";
        switch (downloadType) {
            case APK_ONLY:
                statusText = "Preparing APK download...";
                break;
            case APK_AND_OBB:
                statusText = "Preparing APK + OBB download...";
                break;
            case OBB_ONLY:
                statusText = "Preparing OBB download...";
                break;
        }
        binding.tvDownloadStatus.setText(statusText);

        // Set progress listener with enhanced information
        downloadManager.setProgressListener((progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds) -> {
            // Format sizes and speed with one decimal place
            DecimalFormat df = new DecimalFormat("#.#");
            String downloadedSize = df.format(downloadedMB);
            String totalSize = df.format(totalSizeMB);
            String speed = df.format(speedMBps);

            // Format ETA
            @SuppressLint("DefaultLocale") String eta = String.format("%d:%02d", etaMinutes, etaSeconds);

            // Update progress bar
            binding.progressDownload.setProgress(progress);

            // Update download status with speed and ETA based on type
            String currentFile = "";
            switch (downloadType) {
                case APK_ONLY:
                    currentFile = "APK";
                    break;
                case APK_AND_OBB:
                    currentFile = progress < 50 ? "APK" : "OBB";
                    break;
                case OBB_ONLY:
                    currentFile = "OBB";
                    break;
            }

            String detailedStatus = "Downloading " + currentFile + ": " +
                    downloadedSize + " MB / " + totalSize + " MB (" +
                    speed + " MB/s, ETA: " + eta + ")";
            binding.tvDownloadStatus.setText(detailedStatus);

            // Update percentage
            binding.tvDownloadPercentage.setText(progress + "%");
        });

        // Start download based on type
        startActualDownload(release, downloadType);
    }

    /**
     * Start the actual download process
     * @param release Patch release to download
     * @param downloadType Type of download
     */
    private void startActualDownload(PatchRelease release, DownloadType downloadType) {
        // For now, use the existing download manager but we'll enhance it later
        downloadManager.downloadGameFiles(release, downloadType, new DownloadManager.DownloadListener() {
            @Override
            public void onSuccess(File downloadedFile) {
                // Update UI
                binding.btnDownload.setEnabled(true);
                binding.animationDownloadComplete.setVisibility(View.VISIBLE);

                // Show success message with auto-install option for APK downloads
                if (downloadType == DownloadType.APK_ONLY || downloadType == DownloadType.APK_AND_OBB) {
                    showInstallationDialog(downloadedFile, downloadType);
                } else {
                    Toast.makeText(DownloadActivity.this,
                            "Download complete: " + downloadedFile.getAbsolutePath(),
                            Toast.LENGTH_LONG).show();
                }
            }

            @Override
            public void onError(String error) {
                // Update UI
                binding.btnDownload.setEnabled(true);
                binding.cardDownloadProgress.setVisibility(View.GONE);

                // Show error message
                Toast.makeText(DownloadActivity.this,
                        "Download failed: " + error,
                        Toast.LENGTH_LONG).show();
            }
        });
    }

    /**
     * Show installation dialog after successful APK download
     * @param downloadedFile Downloaded file
     * @param downloadType Type of download
     */
    private void showInstallationDialog(File downloadedFile, DownloadType downloadType) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("Download Complete");

        String message = "Download completed successfully!\n\nFile location: " + downloadedFile.getAbsolutePath();
        if (downloadType == DownloadType.APK_ONLY || downloadType == DownloadType.APK_AND_OBB) {
            message += "\n\nWould you like to install the APK now?";
        }

        builder.setMessage(message);

        if (downloadType == DownloadType.APK_ONLY || downloadType == DownloadType.APK_AND_OBB) {
            builder.setPositiveButton("Install Now", (dialog, which) -> {
                installApk(downloadedFile);
            });
            builder.setNegativeButton("Later", null);
        } else {
            builder.setPositiveButton("OK", null);
        }

        builder.show();
    }

    /**
     * Install APK file
     * @param apkFile APK file to install
     */
    private void installApk(File apkFile) {
        try {
            // Check if we have permission to install packages
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                if (!getPackageManager().canRequestPackageInstalls()) {
                    // Request permission to install packages
                    Intent intent = new Intent(android.provider.Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES);
                    intent.setData(android.net.Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                    Toast.makeText(this, "Please enable 'Install unknown apps' permission and try again", Toast.LENGTH_LONG).show();
                    return;
                }
            }

            Intent intent = new Intent(Intent.ACTION_VIEW);
            android.net.Uri apkUri;

            // Use FileProvider for Android 7.0+ (API 24+)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
                apkUri = androidx.core.content.FileProvider.getUriForFile(
                        this,
                        getPackageName() + ".fileprovider",
                        apkFile
                );
                intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            } else {
                apkUri = android.net.Uri.fromFile(apkFile);
            }

            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(this, "Error installing APK: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * Cancel download
     */
    private void cancelDownload() {
        // Check if downloading
        if (!downloadManager.isDownloading()) {
            return;
        }

        // Cancel download
        downloadManager.cancelDownload();

        // Update UI
        binding.cardDownloadProgress.setVisibility(View.GONE);
        binding.btnDownload.setEnabled(true);

        // Show message
        Toast.makeText(this, R.string.download_cancelled, Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onSupportNavigateUp() {
        // Handle back button
        onBackPressed();
        return true;
    }

    @Override
    public void onBackPressed() {
        // Check if downloading
        if (downloadManager.isDownloading()) {
            // Show confirmation dialog
            new androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle(R.string.warning)
                    .setMessage(R.string.download_in_progress)
                    .setPositiveButton(R.string.cancel_download, (dialog, which) -> {
                        // Cancel download
                        cancelDownload();

                        // Finish activity
                        super.onBackPressed();
                    })
                    .setNegativeButton(R.string.continue_download, null)
                    .show();
        } else {
            // Finish activity
            super.onBackPressed();
        }
    }
}
