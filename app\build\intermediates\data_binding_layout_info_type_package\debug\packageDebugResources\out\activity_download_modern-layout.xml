<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_download_modern" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_download_modern.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_download_modern_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="340" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="121" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="26" endOffset="54"/></Target><Target id="@+id/search_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="29" startOffset="8" endLine="56" endOffset="63"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="47" startOffset="12" endLine="54" endOffset="79"/></Target><Target id="@+id/chip_group_filters" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="65" startOffset="12" endLine="117" endOffset="56"/></Target><Target id="@+id/chip_filter_all" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="74" startOffset="16" endLine="83" endOffset="57"/></Target><Target id="@+id/chip_filter_latest" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="85" startOffset="16" endLine="93" endOffset="57"/></Target><Target id="@+id/chip_sort_date" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="95" startOffset="16" endLine="104" endOffset="57"/></Target><Target id="@+id/chip_sort_size" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="106" startOffset="16" endLine="115" endOffset="57"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="124" startOffset="4" endLine="201" endOffset="59"/></Target><Target id="@+id/progress_loading" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="140" startOffset="16" endLine="148" endOffset="68"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="151" startOffset="16" endLine="185" endOffset="30"/></Target><Target id="@+id/rv_releases" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="188" startOffset="16" endLine="195" endOffset="59"/></Target><Target id="@+id/card_download_info" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="204" startOffset="4" endLine="338" endOffset="55"/></Target><Target id="@+id/tv_apk_size" view="TextView"><Expressions/><location startLine="251" startOffset="20" endLine="259" endOffset="56"/></Target><Target id="@+id/tv_obb_size" view="TextView"><Expressions/><location startLine="276" startOffset="20" endLine="284" endOffset="57"/></Target><Target id="@+id/tv_total_size" view="TextView"><Expressions/><location startLine="313" startOffset="20" endLine="322" endOffset="59"/></Target><Target id="@+id/btn_download" view="Button"><Expressions/><location startLine="328" startOffset="12" endLine="334" endOffset="57"/></Target></Targets></Layout>