package com.bearmod.loader.security;

import android.content.Context;
import android.util.Log;

/**
 * Native security manager for enhanced signature verification
 * Uses JNI to perform signature checks at native level for better security
 */
public class NativeSecurityManager {
    
    private static final String TAG = "NativeSecurityManager";
    private static boolean isLibraryLoaded = false;
    
    static {
        try {
            System.loadLibrary("signature_verifier");
            isLibraryLoaded = true;
            Log.d(TAG, "Native security library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native security library", e);
            isLibraryLoaded = false;
        }
    }
    
    /**
     * Check if native library is available
     * @return true if native library is loaded, false otherwise
     */
    public static boolean isNativeLibraryAvailable() {
        return isLibraryLoaded;
    }
    
    /**
     * Verify loader app signature using native code
     * @param context Application context
     * @return true if signature is valid, false otherwise
     */
    public static boolean verifyLoaderSignature(Context context) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, falling back to Java verification");
            return AppSecurityManager.verifyAppSignature(context);
        }
        
        try {
            return nativeVerifyLoaderSignature(context);
        } catch (Exception e) {
            Log.e(TAG, "Native loader signature verification failed", e);
            // Fallback to Java implementation
            return AppSecurityManager.verifyAppSignature(context);
        }
    }
    
    /**
     * Verify PUBG Mobile app signature using native code
     * @param context Application context
     * @param packageName PUBG Mobile package name
     * @return true if signature is valid, false otherwise
     */
    public static boolean verifyPubgSignature(Context context, String packageName) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, falling back to Java verification");
            return AppSecurityManager.verifyPubgSignature(context, packageName);
        }
        
        try {
            return nativeVerifyPubgSignature(context, packageName);
        } catch (Exception e) {
            Log.e(TAG, "Native PUBG signature verification failed", e);
            // Fallback to Java implementation
            return AppSecurityManager.verifyPubgSignature(context, packageName);
        }
    }
    
    /**
     * Verify shared certificate between loader and PUBG Mobile
     * @param context Application context
     * @param pubgPackageName PUBG Mobile package name
     * @return true if both apps have trusted signatures, false otherwise
     */
    public static boolean verifySharedCertificate(Context context, String pubgPackageName) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, using Java verification");
            return AppSecurityManager.verifyAppSignature(context) && 
                   AppSecurityManager.verifyPubgSignature(context, pubgPackageName);
        }
        
        try {
            return nativeVerifySharedCertificate(context, pubgPackageName);
        } catch (Exception e) {
            Log.e(TAG, "Native shared certificate verification failed", e);
            // Fallback to Java implementation
            return AppSecurityManager.verifyAppSignature(context) && 
                   AppSecurityManager.verifyPubgSignature(context, pubgPackageName);
        }
    }
    
    /**
     * Get signature hash for debugging purposes
     * @param context Application context
     * @param packageName Package name to get signature for
     * @return Signature hash or null if failed
     */
    public static String getSignatureHash(Context context, String packageName) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, using Java implementation");
            try {
                return AppSecurityManager.getAppSignature(context, packageName);
            } catch (Exception e) {
                Log.e(TAG, "Java signature hash retrieval failed", e);
                return null;
            }
        }
        
        try {
            return nativeGetSignatureHash(context, packageName);
        } catch (Exception e) {
            Log.e(TAG, "Native signature hash retrieval failed", e);
            return null;
        }
    }
    
    /**
     * Perform comprehensive anti-tampering check
     * @param context Application context
     * @return true if app is not tampered with, false otherwise
     */
    public static boolean performAntiTamperingCheck(Context context) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available, using basic Java checks");
            AppSecurityManager.SecurityCheckResult result = AppSecurityManager.performSecurityCheck(context);
            return result.isSecure;
        }
        
        try {
            return nativePerformAntiTamperingCheck(context);
        } catch (Exception e) {
            Log.e(TAG, "Native anti-tampering check failed", e);
            // Fallback to Java implementation
            AppSecurityManager.SecurityCheckResult result = AppSecurityManager.performSecurityCheck(context);
            return result.isSecure;
        }
    }
    
    /**
     * Enhanced security verification for downloads
     * @param context Application context
     * @param pubgPackageName PUBG Mobile package name
     * @return Security verification result
     */
    public static SecurityVerificationResult performEnhancedSecurityCheck(Context context, String pubgPackageName) {
        Log.d(TAG, "Performing enhanced security check");
        
        // Check if native library is available
        if (!isLibraryLoaded) {
            return new SecurityVerificationResult(false, 
                "Native security library not available", 
                "Enhanced security features require native library");
        }
        
        // Perform anti-tampering check
        if (!performAntiTamperingCheck(context)) {
            return new SecurityVerificationResult(false,
                "Anti-tampering check failed",
                "App may have been modified or is running in an unsafe environment");
        }
        
        // Verify loader signature
        if (!verifyLoaderSignature(context)) {
            return new SecurityVerificationResult(false,
                "Loader signature verification failed",
                "App signature does not match trusted signatures");
        }
        
        // Verify PUBG signature if installed
        if (AppSecurityManager.isPackageInstalled(context, pubgPackageName)) {
            if (!verifyPubgSignature(context, pubgPackageName)) {
                return new SecurityVerificationResult(false,
                    "PUBG Mobile signature verification failed",
                    "Installed PUBG Mobile app signature is not trusted");
            }
        }
        
        return new SecurityVerificationResult(true, "Enhanced security check passed");
    }
    
    /**
     * Log signature information for debugging
     * @param context Application context
     */
    public static void logSignatureInformation(Context context) {
        Log.d(TAG, "=== Native Signature Information ===");
        
        // Log loader signature
        String loaderHash = getSignatureHash(context, context.getPackageName());
        Log.d(TAG, "Loader signature hash: " + loaderHash);
        
        // Log PUBG signatures if installed
        String[] pubgPackages = {
            "com.tencent.ig",
            "com.pubg.krmobile",
            "com.rekoo.pubgm", 
            "com.vng.pubgmobile"
        };
        
        for (String pkg : pubgPackages) {
            if (AppSecurityManager.isPackageInstalled(context, pkg)) {
                String hash = getSignatureHash(context, pkg);
                Log.d(TAG, "PUBG " + pkg + " signature hash: " + hash);
            }
        }
        
        Log.d(TAG, "=== End Native Signature Information ===");
    }
    
    /**
     * Detect Frida and other hooking frameworks
     * @return true if Frida/hooking detected, false otherwise
     */
    public static boolean detectFrida() {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available for Frida detection");
            return false;
        }

        try {
            return nativeDetectFrida();
        } catch (Exception e) {
            Log.e(TAG, "Frida detection failed", e);
            return false;
        }
    }

    /**
     * Start continuous anti-hook monitoring
     */
    public static void startAntiHookMonitoring() {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available for anti-hook monitoring");
            return;
        }

        try {
            nativeStartAntiHookMonitoring();
            Log.d(TAG, "Anti-hook monitoring started");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start anti-hook monitoring", e);
        }
    }

    /**
     * Verify application integrity (includes anti-hooking checks)
     * @param context Application context
     * @return true if integrity is intact, false otherwise
     */
    public static boolean verifyIntegrity(Context context) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available for integrity verification");
            return AppSecurityManager.performSecurityCheck(context).isSecure;
        }

        try {
            return nativeVerifyIntegrity(context);
        } catch (Exception e) {
            Log.e(TAG, "Integrity verification failed", e);
            return false;
        }
    }

    /**
     * Enhanced security verification with anti-hooking
     * @param context Application context
     * @param pubgPackageName PUBG Mobile package name
     * @return Enhanced security verification result
     */
    public static SecurityVerificationResult performEnhancedSecurityCheckWithAntiHook(Context context, String pubgPackageName) {
        Log.d(TAG, "Performing enhanced security check with anti-hooking");

        // Check if native library is available
        if (!isLibraryLoaded) {
            return new SecurityVerificationResult(false,
                "Native security library not available",
                "Enhanced security features require native library");
        }

        // Detect Frida/hooking first
        if (detectFrida()) {
            return new SecurityVerificationResult(false,
                "Hooking framework detected",
                "Frida or other hooking framework is active. Please disable debugging tools.");
        }

        // Verify application integrity
        if (!verifyIntegrity(context)) {
            return new SecurityVerificationResult(false,
                "Application integrity check failed",
                "App may have been modified or is running in an unsafe environment");
        }

        // Perform standard security checks
        SecurityVerificationResult standardResult = performEnhancedSecurityCheck(context, pubgPackageName);
        if (!standardResult.isSecure) {
            return standardResult;
        }

        // Start continuous monitoring
        startAntiHookMonitoring();

        return new SecurityVerificationResult(true, "Enhanced security check with anti-hooking passed");
    }

    // Native method declarations
    private static native boolean nativeVerifyLoaderSignature(Context context);
    private static native boolean nativeVerifyPubgSignature(Context context, String packageName);
    private static native boolean nativeVerifySharedCertificate(Context context, String pubgPackageName);
    private static native String nativeGetSignatureHash(Context context, String packageName);
    private static native boolean nativePerformAntiTamperingCheck(Context context);

    // Anti-hooking native methods
    private static native boolean nativeDetectFrida();
    private static native void nativeStartAntiHookMonitoring();
    private static native boolean nativeVerifyIntegrity(Context context);
    
    /**
     * Security verification result
     */
    public static class SecurityVerificationResult {
        public final boolean isSecure;
        public final String message;
        public final String details;
        
        public SecurityVerificationResult(boolean isSecure, String message, String details) {
            this.isSecure = isSecure;
            this.message = message;
            this.details = details;
        }
        
        public SecurityVerificationResult(boolean isSecure, String message) {
            this(isSecure, message, null);
        }
        
        @Override
        public String toString() {
            return "SecurityVerificationResult{" +
                    "isSecure=" + isSecure +
                    ", message='" + message + '\'' +
                    ", details='" + details + '\'' +
                    '}';
        }
    }
}
