@echo off
"D:\\AndroidBuildEnv\\SDK\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HD:\\Augment_Code\\BearMod-Loader\\nativelib\\src\\main\\cpp" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973" ^
  "-DCMAKE_ANDROID_NDK=D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973" ^
  "-DCMAKE_TOOLCHAIN_FILE=D:\\AndroidBuildEnv\\SDK\\ndk\\27.0.12077973\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=D:\\AndroidBuildEnv\\SDK\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_CXX_FLAGS=-std=c++17" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx\\Debug\\6z2l3s6b\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\Augment_Code\\BearMod-Loader\\nativelib\\build\\intermediates\\cxx\\Debug\\6z2l3s6b\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-BD:\\Augment_Code\\BearMod-Loader\\nativelib\\.cxx\\Debug\\6z2l3s6b\\armeabi-v7a" ^
  -GNinja
