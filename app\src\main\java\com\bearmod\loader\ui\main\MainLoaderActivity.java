package com.bearmod.loader.ui.main;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bearmod.loader.R;
import com.bearmod.loader.databinding.ActivityMainLoaderBinding;
import com.bearmod.loader.download.DownloadManager;
import com.bearmod.loader.ui.settings.SettingsActivity;
import com.google.android.material.bottomnavigation.BottomNavigationView;

import java.util.ArrayList;
import java.util.List;

public class MainLoaderActivity extends AppCompatActivity implements AppVersionAdapter.OnAppVersionClickListener {

    private ActivityMainLoaderBinding binding;
    private AppVersionAdapter appVersionAdapter;
    private DownloadManager.DownloadProgressListener progressListener;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Navigate directly to modern download interface
        Intent intent = new Intent(this, com.bearmod.loader.ui.download.ModernDownloadActivity.class);
        startActivity(intent);
        finish();
    }

    @Override
    public void onAppVersionClick(AppVersion appVersion) {
        // Show APK+OBB download options for the selected app
        showDownloadOptionsDialog(appVersion);
    }

    /**
     * Show APK+OBB download options dialog
     * @param appVersion Selected app version
     */
    private void showDownloadOptionsDialog(AppVersion appVersion) {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("Download " + appVersion.getName());
        builder.setMessage("Choose download type:");

        // APK+OBB download options
        String[] options = {
            "APK Only (120.5 MB)\nGame application file with auto-installation",
            "APK + OBB (1100.8 MB)\nComplete game package with data files",
            "OBB Only (980.3 MB)\nGame data files for existing installation"
        };

        builder.setItems(options, (dialog, which) -> {
            DownloadManager.DownloadType downloadType;
            switch (which) {
                case 0:
                    downloadType = DownloadManager.DownloadType.APK_ONLY;
                    break;
                case 1:
                    downloadType = DownloadManager.DownloadType.APK_AND_OBB;
                    break;
                case 2:
                    downloadType = DownloadManager.DownloadType.OBB_ONLY;
                    break;
                default:
                    return;
            }

            startDownload(appVersion, downloadType);
        });

        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    /**
     * Start download for selected app and type
     * @param appVersion Selected app version
     * @param downloadType Download type
     */
    private void startDownload(AppVersion appVersion, DownloadManager.DownloadType downloadType) {
        android.widget.Toast.makeText(this,
            "Starting " + downloadType.name() + " download for " + appVersion.getName(),
            android.widget.Toast.LENGTH_SHORT).show();

        // Start enhanced download with progress tracking
        DownloadManager.getInstance().initialize(this);

        // Set up progress listener for inline progress bars
        setupProgressListener(appVersion.getName());

        // Start download with enhanced progress dialog
        DownloadManager.getInstance().downloadGameFilesEnhanced(this, appVersion.getName(), downloadType,
            new DownloadManager.EnhancedDownloadListener() {
                @Override
                public void onSuccess(String filePath) {
                    // Hide inline progress
                    appVersionAdapter.hideDownloadProgress(appVersion.getName(), true);

                    android.widget.Toast.makeText(MainLoaderActivity.this,
                        "Download completed: " + new java.io.File(filePath).getName(),
                        android.widget.Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onError(String error) {
                    // Hide inline progress
                    appVersionAdapter.hideDownloadProgress(appVersion.getName(), false);

                    android.widget.Toast.makeText(MainLoaderActivity.this,
                        "Download failed: " + error,
                        android.widget.Toast.LENGTH_LONG).show();
                }
            });
    }

    /**
     * Set up progress listener for inline progress tracking
     * @param appName App name being downloaded
     */
    private void setupProgressListener(String appName) {
        progressListener = new DownloadManager.DownloadProgressListener() {
            @Override
            public void onProgressUpdate(int progress, double downloadedMB, double totalSizeMB,
                                       double speedMBps, int etaMinutes, int etaSeconds) {
                runOnUiThread(() -> {
                    // Update inline progress in the adapter
                    appVersionAdapter.showDownloadProgress(appName, progress, downloadedMB,
                        totalSizeMB, speedMBps, etaMinutes, etaSeconds);
                });
            }
        };

        // Set the progress listener on DownloadManager
        DownloadManager.getInstance().setProgressListener(progressListener);
    }

    private boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.nav_home) {
            return true;
        } else if (itemId == R.id.nav_downloads) {
            // Show APK+OBB download info
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("APK+OBB Download System")
                .setMessage("Click on any game to access download options:\n\n" +
                           "• APK Only downloads\n" +
                           "• APK + OBB packages\n" +
                           "• OBB data files\n" +
                           "• Progress tracking\n" +
                           "• Auto-installation")
                .setPositiveButton("OK", null)
                .show();
            return true;
        } else if (itemId == R.id.nav_settings) {
            startActivity(new Intent(this, SettingsActivity.class));
            return true;
        }
        return false;
    }
} 