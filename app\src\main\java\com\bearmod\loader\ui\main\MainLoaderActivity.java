package com.bearmod.loader.ui.main;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.bearmod.loader.R;
import com.bearmod.loader.databinding.ActivityMainLoaderBinding;
import com.bearmod.loader.ui.patch.PatchExecutionActivity;

import com.bearmod.loader.ui.settings.SettingsActivity;
import com.google.android.material.bottomnavigation.BottomNavigationView;

import java.util.ArrayList;
import java.util.List;

public class MainLoaderActivity extends AppCompatActivity implements AppVersionAdapter.OnAppVersionClickListener {
    
    private ActivityMainLoaderBinding binding;
    private AppVersionAdapter appVersionAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            // Enable edge-to-edge
            WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

            // Initialize view binding
            binding = ActivityMainLoaderBinding.inflate(getLayoutInflater());
            setContentView(binding.getRoot());

            // Set up RecyclerView
            binding.rvAppVersions.setLayoutManager(new LinearLayoutManager(this));
            appVersionAdapter = new AppVersionAdapter(this, this);
            binding.rvAppVersions.setAdapter(appVersionAdapter);

            // Add sample data with error handling
            List<AppVersion> versions = new ArrayList<>();
            try {
                versions.add(new AppVersion(
                    R.drawable.ic_pubg_global_vector,
                    "PUBG MOBILE GL",
                    "Version 3.8.0",
                    "Global version patch"
                ));
                versions.add(new AppVersion(
                    R.drawable.ic_pubg_kr_vector,
                    "PUBG MOBILE KR",
                    "Version 3.8.0",
                    "Korean version patch"
                ));
                versions.add(new AppVersion(
                    R.drawable.ic_pubg_tw_vector,
                    "PUBG MOBILE TW",
                    "Version 3.8.0",
                    "Taiwan version patch"
                ));
                versions.add(new AppVersion(
                    R.drawable.ic_pubg_vn_vector,
                    "PUBG MOBILE VN",
                    "Version 3.8.0",
                    "Vietnam version patch"
                ));
            } catch (Exception e) {
                android.util.Log.e("MainLoaderActivity", "Error creating app versions: " + e.getMessage(), e);
                // Add fallback data
                versions.add(new AppVersion(
                    android.R.drawable.ic_menu_gallery,
                    "PUBG MOBILE GL",
                    "Version 3.8.0",
                    "Global version patch"
                ));
            }

            appVersionAdapter.setAppVersions(versions);

            // Set up bottom navigation
            binding.bottomNavigation.setOnItemSelectedListener(this::onNavigationItemSelected);

        } catch (Exception e) {
            android.util.Log.e("MainLoaderActivity", "Error in onCreate: " + e.getMessage(), e);
            // Show error and finish activity
            android.widget.Toast.makeText(this, "Error loading main interface: " + e.getMessage(), android.widget.Toast.LENGTH_LONG).show();
            finish();
        }
    }

    @Override
    public void onAppVersionClick(AppVersion appVersion) {
        // Navigate to patch execution activity
        Intent intent = new Intent(this, PatchExecutionActivity.class);
        intent.putExtra("app_name", appVersion.getName());
        intent.putExtra("app_version", appVersion.getVersion());
        startActivity(intent);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
    }

    private boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.nav_home) {
            return true;
        } else if (itemId == R.id.nav_downloads) {
            // Show enhanced download info instead of navigating to separate activity
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Enhanced Download System")
                .setMessage("Download features are integrated into the main interface:\n\n" +
                           "• Click download buttons on patches for APK/OBB options\n" +
                           "• Real-time progress tracking\n" +
                           "• Auto-installation support\n" +
                           "• Multiple download types available")
                .setPositiveButton("OK", null)
                .show();
            return true;
        } else if (itemId == R.id.nav_settings) {
            startActivity(new Intent(this, SettingsActivity.class));
            return true;
        }
        return false;
    }
} 