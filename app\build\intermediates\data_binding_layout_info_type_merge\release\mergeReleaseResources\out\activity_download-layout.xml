<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_download" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\activity_download.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_download_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="412" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="119" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="54"/></Target><Target id="@+id/search_layout" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="28" startOffset="8" endLine="55" endOffset="63"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="79"/></Target><Target id="@+id/chip_group_filters" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="63" startOffset="12" endLine="115" endOffset="56"/></Target><Target id="@+id/chip_filter_all" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="72" startOffset="16" endLine="81" endOffset="57"/></Target><Target id="@+id/chip_filter_latest" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="83" startOffset="16" endLine="91" endOffset="57"/></Target><Target id="@+id/chip_sort_date" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="93" startOffset="16" endLine="102" endOffset="57"/></Target><Target id="@+id/chip_sort_size" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="104" startOffset="16" endLine="113" endOffset="57"/></Target><Target id="@+id/swipe_refresh" view="androidx.swiperefreshlayout.widget.SwipeRefreshLayout"><Expressions/><location startLine="121" startOffset="4" endLine="195" endOffset="59"/></Target><Target id="@+id/progressLoading" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="136" startOffset="16" endLine="144" endOffset="68"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="146" startOffset="16" endLine="180" endOffset="30"/></Target><Target id="@+id/rvReleases" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="182" startOffset="16" endLine="189" endOffset="59"/></Target><Target id="@+id/cardDownloadInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="197" startOffset="4" endLine="329" endOffset="55"/></Target><Target id="@+id/tvApkSize" view="TextView"><Expressions/><location startLine="243" startOffset="20" endLine="251" endOffset="56"/></Target><Target id="@+id/tvObbSize" view="TextView"><Expressions/><location startLine="268" startOffset="20" endLine="276" endOffset="57"/></Target><Target id="@+id/tvTotalSize" view="TextView"><Expressions/><location startLine="304" startOffset="20" endLine="313" endOffset="59"/></Target><Target id="@+id/btnDownload" view="Button"><Expressions/><location startLine="319" startOffset="12" endLine="325" endOffset="57"/></Target><Target id="@+id/cardDownloadProgress" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="332" startOffset="4" endLine="398" endOffset="55"/></Target><Target id="@+id/tv_download_status" view="TextView"><Expressions/><location startLine="351" startOffset="12" endLine="357" endOffset="57"/></Target><Target id="@+id/progressDownload" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="359" startOffset="12" endLine="366" endOffset="47"/></Target><Target id="@+id/tv_download_percentage" view="TextView"><Expressions/><location startLine="368" startOffset="12" endLine="376" endOffset="59"/></Target><Target id="@+id/animationDownloadComplete" view="com.airbnb.lottie.LottieAnimationView"><Expressions/><location startLine="378" startOffset="12" endLine="387" endOffset="62"/></Target><Target id="@+id/btnCancelDownload" view="Button"><Expressions/><location startLine="389" startOffset="12" endLine="395" endOffset="56"/></Target><Target id="@+id/tvNoReleases" view="TextView"><Expressions/><location startLine="401" startOffset="4" endLine="410" endOffset="35"/></Target></Targets></Layout>