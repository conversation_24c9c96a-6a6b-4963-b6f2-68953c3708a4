{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-59:/values-sk/values-sk.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\55919508f6a916749b6624a060d17abd\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "61,62,63,64,65,66,67,192", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4426,4522,4624,4725,4823,4933,5041,14944", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "4517,4619,4720,4818,4928,5036,5158,15040"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\a4fb1c2e1fb11023fe27b3d0a699c4d7\\transformed\\exoplayer-ui-2.19.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3748,3814,3866,3930,4008,4086", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3743,3809,3861,3925,4003,4081,4135"}, "to": {"startLines": "2,11,17,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,719,5590,5671,5751,5833,5936,6035,6114,6179,6270,6364,6434,6500,6565,6642,6764,6881,7002,7076,7158,7231,7313,7413,7512,7579,8315,8368,8426,8474,8535,8607,8681,8744,8817,8882,8942,9007,9071,9137,9189,9253,9331,9409", "endLines": "10,16,22,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "383,714,1028,5666,5746,5828,5931,6030,6109,6174,6265,6359,6429,6495,6560,6637,6759,6876,6997,7071,7153,7226,7308,7408,7507,7574,7639,8363,8421,8469,8530,8602,8676,8739,8812,8877,8937,9002,9066,9132,9184,9248,9326,9404,9458"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\377aab835c73943f35a2dc72ba361cea\\transformed\\material-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1143,1208,1307,1383,1448,1538,1602,1668,1722,1791,1851,1905,2022,2082,2144,2198,2270,2400,2487,2567,2663,2747,2839,2978,3047,3125,3256,3344,3424,3478,3529,3595,3667,3744,3815,3897,3969,4046,4119,4190,4295,4383,4455,4547,4643,4717,4791,4887,4939,5021,5088,5175,5262,5324,5388,5451,5519,5625,5732,5830,5947,6005,6060,6139,6222,6297", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "373,448,523,601,693,776,868,996,1077,1138,1203,1302,1378,1443,1533,1597,1663,1717,1786,1846,1900,2017,2077,2139,2193,2265,2395,2482,2562,2658,2742,2834,2973,3042,3120,3251,3339,3419,3473,3524,3590,3662,3739,3810,3892,3964,4041,4114,4185,4290,4378,4450,4542,4638,4712,4786,4882,4934,5016,5083,5170,5257,5319,5383,5446,5514,5620,5727,5825,5942,6000,6055,6134,6217,6292,6368"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,71,72,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,187,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1033,4023,4098,4173,4251,4343,5163,5255,5383,5464,5525,9463,9562,9638,9703,9793,9857,9923,9977,10046,10106,10160,10277,10337,10399,10453,10525,10655,10742,10822,10918,11002,11094,11233,11302,11380,11511,11599,11679,11733,11784,11850,11922,11999,12070,12152,12224,12301,12374,12445,12550,12638,12710,12802,12898,12972,13046,13142,13194,13276,13343,13430,13517,13579,13643,13706,13774,13880,13987,14085,14202,14260,14548,14710,14793,14868", "endLines": "28,56,57,58,59,60,68,69,70,71,72,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,187,189,190,191", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "1306,4093,4168,4246,4338,4421,5250,5378,5459,5520,5585,9557,9633,9698,9788,9852,9918,9972,10041,10101,10155,10272,10332,10394,10448,10520,10650,10737,10817,10913,10997,11089,11228,11297,11375,11506,11594,11674,11728,11779,11845,11917,11994,12065,12147,12219,12296,12369,12440,12545,12633,12705,12797,12893,12967,13041,13137,13189,13271,13338,13425,13512,13574,13638,13701,13769,13875,13982,14080,14197,14255,14310,14622,14788,14863,14939"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\8d1253ec0fc93acd53c5fd46d4f60417\\transformed\\navigation-ui-2.9.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,126", "endOffsets": "156,283"}, "to": {"startLines": "185,186", "startColumns": "4,4", "startOffsets": "14315,14421", "endColumns": "105,126", "endOffsets": "14416,14543"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c331959512d9a33669ba0caa36c41fa5\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1311,1418,1519,1630,1716,1824,1942,2021,2098,2189,2282,2380,2474,2574,2667,2762,2860,2951,3042,3126,3231,3339,3438,3544,3656,3759,3925,14627", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1413,1514,1625,1711,1819,1937,2016,2093,2184,2277,2375,2469,2569,2662,2757,2855,2946,3037,3121,3226,3334,3433,3539,3651,3754,3920,4018,14705"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\5ff2befc40d2f1ed1bc48bc5bd6bf3b2\\transformed\\exoplayer-core-2.19.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7644,7721,7783,7847,7918,7995,8069,8153,8235", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "7716,7778,7842,7913,7990,8064,8148,8230,8310"}}]}]}