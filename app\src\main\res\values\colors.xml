<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Original colors (keeping for reference) -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>

    <!-- Base colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFF</color>

    <!-- Status colors -->
    <color name="success">#4CAF50</color>
    <color name="error">#F44336</color>

    <!-- App theme colors -->
    <color name="primary">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="primary_container">#E3F2FD</color>
    <color name="on_primary">#FFFFFF</color>

    <!-- Secondary colors -->
    <color name="secondary">#03DAC5</color>
    <color name="secondary_dark">#018786</color>
    <color name="secondary_light">#B2EBF2</color>
    <color name="secondary_container">#E0F7FA</color>
    <color name="on_secondary">#000000</color>

    <!-- Accent colors -->
    <color name="accent">#FF4081</color>
    <color name="accent_dark">#C51162</color>
    <color name="accent_light">#FF80AB</color>

    <!-- Surface colors -->
    <color name="surface">#FFFFFF</color>
    <color name="surface_variant">#F5F5F5</color>
    <color name="surface_container">#FAFAFA</color>
    <color name="surface_container_low">#F8F8F8</color>
    <color name="surface_container_lowest">#FFFFFF</color>
    <color name="surface_container_high">#EEEEEE</color>
    <color name="surface_container_highest">#E0E0E0</color>
    <color name="on_surface">#000000</color>

    <!-- Outline colors -->
    <color name="outline">#CCCCCC</color>
    <color name="outline_variant">#E0E0E0</color>

    <!-- Background colors -->
    <color name="background">#121212</color>
    <color name="background_light">#1E1E1E</color>
    <color name="on_background">#FFFFFF</color>

    <!-- Card colors -->
    <color name="card_background">#1F1F1F</color>
    <color name="card_bg">#1F1F1F</color>

    <!-- Text colors -->
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3FFFFFF</color>
    <color name="text_hint">#80FFFFFF</color>

    <!-- Common colors -->
    <color name="transparent">#00000000</color>

    <!-- Status colors -->
    <color name="warning">#FFC107</color>
    <color name="info">#2196F3</color>

    <!-- Specific UI element colors -->
    <color name="divider">#E0E0E0</color>
    <color name="ripple">#33FFFFFF</color>
    <color name="shimmer_color">#DDDDDD</color>
    <color name="progress_background">#424242</color>
</resources>