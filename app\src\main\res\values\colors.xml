<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Original colors removed - using modern GitHub/Discord palette -->

    <!-- Base colors -->
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFF</color>

    <!-- Status colors -->
    <color name="success">#4CAF50</color>

    <!-- App theme colors -->
    <color name="primary">#2196F3</color>
    <color name="primary_dark">#1976D2</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="primary_container">#E3F2FD</color>
    <color name="on_primary">#FFFFFF</color>

    <!-- Secondary colors -->
    <color name="secondary">#03DAC5</color>
    <color name="secondary_dark">#018786</color>
    <color name="secondary_light">#B2EBF2</color>
    <color name="secondary_container">#E0F7FA</color>
    <color name="on_secondary">#000000</color>

    <!-- Accent colors -->
    <color name="accent">#FF4081</color>
    <color name="accent_dark">#C51162</color>
    <color name="accent_light">#FF80AB</color>

    <!-- Surface colors -->
    <color name="surface">#121212</color>
    <color name="surface_variant">#1E1E1E</color>
    <color name="surface_container">#1F1F1F</color>
    <color name="surface_container_low">#1A1A1A</color>
    <color name="surface_container_high">#2D2D2D</color>
    <color name="on_surface">#FFFFFF</color>

    <!-- Outline colors -->
    <color name="outline_variant">#E0E0E0</color>

    <!-- Background colors -->
    <color name="background">#121212</color>
    <color name="background_light">#1E1E1E</color>

    <!-- Card colors -->
    <color name="card_background">#1F1F1F</color>

    <!-- Text colors -->
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B3FFFFFF</color>

    <!-- Status colors -->
    <color name="warning">#FFC107</color>
    <color name="info">#2196F3</color>
    <color name="teal_200">#FF03DAC5</color>

    <!-- Specific UI element colors -->
    <color name="divider">#E0E0E0</color>
    <color name="ripple">#33FFFFFF</color>
</resources>