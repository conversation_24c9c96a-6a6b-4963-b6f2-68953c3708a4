<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.10.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.10.0)" variant="all" version="8.10.0">

    <issue
        id="MissingPermission"
        message="Missing permissions required by Build.getSerial: android.permission.READ_PRIVILEGED_PHONE_STATE"
        errorLine1="                    serial = android.os.Build.getSerial();"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthManager.java"
            line="90"
            column="30"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String normalizedUrl = url.toLowerCase().trim();"
        errorLine2="                                   ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/download/DownloadManager.java"
            line="113"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="            String fileName = release.getName().replaceAll(&quot;\\s+&quot;, &quot;-&quot;).toLowerCase() + &quot;.zip&quot;;"
        errorLine2="                                                                        ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/download/DownloadManager.java"
            line="157"
            column="73"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        message = message.toLowerCase();"
        errorLine2="                          ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthManager.java"
            line="620"
            column="27"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String lowerMessage = message.toLowerCase();"
        errorLine2="                                      ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthManager.java"
            line="644"
            column="39"/>
    </issue>

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        String cleanKey = licenseKey.replace(&quot;-&quot;, &quot;&quot;).replace(&quot; &quot;, &quot;&quot;).toUpperCase();"
        errorLine2="                                                                       ~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/auth/LoginActivity.java"
            line="172"
            column="72"/>
    </issue>

    <issue
        id="UnusedAttribute"
        message="Attribute `iconTint` is only used in API level 26 and higher (current min is 24)"
        errorLine1="            android:iconTint=&quot;@color/white&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_patch_release.xml"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="CustomSplashScreen"
        message="The application should not provide its own launch screen"
        errorLine1="public class SplashActivity extends AppCompatActivity {"
        errorLine2="             ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/ui/splash/SplashActivity.java"
            line="32"
            column="14"/>
    </issue>

    <issue
        id="SelectedPhotoAccess"
        message="Your app is currently not handling Selected Photos Access introduced in Android 14+"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_MEDIA_VIDEO&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="22"
            column="36"/>
    </issue>

    <issue
        id="LockedOrientationActivity"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices"
        errorLine1="            android:screenOrientation=&quot;portrait&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed."
        errorLine1="            android:screenOrientation=&quot;portrait&quot;>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/AndroidManifest.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="PluralsCandidate"
        message="Formatting %d followed by words (&quot;days&quot;): This should probably be a plural rather than a string"
        errorLine1="    &lt;string name=&quot;days_remaining&quot;>%1$d days remaining&lt;/string>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="5"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `getString` to get device identifiers is not recommended"
        errorLine1="            String androidId = android.provider.Settings.Secure.getString("
        errorLine2="                               ^">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthManager.java"
            line="81"
            column="32"/>
    </issue>

    <issue
        id="HardwareIds"
        message="Using `SERIAL` to get device identifiers is not recommended"
        errorLine1="                    serial = android.os.Build.SERIAL;"
        errorLine2="                                              ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/auth/KeyAuthManager.java"
            line="92"
            column="47"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `CloudSyncManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static CloudSyncManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/cloud/CloudSyncManager.java"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `DockerManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static DockerManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/patch/DockerManager.java"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `DownloadManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static DownloadManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/download/DownloadManager.java"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `FridaManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static FridaManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/patch/FridaManager.java"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `PatchManager` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private static PatchManager instance;"
        errorLine2="            ~~~~~~">
        <location
            file="src/main/java/com/bearmod/loader/patch/PatchManager.java"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="                &lt;LinearLayout"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="240"
            column="18"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="                &lt;LinearLayout"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="264"
            column="18"/>
    </issue>

    <issue
        id="UseCompoundDrawables"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable"
        errorLine1="                &lt;LinearLayout"
        errorLine2="                 ~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="300"
            column="18"/>
    </issue>

    <issue
        id="VectorPath"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector."
        errorLine1="        android:pathData=&quot;M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z&quot; />"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/drawable/ic_settings.xml"
            line="9"
            column="27"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_patch_execution.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_NoActionBar`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_settings.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/Theme_BearLoader_Splash`)"
        errorLine1="    android:background=&quot;@color/background&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_splash.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/Theme.BearLoader`)"
        errorLine1="    android:background=&quot;@color/background_light&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/nav_header.xml"
            line="5"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.activity_download_modern` appears to be unused"
        errorLine1="&lt;androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.app_logo` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/app_logo.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.raw.bear_intro_mobile` appears to be unused">
        <location
            file="src/main/res/raw/bear_intro_mobile.mp4"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.circle_background` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/circle_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.teal_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;teal_700&quot;>#FF018786&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="8"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.error` appears to be unused"
        errorLine1="    &lt;color name=&quot;error&quot;>#F44336&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="16"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.surface_container_low` appears to be unused"
        errorLine1="    &lt;color name=&quot;surface_container_low&quot;>#1A1A1A&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="28"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.surface_container_highest` appears to be unused"
        errorLine1="    &lt;color name=&quot;surface_container_highest&quot;>#343434&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="31"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_overlay` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_overlay&quot;>#80121212&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="36"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.text_hint` appears to be unused"
        errorLine1="    &lt;color name=&quot;text_hint&quot;>#80FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="43"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.ripple` appears to be unused"
        errorLine1="    &lt;color name=&quot;ripple&quot;>#33FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="51"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.margin_medium` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;margin_medium&quot;>16dp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="2"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.margin_small` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;margin_small&quot;>8dp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="3"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_large` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_large&quot;>18sp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="4"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_medium` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_medium&quot;>16sp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="5"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_small` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_small&quot;>14sp&lt;/dimen>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="6"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.dimen.text_size_caption` appears to be unused"
        errorLine1="        &lt;dimen name=&quot;text_size_caption&quot;>12sp&lt;/dimen>&lt;/resources>"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/dimens.xml"
            line="7"
            column="16"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_check_circle` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_check_circle.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_close` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_close.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_background` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_launcher_foreground` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_launcher_foreground.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_pause` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_pause.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_search` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_search.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_sort` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_sort.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.layout_download_progress_modern` appears to be unused"
        errorLine1="&lt;com.google.android.material.card.MaterialCardView xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/layout_download_progress_modern.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.splash_background` appears to be unused"
        errorLine1="&lt;layer-list xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/splash_background.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.splash_gradient` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/splash_gradient.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_version_static` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_version_static&quot;>Version 1.3.0&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.keyauth_init_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;keyauth_init_failed&quot;>Failed to initialize KeyAuth. Please check your internet connection and try again.&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.select_target` appears to be unused"
        errorLine1="    &lt;string name=&quot;select_target&quot;>Select Target&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.patch_status` appears to be unused"
        errorLine1="    &lt;string name=&quot;patch_status&quot;>Patch Status&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.released` appears to be unused"
        errorLine1="    &lt;string name=&quot;released&quot;>Released: %1$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.downloading` appears to be unused"
        errorLine1="    &lt;string name=&quot;downloading&quot;>Downloading… %1$s / %2$s&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.download_complete` appears to be unused"
        errorLine1="    &lt;string name=&quot;download_complete&quot;>Download Complete&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="63"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.downloading_status` appears to be unused"
        errorLine1="    &lt;string name=&quot;downloading_status&quot;>Downloading&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.completed` appears to be unused"
        errorLine1="    &lt;string name=&quot;completed&quot;>Completed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="72"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;failed&quot;>Failed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="73"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.search_releases` appears to be unused"
        errorLine1="    &lt;string name=&quot;search_releases&quot;>Search releases…&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="74"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.filter_by_version` appears to be unused"
        errorLine1="    &lt;string name=&quot;filter_by_version&quot;>Filter by version&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="75"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sort_by_date` appears to be unused"
        errorLine1="    &lt;string name=&quot;sort_by_date&quot;>Sort by date&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="76"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.sort_by_size` appears to be unused"
        errorLine1="    &lt;string name=&quot;sort_by_size&quot;>Sort by size&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="77"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_results_found` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_results_found&quot;>No results found&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="78"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.clear_filters` appears to be unused"
        errorLine1="    &lt;string name=&quot;clear_filters&quot;>Clear filters&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.notification_settings` appears to be unused"
        errorLine1="    &lt;string name=&quot;notification_settings&quot;>Notification Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.dark_mode` appears to be unused"
        errorLine1="    &lt;string name=&quot;dark_mode&quot;>Dark Mode&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="90"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.language` appears to be unused"
        errorLine1="    &lt;string name=&quot;language&quot;>Language&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="91"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.retry` appears to be unused"
        errorLine1="    &lt;string name=&quot;retry&quot;>Retry&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="106"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.loading` appears to be unused"
        errorLine1="    &lt;string name=&quot;loading&quot;>Loading…&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="107"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error` appears to be unused"
        errorLine1="    &lt;string name=&quot;error&quot;>Error&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="108"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.info` appears to be unused"
        errorLine1="    &lt;string name=&quot;info&quot;>Information&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="111"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.exit` appears to be unused"
        errorLine1="    &lt;string name=&quot;exit&quot;>Exit&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="112"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.continue_anyway` appears to be unused"
        errorLine1="    &lt;string name=&quot;continue_anyway&quot;>Continue Anyway&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="113"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.app_version` appears to be unused"
        errorLine1="    &lt;string name=&quot;app_version&quot;>Version 1.3&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="114"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.all` appears to be unused"
        errorLine1="    &lt;string name=&quot;all&quot;>All&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="121"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.latest` appears to be unused"
        errorLine1="    &lt;string name=&quot;latest&quot;>Latest&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="122"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.pull_down_to_refresh_or_check_your_connection` appears to be unused"
        errorLine1="    &lt;string name=&quot;pull_down_to_refresh_or_check_your_connection&quot;>Pull down to refresh or check your connection&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="123"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.downloaded` appears to be unused"
        errorLine1="    &lt;string name=&quot;downloaded&quot;>Downloaded&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="124"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.eta` appears to be unused"
        errorLine1="    &lt;string name=&quot;eta&quot;>ETA&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="125"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.pause` appears to be unused"
        errorLine1="    &lt;string name=&quot;pause&quot;>Pause&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="126"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_BearLoader_Dialog` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.BearLoader.Dialog&quot; parent=&quot;ThemeOverlay.MaterialComponents.Dialog.Alert&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="54"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Widget_BearLoader_Button_Tonal` appears to be unused"
        errorLine1="    &lt;style name=&quot;Widget.BearLoader.Button.Tonal&quot; parent=&quot;Widget.MaterialComponents.Button.OutlinedButton&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="87"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Headline6` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Headline6&quot;"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="98"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Body1` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Body1&quot; parent=&quot;TextAppearance.MaterialComponents.Body1&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="105"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Body2` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Body2&quot; parent=&quot;TextAppearance.MaterialComponents.Body2&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="111"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.TextAppearance_BearLoader_Caption` appears to be unused"
        errorLine1="    &lt;style name=&quot;TextAppearance.BearLoader.Caption&quot;"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="116"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_BearLoader_AppBarOverlay` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.BearLoader.AppBarOverlay&quot; parent=&quot;ThemeOverlay.AppCompat.Dark.ActionBar&quot; />"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="123"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Theme_BearLoader_PopupOverlay` appears to be unused"
        errorLine1="    &lt;style name=&quot;Theme.BearLoader.PopupOverlay&quot; parent=&quot;ThemeOverlay.AppCompat.Light&quot; />"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="125"
            column="12"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ic_pubg_gl.png` in densityless folder">
        <location
            file="src/main/res/drawable/ic_pubg_gl.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ic_pubg_kr.png` in densityless folder">
        <location
            file="src/main/res/drawable/ic_pubg_kr.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ic_pubg_tw.png` in densityless folder">
        <location
            file="src/main/res/drawable/ic_pubg_tw.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/ic_pubg_vn.png` in densityless folder">
        <location
            file="src/main/res/drawable/ic_pubg_vn.png"/>
    </issue>

    <issue
        id="IconLocation"
        message="Found bitmap drawable `res/drawable/logo.png` in densityless folder">
        <location
            file="src/main/res/drawable/logo.png"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="162"
            column="22"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="246"
            column="22"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="271"
            column="22"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="                    &lt;ImageView"
        errorLine2="                     ~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_download_modern.xml"
            line="307"
            column="22"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout-v26/item_patch_release.xml"
            line="17"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_patch_release.xml"
            line="17"
            column="10"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Game v1.0.5&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Game v1.0.5&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_release.xml"
            line="112"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;2023-06-15&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;2023-06-15&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_release.xml"
            line="125"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;25.4 MB&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;25.4 MB&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_release.xml"
            line="138"
            column="17"/>
    </issue>

</issues>
