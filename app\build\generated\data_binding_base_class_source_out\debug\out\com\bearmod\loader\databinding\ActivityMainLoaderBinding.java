// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainLoaderBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final TextView headerSubtitle;

  @NonNull
  public final TextView headerTitle;

  @NonNull
  public final RecyclerView rvAppVersions;

  private ActivityMainLoaderBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull BottomNavigationView bottomNavigation,
      @NonNull TextView headerSubtitle, @NonNull TextView headerTitle,
      @NonNull RecyclerView rvAppVersions) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomNavigation = bottomNavigation;
    this.headerSubtitle = headerSubtitle;
    this.headerTitle = headerTitle;
    this.rvAppVersions = rvAppVersions;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainLoaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainLoaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main_loader, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainLoaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottom_navigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.header_subtitle;
      TextView headerSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (headerSubtitle == null) {
        break missingId;
      }

      id = R.id.header_title;
      TextView headerTitle = ViewBindings.findChildViewById(rootView, id);
      if (headerTitle == null) {
        break missingId;
      }

      id = R.id.rv_app_versions;
      RecyclerView rvAppVersions = ViewBindings.findChildViewById(rootView, id);
      if (rvAppVersions == null) {
        break missingId;
      }

      return new ActivityMainLoaderBinding((CoordinatorLayout) rootView, appBarLayout,
          bottomNavigation, headerSubtitle, headerTitle, rvAppVersions);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
