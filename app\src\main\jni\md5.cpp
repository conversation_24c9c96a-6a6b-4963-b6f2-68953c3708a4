#include <jni.h>
#include <android/log.h>
#include <string>

#define LOG_TAG "BearMD5"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

/**
 * Calculate simple hash of a string (fallback without OpenSSL)
 */
std::string calculateSimpleHash(const std::string& input) {
    // Simple hash function for demonstration
    unsigned long hash = 5381;
    for (char c : input) {
        hash = ((hash << 5) + hash) + c;
    }

    char buf[32];
    sprintf(buf, "%08lx", hash);
    return std::string(buf);
}

/**
 * Calculate simple hash of byte array (fallback without OpenSSL)
 */
std::string calculateSimpleHash(const unsigned char* data, size_t length) {
    // Simple hash function for demonstration
    unsigned long hash = 5381;
    for (size_t i = 0; i < length; i++) {
        hash = ((hash << 5) + hash) + data[i];
    }

    char buf[32];
    sprintf(buf, "%08lx", hash);
    return std::string(buf);
}

/**
 * JNI export for hash calculation
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_MD5_calculateMD5(JNIEnv* env, jobject thiz, jstring input) {
    const char* inputStr = env->GetStringUTFChars(input, nullptr);
    std::string result = calculateSimpleHash(std::string(inputStr));
    env->ReleaseStringUTFChars(input, inputStr);
    return env->NewStringUTF(result.c_str());
}

/**
 * JNI export for hash calculation from byte array
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_MD5_calculateMD5FromBytes(JNIEnv* env, jobject thiz, jbyteArray input) {
    jsize length = env->GetArrayLength(input);
    jbyte* bytes = env->GetByteArrayElements(input, nullptr);

    std::string result = calculateSimpleHash((unsigned char*)bytes, length);

    env->ReleaseByteArrayElements(input, bytes, JNI_ABORT);
    return env->NewStringUTF(result.c_str());
}
