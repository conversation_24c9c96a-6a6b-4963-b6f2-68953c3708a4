#include <jni.h>
#include <android/log.h>
#include <string>
#include <openssl/md5.h>

#define LOG_TAG "BearMD5"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

/**
 * Calculate MD5 hash of a string
 */
std::string calculateMD5(const std::string& input) {
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5((unsigned char*)input.c_str(), input.length(), digest);
    
    std::string result;
    char buf[3];
    for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
        sprintf(buf, "%02x", digest[i]);
        result += buf;
    }
    return result;
}

/**
 * Calculate MD5 hash of byte array
 */
std::string calculateMD5(const unsigned char* data, size_t length) {
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5(data, length, digest);
    
    std::string result;
    char buf[3];
    for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
        sprintf(buf, "%02x", digest[i]);
        result += buf;
    }
    return result;
}

/**
 * JNI export for MD5 calculation
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_MD5_calculateMD5(JNIEnv* env, jobject thiz, jstring input) {
    const char* inputStr = env->GetStringUTFChars(input, nullptr);
    std::string result = calculateMD5(std::string(inputStr));
    env->ReleaseStringUTFChars(input, inputStr);
    return env->NewStringUTF(result.c_str());
}

/**
 * JNI export for MD5 calculation from byte array
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_MD5_calculateMD5FromBytes(JNIEnv* env, jobject thiz, jbyteArray input) {
    jsize length = env->GetArrayLength(input);
    jbyte* bytes = env->GetByteArrayElements(input, nullptr);
    
    std::string result = calculateMD5((unsigned char*)bytes, length);
    
    env->ReleaseByteArrayElements(input, bytes, JNI_ABORT);
    return env->NewStringUTF(result.c_str());
}
