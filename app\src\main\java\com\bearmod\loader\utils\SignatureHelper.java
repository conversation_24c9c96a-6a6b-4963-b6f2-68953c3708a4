package com.bearmod.loader.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Build;
import android.util.Log;

import java.io.ByteArrayInputStream;
import java.security.MessageDigest;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;

/**
 * Utility class to help developers get signature fingerprints for configuration
 * Use this during development to get the correct signature fingerprints
 */
public class SignatureHelper {
    
    private static final String TAG = "SignatureHelper";
    
    /**
     * Get current app's signature fingerprint
     * Use this method during development to get your app's signature
     * @param context Application context
     * @return Signature fingerprint or null if failed
     */
    public static String getCurrentAppSignature(Context context) {
        try {
            return getPackageSignature(context, context.getPackageName());
        } catch (Exception e) {
            Log.e(TAG, "Failed to get current app signature", e);
            return null;
        }
    }
    
    /**
     * Get signature fingerprint for any installed package
     * @param context Application context
     * @param packageName Package name to get signature for
     * @return Signature fingerprint or null if failed
     */
    public static String getPackageSignature(Context context, String packageName) {
        try {
            PackageManager pm = context.getPackageManager();
            
            PackageInfo packageInfo;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNING_CERTIFICATES);
                if (packageInfo.signingInfo == null) {
                    Log.e(TAG, "No signing info found for " + packageName);
                    return null;
                }
                
                Signature[] signatures = packageInfo.signingInfo.getApkContentsSigners();
                if (signatures.length == 0) {
                    Log.e(TAG, "No signatures found for " + packageName);
                    return null;
                }
                
                return getSignatureFingerprint(signatures[0]);
            } else {
                packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_SIGNATURES);
                if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                    Log.e(TAG, "No signatures found for " + packageName);
                    return null;
                }
                
                return getSignatureFingerprint(packageInfo.signatures[0]);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get signature for " + packageName, e);
            return null;
        }
    }
    
    /**
     * Get signature fingerprint using SHA-256
     * @param signature Signature to get fingerprint for
     * @return SHA-256 fingerprint string
     * @throws Exception If fingerprint generation fails
     */
    private static String getSignatureFingerprint(Signature signature) throws Exception {
        byte[] signatureBytes = signature.toByteArray();
        
        // Get certificate
        CertificateFactory cf = CertificateFactory.getInstance("X.509");
        X509Certificate cert = (X509Certificate) cf.generateCertificate(new ByteArrayInputStream(signatureBytes));
        
        // Get SHA-256 fingerprint
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] fingerprint = md.digest(cert.getEncoded());
        
        // Convert to hex string with colons
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fingerprint.length; i++) {
            if (i > 0) {
                sb.append(":");
            }
            sb.append(String.format("%02X", fingerprint[i] & 0xFF));
        }
        
        return sb.toString();
    }
    
    /**
     * Log all signature information for debugging
     * Call this method during development to see signature details
     * @param context Application context
     */
    public static void logSignatureInfo(Context context) {
        String packageName = context.getPackageName();
        Log.d(TAG, "=== Signature Information for " + packageName + " ===");
        
        try {
            String signature = getCurrentAppSignature(context);
            if (signature != null) {
                Log.d(TAG, "SHA-256 Fingerprint: " + signature);
                Log.d(TAG, "Add this to TRUSTED_SIGNATURES in AppSecurityManager:");
                Log.d(TAG, "\"" + signature + "\",");
            } else {
                Log.e(TAG, "Failed to get signature");
            }
            
            // Also log PUBG Mobile signatures if installed
            String[] pubgPackages = {
                "com.tencent.ig",
                "com.pubg.krmobile", 
                "com.rekoo.pubgm",
                "com.vng.pubgmobile"
            };
            
            for (String pubgPackage : pubgPackages) {
                try {
                    String pubgSignature = getPackageSignature(context, pubgPackage);
                    if (pubgSignature != null) {
                        Log.d(TAG, "PUBG " + pubgPackage + " signature: " + pubgSignature);
                    }
                } catch (Exception e) {
                    // Package not installed, ignore
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error logging signature info", e);
        }
        
        Log.d(TAG, "=== End Signature Information ===");
    }
    
    /**
     * Check if two signatures match
     * @param signature1 First signature
     * @param signature2 Second signature
     * @return true if signatures match, false otherwise
     */
    public static boolean signaturesMatch(String signature1, String signature2) {
        if (signature1 == null || signature2 == null) {
            return false;
        }
        return signature1.equals(signature2);
    }
    
    /**
     * Validate signature format
     * @param signature Signature to validate
     * @return true if signature format is valid, false otherwise
     */
    public static boolean isValidSignatureFormat(String signature) {
        if (signature == null || signature.isEmpty()) {
            return false;
        }
        
        // SHA-256 fingerprint should be 64 hex characters with colons
        // Format: XX:XX:XX:XX:... (32 pairs of hex digits)
        String[] parts = signature.split(":");
        if (parts.length != 32) {
            return false;
        }
        
        for (String part : parts) {
            if (part.length() != 2) {
                return false;
            }
            try {
                Integer.parseInt(part, 16);
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get signature information for display
     * @param context Application context
     * @return Formatted signature information
     */
    public static String getSignatureInfoForDisplay(Context context) {
        StringBuilder info = new StringBuilder();
        
        String packageName = context.getPackageName();
        info.append("Package: ").append(packageName).append("\n");
        
        String signature = getCurrentAppSignature(context);
        if (signature != null) {
            info.append("Signature: ").append(signature).append("\n");
            info.append("Valid Format: ").append(isValidSignatureFormat(signature) ? "Yes" : "No").append("\n");
        } else {
            info.append("Signature: Failed to retrieve\n");
        }
        
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(packageName, 0);
            info.append("Version: ").append(packageInfo.versionName).append("\n");
            info.append("Version Code: ").append(packageInfo.versionCode).append("\n");
        } catch (Exception e) {
            info.append("Version: Failed to retrieve\n");
        }
        
        return info.toString();
    }
}
