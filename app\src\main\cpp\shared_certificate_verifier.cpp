#include <jni.h>
#include <string>
#include <vector>
#include <openssl/sha.h>
#include <android/log.h>

#define LOG_TAG "SharedCertVerifier"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Trusted package names
const char* TRUSTED_LOADER_PACKAGE = "com.bearmod.loader";
const char* TRUSTED_PUBG_GLOBAL = "com.tencent.ig";
const char* TRUSTED_PUBG_KR = "com.pubg.krmobile";
const char* TRUSTED_PUBG_TW = "com.rekoo.pubgm";
const char* TRUSTED_PUBG_VN = "com.vng.pubgmobile";

// Trusted signature hashes (SHA-256) - replace with your actual hashes
static const std::vector<std::string> TRUSTED_LOADER_HASHES = {
    "YOUR_BEARMOD_LOADER_RELEASE_SHA256_HASH",
    "YOUR_BEARMOD_LOADER_DEBUG_SHA256_HASH"
};

static const std::vector<std::string> TRUSTED_PUBG_HASHES = {
    "PUBG_GLOBAL_OFFICIAL_SHA256_HASH",
    "PUBG_KR_OFFICIAL_SHA256_HASH",
    "PUBG_TW_OFFICIAL_SHA256_HASH",
    "PUBG_VN_OFFICIAL_SHA256_HASH"
};

/**
 * Calculate SHA-256 hash of byte array
 */
std::string calculateSHA256(const unsigned char* data, size_t length) {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(data, length, hash);
    
    std::string result;
    char buf[3];
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        sprintf(buf, "%02x", hash[i]);
        result += buf;
    }
    return result;
}

/**
 * Get package signature hash
 */
std::string getPackageSignatureHash(JNIEnv* env, jobject context, const char* packageName) {
    try {
        // Get PackageManager
        jclass contextClass = env->GetObjectClass(context);
        jmethodID getPM = env->GetMethodID(contextClass, "getPackageManager", 
                                          "()Landroid/content/pm/PackageManager;");
        if (!getPM) {
            LOGE("Failed to get getPackageManager method");
            return "";
        }
        
        jobject pm = env->CallObjectMethod(context, getPM);
        if (!pm) {
            LOGE("Failed to get PackageManager");
            return "";
        }

        // Create package name string
        jstring jPackageName = env->NewStringUTF(packageName);
        if (!jPackageName) {
            LOGE("Failed to create package name string");
            return "";
        }

        // Get PackageInfo with signatures
        jclass pmClass = env->GetObjectClass(pm);
        jmethodID getInfo = env->GetMethodID(pmClass, "getPackageInfo", 
                                           "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        if (!getInfo) {
            LOGE("Failed to get getPackageInfo method");
            env->DeleteLocalRef(jPackageName);
            return "";
        }

        // GET_SIGNATURES = 0x40
        jobject pkgInfo = env->CallObjectMethod(pm, getInfo, jPackageName, 0x40);
        env->DeleteLocalRef(jPackageName);
        
        if (!pkgInfo) {
            LOGE("Failed to get PackageInfo for %s", packageName);
            return "";
        }

        // Get signatures array
        jclass pkgInfoClass = env->GetObjectClass(pkgInfo);
        jfieldID sigField = env->GetFieldID(pkgInfoClass, "signatures", 
                                          "[Landroid/content/pm/Signature;");
        if (!sigField) {
            LOGE("Failed to get signatures field");
            return "";
        }
        
        jobjectArray sigs = (jobjectArray)env->GetObjectField(pkgInfo, sigField);
        if (!sigs) {
            LOGE("No signatures found for %s", packageName);
            return "";
        }

        // Get first signature
        jobject sig = env->GetObjectArrayElement(sigs, 0);
        if (!sig) {
            LOGE("Failed to get first signature for %s", packageName);
            return "";
        }

        // Get signature bytes
        jclass sigClass = env->GetObjectClass(sig);
        jmethodID toByteArray = env->GetMethodID(sigClass, "toByteArray", "()[B");
        if (!toByteArray) {
            LOGE("Failed to get toByteArray method");
            return "";
        }
        
        jbyteArray byteArray = (jbyteArray)env->CallObjectMethod(sig, toByteArray);
        if (!byteArray) {
            LOGE("Failed to get signature byte array for %s", packageName);
            return "";
        }

        // Get byte array data
        jsize length = env->GetArrayLength(byteArray);
        jbyte* bytes = env->GetByteArrayElements(byteArray, nullptr);
        if (!bytes) {
            LOGE("Failed to get byte array elements for %s", packageName);
            return "";
        }

        // Calculate SHA-256 hash
        std::string hash = calculateSHA256((unsigned char*)bytes, length);
        
        // Release byte array
        env->ReleaseByteArrayElements(byteArray, bytes, JNI_ABORT);
        
        LOGI("Package %s signature hash: %s", packageName, hash.c_str());
        return hash;
        
    } catch (...) {
        LOGE("Exception in getPackageSignatureHash for %s", packageName);
        return "";
    }
}

/**
 * Check if package is installed
 */
bool isPackageInstalled(JNIEnv* env, jobject context, const char* packageName) {
    try {
        jclass contextClass = env->GetObjectClass(context);
        jmethodID getPM = env->GetMethodID(contextClass, "getPackageManager", 
                                          "()Landroid/content/pm/PackageManager;");
        jobject pm = env->CallObjectMethod(context, getPM);
        
        jstring jPackageName = env->NewStringUTF(packageName);
        
        jclass pmClass = env->GetObjectClass(pm);
        jmethodID getInfo = env->GetMethodID(pmClass, "getPackageInfo", 
                                           "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        
        jobject pkgInfo = env->CallObjectMethod(pm, getInfo, jPackageName, 0);
        env->DeleteLocalRef(jPackageName);
        
        return pkgInfo != nullptr;
        
    } catch (...) {
        return false;
    }
}

/**
 * Verify loader signature
 */
bool verifyLoaderSignature(JNIEnv* env, jobject context) {
    LOGI("Verifying BearMod Loader signature");
    
    // Check if loader is installed
    if (!isPackageInstalled(env, context, TRUSTED_LOADER_PACKAGE)) {
        LOGE("BearMod Loader not installed: %s", TRUSTED_LOADER_PACKAGE);
        return false;
    }
    
    // Get loader signature hash
    std::string hash = getPackageSignatureHash(env, context, TRUSTED_LOADER_PACKAGE);
    if (hash.empty()) {
        LOGE("Failed to get loader signature hash");
        return false;
    }
    
    // Check against trusted hashes
    for (const auto& trustedHash : TRUSTED_LOADER_HASHES) {
        if (hash == trustedHash) {
            LOGI("BearMod Loader signature verified successfully");
            return true;
        }
    }
    
    LOGE("BearMod Loader signature verification failed");
    return false;
}

/**
 * Verify PUBG Mobile signature
 */
bool verifyPubgSignature(JNIEnv* env, jobject context, const char* pubgPackageName) {
    LOGI("Verifying PUBG Mobile signature for: %s", pubgPackageName);
    
    // Check if PUBG is installed
    if (!isPackageInstalled(env, context, pubgPackageName)) {
        LOGE("PUBG Mobile not installed: %s", pubgPackageName);
        return false;
    }
    
    // Get PUBG signature hash
    std::string hash = getPackageSignatureHash(env, context, pubgPackageName);
    if (hash.empty()) {
        LOGE("Failed to get PUBG signature hash");
        return false;
    }
    
    // Check against trusted PUBG hashes
    for (const auto& trustedHash : TRUSTED_PUBG_HASHES) {
        if (hash == trustedHash) {
            LOGI("PUBG Mobile signature verified successfully for %s", pubgPackageName);
            return true;
        }
    }
    
    LOGE("PUBG Mobile signature verification failed for %s", pubgPackageName);
    return false;
}

/**
 * Main shared certificate verification function
 * This is the function you call in your target mod
 */
bool verifySharedCertificate(JNIEnv* env, jobject context) {
    LOGI("=== Starting Shared Certificate Verification ===");
    
    // 1. Verify BearMod Loader signature
    if (!verifyLoaderSignature(env, context)) {
        LOGE("BearMod Loader signature verification failed");
        return false;
    }
    
    // 2. Verify current app (target mod) signature
    // Get current package name
    jclass contextClass = env->GetObjectClass(context);
    jmethodID getPkg = env->GetMethodID(contextClass, "getPackageName", "()Ljava/lang/String;");
    jstring currentPkg = (jstring)env->CallObjectMethod(context, getPkg);
    
    const char* currentPkgName = env->GetStringUTFChars(currentPkg, nullptr);
    LOGI("Verifying current app signature: %s", currentPkgName);
    
    std::string currentHash = getPackageSignatureHash(env, context, currentPkgName);
    env->ReleaseStringUTFChars(currentPkg, currentPkgName);
    
    if (currentHash.empty()) {
        LOGE("Failed to get current app signature");
        return false;
    }
    
    // For target mod, you would check against your target mod's trusted signatures
    // For now, we'll just verify it exists and is not empty
    LOGI("Current app signature hash: %s", currentHash.c_str());
    
    // 3. Optional: Verify PUBG Mobile signature if installed
    const char* pubgPackages[] = {
        TRUSTED_PUBG_GLOBAL,
        TRUSTED_PUBG_KR,
        TRUSTED_PUBG_TW,
        TRUSTED_PUBG_VN
    };
    
    bool pubgFound = false;
    for (const char* pubgPkg : pubgPackages) {
        if (isPackageInstalled(env, context, pubgPkg)) {
            LOGI("Found PUBG Mobile: %s", pubgPkg);
            if (verifyPubgSignature(env, context, pubgPkg)) {
                pubgFound = true;
                break;
            } else {
                LOGE("PUBG Mobile signature verification failed for %s", pubgPkg);
                return false;
            }
        }
    }
    
    if (!pubgFound) {
        LOGI("No PUBG Mobile installation found - continuing without PUBG verification");
    }
    
    LOGI("=== Shared Certificate Verification PASSED ===");
    return true;
}

/**
 * JNI export for Java integration
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_targetapp_NativeAuth_verifySharedCertificate(
        JNIEnv* env, jobject thiz, jobject context) {
    return verifySharedCertificate(env, context) ? JNI_TRUE : JNI_FALSE;
}

/**
 * JNI export for getting signature hash (debugging)
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_targetapp_NativeAuth_getPackageSignatureHash(
        JNIEnv* env, jobject thiz, jobject context, jstring packageName) {
    
    const char* pkgName = env->GetStringUTFChars(packageName, nullptr);
    std::string hash = getPackageSignatureHash(env, context, pkgName);
    env->ReleaseStringUTFChars(packageName, pkgName);
    
    if (hash.empty()) {
        return nullptr;
    }
    
    return env->NewStringUTF(hash.c_str());
}

/**
 * JNI export for checking if loader is trusted
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_targetapp_NativeAuth_isLoaderTrusted(
        JNIEnv* env, jobject thiz, jobject context) {
    return verifyLoaderSignature(env, context) ? JNI_TRUE : JNI_FALSE;
}
