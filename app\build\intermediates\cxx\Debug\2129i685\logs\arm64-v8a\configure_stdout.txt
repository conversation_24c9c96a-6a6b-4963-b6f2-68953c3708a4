md "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685\lib\arm64-v8a" >NUL 2>NUL || rem
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Install "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/lib/arm64-v8a/libc++_shared.so"":
copy /b/y "D:\AndroidBuildEnv\SDK\ndk\27.1.12297006\build\..\toolchains\llvm\prebuilt\windows-x86_64\sysroot\usr\lib\aarch64-linux-android\libc++_shared.so" "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685\lib\arm64-v8a\libc++_shared.so" > NUL
md "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685\obj\local\arm64-v8a\objs-debug\bearmod" >NUL 2>NUL || rem
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "bearmod <= Main.cpp"
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/Main.o.d -target aarch64-none-linux-android30 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O0 -UNDEBUG -fno-limit-debug-info  -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -Wall -Wno-error -O0 -g -DDEBUG -std=c++17 -frtti -fexceptions  -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fpermissive -Wformat -Werror=format-security -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -frtti -fexceptions -frtti -fexceptions  -c  D:/Augment_Code/BearMod-Loader/app/src/main/jni/Main.cpp -o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/Main.o
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "bearmod <= Tools.cpp"
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/Tools.o.d -target aarch64-none-linux-android30 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O0 -UNDEBUG -fno-limit-debug-info  -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -Wall -Wno-error -O0 -g -DDEBUG -std=c++17 -frtti -fexceptions  -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fpermissive -Wformat -Werror=format-security -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -frtti -fexceptions -frtti -fexceptions  -c  D:/Augment_Code/BearMod-Loader/app/src/main/jni/Tools.cpp -o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/Tools.o
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "bearmod <= md5.cpp"
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/md5.o.d -target aarch64-none-linux-android30 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O0 -UNDEBUG -fno-limit-debug-info  -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -Wall -Wno-error -O0 -g -DDEBUG -std=c++17 -frtti -fexceptions  -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fpermissive -Wformat -Werror=format-security -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -frtti -fexceptions -frtti -fexceptions  -c  D:/Augment_Code/BearMod-Loader/app/src/main/jni/md5.cpp -o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/md5.o
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Compile++      ": "bearmod <= bear_trust_verifier.cpp"
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -MMD -MP -MF D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/bear_trust_verifier.o.d -target aarch64-none-linux-android30 -fdata-sections -ffunction-sections -fstack-protector-strong -funwind-tables -no-canonical-prefixes  --sysroot D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/sysroot -g -Wno-invalid-command-line-argument -Wno-unused-command-line-argument  -D_FORTIFY_SOURCE=2 -fno-exceptions -fno-rtti -fpic -O0 -UNDEBUG -fno-limit-debug-info  -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -ID:/Augment_Code/BearMod-Loader/app/src/main/jni -Wall -Wno-error -O0 -g -DDEBUG -std=c++17 -frtti -fexceptions  -DANDROID -Wno-error=format-security -fvisibility=hidden -ffunction-sections -fdata-sections -w -fpermissive -Wformat -Werror=format-security -std=c++17 -Wno-error=c++11-narrowing -fms-extensions -frtti -fexceptions -frtti -fexceptions  -c  D:/Augment_Code/BearMod-Loader/app/src/main/jni/bear_trust_verifier.cpp -o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/bear_trust_verifier.o
md "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685\obj\local\arm64-v8a" >NUL 2>NUL || rem
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "SharedLibrary  ": "libbearmod.so"
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe -Wl,-soname,libbearmod.so -shared D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/Main.o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/Tools.o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/md5.o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/objs-debug/bearmod/bear_trust_verifier.o -latomic -target aarch64-none-linux-android30 -no-canonical-prefixes   -Wl,--build-id=sha1  -Wl,--gc-sections,--strip-all -Wl,--no-undefined -Wl,--fatal-warnings -Wl,--no-undefined-version -llog -landroid -lz -lc -lm -o D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/obj/local/arm64-v8a/libbearmod.so
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../prebuilt/windows-x86_64/bin/echo.exe [arm64-v8a] "Install        ": "libbearmod.so => D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/lib/arm64-v8a/libbearmod.so"
copy /b/y "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685\obj\local\arm64-v8a\libbearmod.so" "D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685\lib\arm64-v8a\libbearmod.so" > NUL
D:/AndroidBuildEnv/SDK/ndk/27.1.12297006/build/../toolchains/llvm/prebuilt/windows-x86_64/bin/llvm-strip.exe --strip-unneeded  D:\Augment_Code\BearMod-Loader\app\build\intermediates\cxx\Debug\2129i685/lib/arm64-v8a/libbearmod.so
