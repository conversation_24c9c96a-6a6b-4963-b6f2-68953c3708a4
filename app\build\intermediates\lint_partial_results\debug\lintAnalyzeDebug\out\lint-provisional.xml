<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.READ_PRIVILEGED_PHONE_STATE" message="Missing permissions required by Build.getSerial: %1$s" lastApi="**********" operator="&amp;"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/auth/KeyAuthManager.java"
            line="134"
            column="30"
            startOffset="5255"
            endLine="134"
            endColumn="58"
            endOffset="5283"/>
        <map>
            <entry
                name="message"
                string="Missing permissions required by Build.getSerial: %1$s"/>
            <entry
                name="requirement"
                string="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="10"
            column="36"
            startOffset="410"
            endLine="10"
            endColumn="76"
            endOffset="450"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="32"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="32"
            column="15"
            startOffset="1645"
            endLine="32"
            endColumn="54"
            endOffset="1684"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="31-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="38"
            column="15"
            startOffset="1857"
            endLine="38"
            endColumn="58"
            endOffset="1900"/>
        <map>
            <entry
                name="message"
                string="`android:windowSplashScreenBackground` requires API level 31 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="31-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="31-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="39"
            column="15"
            startOffset="1940"
            endLine="39"
            endColumn="60"
            endOffset="1985"/>
        <map>
            <entry
                name="message"
                string="`android:windowSplashScreenAnimatedIcon` requires API level 31 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="31-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="31-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="40"
            column="15"
            startOffset="2040"
            endLine="40"
            endColumn="67"
            endOffset="2092"/>
        <map>
            <entry
                name="message"
                string="`android:windowSplashScreenIconBackgroundColor` requires API level 31 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="31-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="31-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="41"
            column="15"
            startOffset="2129"
            endLine="41"
            endColumn="61"
            endOffset="2175"/>
        <map>
            <entry
                name="message"
                string="`android:windowSplashScreenBrandingImage` requires API level 31 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="31-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="57"
            column="15"
            startOffset="3105"
            endLine="57"
            endColumn="54"
            endOffset="3144"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="24-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="255"
            column="15"
            startOffset="12171"
            endLine="255"
            endColumn="54"
            endOffset="12210"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="24-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="CustomSplashScreen"
        severity="warning"
        message="The application should not provide its own launch screen">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/bearmod/loader/ui/splash/SplashActivity.java"
            line="23"
            column="14"
            startOffset="611"
            endLine="23"
            endColumn="28"
            endOffset="625"/>
        <map>
            <condition targetGE="31"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_back.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="40"
            endOffset="270"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cancel.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cancel.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clear_cache.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="42"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_dashboard.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="42"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="49"
            endOffset="279"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_logout.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="42"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_paste.xml"
            line="7"
            column="19"
            startOffset="244"
            endLine="7"
            endColumn="43"
            endOffset="268"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_paste.xml"
            line="9"
            column="28"
            startOffset="310"
            endLine="9"
            endColumn="48"
            endOffset="330"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reset.xml"
            line="8"
            column="28"
            startOffset="258"
            endLine="8"
            endColumn="42"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="2237"
                endOffset="2257"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="49"
            column="9"
            startOffset="2237"
            endLine="49"
            endColumn="29"
            endOffset="2257"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
