{"logs": [{"outputFile": "com.bearmod.loader.app-mergeReleaseResources-59:/values-fa/values-fa.xml", "map": [{"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\cfcf24ccfb230d11ef9bef592956f67b\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "55,56,57,58,59,60,61,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4054,4153,4255,4354,4454,4555,4661,14301", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "4148,4250,4349,4449,4550,4656,4773,14397"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\c4a1e893a995f6a58f7d5e0e2b76f6f2\\transformed\\navigation-ui-2.9.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,114", "endOffsets": "159,274"}, "to": {"startLines": "179,180", "startColumns": "4,4", "startOffsets": "13680,13789", "endColumns": "108,114", "endOffsets": "13784,13899"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2fdb62b6838fcf913e5e5ca4d6ff4db5\\transformed\\exoplayer-core-2.19.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7166,7233,7294,7361,7421,7499,7574,7663,7751", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "7228,7289,7356,7416,7494,7569,7658,7746,7811"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\2baa16814b930be5ec3f7d400737a6ef\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "950,1060,1161,1272,1356,1457,1572,1652,1729,1822,1917,2009,2103,2205,2300,2397,2491,2584,2674,2756,2864,2968,3066,3172,3277,3382,3539,13982", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "1055,1156,1267,1351,1452,1567,1647,1724,1817,1912,2004,2098,2200,2295,2392,2486,2579,2669,2751,2859,2963,3061,3167,3272,3377,3534,3635,14059"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\36f784ecfa4226c5c1c4c160bbf08ede\\transformed\\material-1.12.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,334,411,493,586,673,770,899,983,1041,1104,1194,1263,1323,1414,1477,1541,1600,1667,1729,1784,1907,1965,2026,2081,2153,2290,2371,2451,2548,2629,2711,2841,2915,2989,3121,3207,3284,3335,3389,3455,3526,3603,3674,3753,3826,3900,3970,4044,4145,4231,4305,4394,4486,4560,4633,4722,4773,4853,4920,5003,5087,5149,5213,5276,5345,5439,5540,5633,5731,5786,5844,5922,6008,6085", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "254,329,406,488,581,668,765,894,978,1036,1099,1189,1258,1318,1409,1472,1536,1595,1662,1724,1779,1902,1960,2021,2076,2148,2285,2366,2446,2543,2624,2706,2836,2910,2984,3116,3202,3279,3330,3384,3450,3521,3598,3669,3748,3821,3895,3965,4039,4140,4226,4300,4389,4481,4555,4628,4717,4768,4848,4915,4998,5082,5144,5208,5271,5340,5434,5535,5628,5726,5781,5839,5917,6003,6080,6154"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,3640,3715,3792,3874,3967,4778,4875,5004,5088,5146,8940,9030,9099,9159,9250,9313,9377,9436,9503,9565,9620,9743,9801,9862,9917,9989,10126,10207,10287,10384,10465,10547,10677,10751,10825,10957,11043,11120,11171,11225,11291,11362,11439,11510,11589,11662,11736,11806,11880,11981,12067,12141,12230,12322,12396,12469,12558,12609,12689,12756,12839,12923,12985,13049,13112,13181,13275,13376,13469,13567,13622,13904,14064,14150,14227", "endLines": "22,50,51,52,53,54,62,63,64,65,66,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,181,183,184,185", "endColumns": "12,74,76,81,92,86,96,128,83,57,62,89,68,59,90,62,63,58,66,61,54,122,57,60,54,71,136,80,79,96,80,81,129,73,73,131,85,76,50,53,65,70,76,70,78,72,73,69,73,100,85,73,88,91,73,72,88,50,79,66,82,83,61,63,62,68,93,100,92,97,54,57,77,85,76,73", "endOffsets": "945,3710,3787,3869,3962,4049,4870,4999,5083,5141,5204,9025,9094,9154,9245,9308,9372,9431,9498,9560,9615,9738,9796,9857,9912,9984,10121,10202,10282,10379,10460,10542,10672,10746,10820,10952,11038,11115,11166,11220,11286,11357,11434,11505,11584,11657,11731,11801,11875,11976,12062,12136,12225,12317,12391,12464,12553,12604,12684,12751,12834,12918,12980,13044,13107,13176,13270,13371,13464,13562,13617,13675,13977,14145,14222,14296"}}, {"source": "D:\\AndroidBuildEnv\\.gradle\\caches\\8.11.1\\transforms\\b7fdc3266dd2bd73d6604182feb99497\\transformed\\exoplayer-ui-2.19.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1816,1937,2057,2126,2202,2272,2344,2429,2516,2579,2653,2707,2776,2824,2885,2943,3020,3084,3148,3208,3270,3335,3401,3467,3519,3578,3651,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1811,1932,2052,2121,2197,2267,2339,2424,2511,2574,2648,2702,2771,2819,2880,2938,3015,3079,3143,3203,3265,3330,3396,3462,3514,3573,3646,3719,3772"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,408,607,5209,5292,5377,5456,5549,5641,5718,5781,5873,5960,6023,6085,6146,6213,6329,6450,6570,6639,6715,6785,6857,6942,7029,7092,7816,7870,7939,7987,8048,8106,8183,8247,8311,8371,8433,8498,8564,8630,8682,8741,8814,8887", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "403,602,786,5287,5372,5451,5544,5636,5713,5776,5868,5955,6018,6080,6141,6208,6324,6445,6565,6634,6710,6780,6852,6937,7024,7087,7161,7865,7934,7982,8043,8101,8178,8242,8306,8366,8428,8493,8559,8625,8677,8736,8809,8882,8935"}}]}]}