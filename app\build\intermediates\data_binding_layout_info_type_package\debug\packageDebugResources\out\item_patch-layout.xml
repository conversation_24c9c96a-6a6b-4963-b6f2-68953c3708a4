<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_patch" modulePackage="com.bearmod.loader" filePath="app\src\main\res\layout\item_patch.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_patch_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="51"/></Target><Target id="@+id/tv_patch_name" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="27" endOffset="44"/></Target><Target id="@+id/tv_patch_description" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="39" endOffset="80"/></Target><Target id="@+id/tv_game_version" view="TextView"><Expressions/><location startLine="41" startOffset="8" endLine="50" endOffset="46"/></Target><Target id="@+id/tv_update_date" view="TextView"><Expressions/><location startLine="52" startOffset="8" endLine="61" endOffset="46"/></Target><Target id="@+id/btn_download" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="64" startOffset="8" endLine="74" endOffset="55"/></Target><Target id="@+id/tv_status" view="TextView"><Expressions/><location startLine="76" startOffset="8" endLine="91" endOffset="37"/></Target><Target id="@+id/btn_apply_patch" view="Button"><Expressions/><location startLine="93" startOffset="8" endLine="104" endOffset="49"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="106" startOffset="8" endLine="115" endOffset="69"/></Target></Targets></Layout>