package com.bearmod.loader.ui.auth;

import android.content.Intent;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;

import com.bearmod.loader.R;

import com.google.android.material.imageview.ShapeableImageView;

public class TapToUnlockActivity extends AppCompatActivity {

    private boolean unlocked = false;
    private ShapeableImageView glyphView;
    private TextView tapText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Enable edge-to-edge
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        
        setContentView(R.layout.activity_tap_unlock);

        glyphView = findViewById(R.id.glyphView);
        tapText = findViewById(R.id.tapText);

        // Set up shape appearance
        glyphView.setShapeAppearanceModel(
            glyphView.getShapeAppearanceModel()
                .toBuilder()
                .setAllCornerSizes(getResources().getDimensionPixelSize(R.dimen.glyph_corner_radius))
                .build()
        );

        // Start pulsing animation
        Animation pulse = AnimationUtils.loadAnimation(this, R.anim.pulse);
        glyphView.startAnimation(pulse);
        tapText.startAnimation(pulse);

        glyphView.setOnTouchListener((v, event) -> {
            if (!unlocked && event.getAction() == MotionEvent.ACTION_DOWN) {
                unlockLoader();
                return true;
            }
            return false;
        });
    }

    private void unlockLoader() {
        unlocked = true;

        // Stop pulsing animations
        glyphView.clearAnimation();
        tapText.clearAnimation();

        // Animate glyph unlock
        glyphView.animate()
                .rotationY(720)
                .alpha(0f)
                .setDuration(600)
                .withEndAction(() -> {
                    // Launch main UI
                    Intent intent = new Intent(this, com.bearmod.loader.ui.main.MainLoaderActivity.class);
                    startActivity(intent);
                    overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
                    finish();
                }).start();

        // Fade out text with scale
        tapText.animate()
                .alpha(0f)
                .scaleX(0.8f)
                .scaleY(0.8f)
                .setDuration(300)
                .start();
    }
} 