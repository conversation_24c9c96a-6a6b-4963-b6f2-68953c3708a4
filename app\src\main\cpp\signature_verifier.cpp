#include <jni.h>
#include <string>
#include <vector>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <android/log.h>

#define LOG_TAG "SignatureVerifier"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Known good signature hashes (SHA-256) - replace with your actual hashes
static const std::vector<std::string> TRUSTED_LOADER_HASHES = {
    "YOUR_LOADER_APP_SHA256_HASH_HERE",
    "YOUR_DEBUG_LOADER_SHA256_HASH_HERE"
};

static const std::vector<std::string> TRUSTED_PUBG_HASHES = {
    "PUBG_GLOBAL_SHA256_HASH_HERE",
    "PUBG_KR_SHA256_HASH_HERE", 
    "PUBG_TW_SHA256_HASH_HERE",
    "PUBG_VN_SHA256_HASH_HERE"
};

/**
 * Calculate SHA-256 hash of byte array
 */
std::string calculateSHA256(const unsigned char* data, size_t length) {
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256(data, length, hash);
    
    std::string result;
    char buf[3];
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        sprintf(buf, "%02x", hash[i]);
        result += buf;
    }
    return result;
}

/**
 * Get package signature hash
 */
std::string getPackageSignatureHash(JNIEnv* env, jobject context, jstring packageName) {
    try {
        // Get PackageManager
        jclass contextClass = env->GetObjectClass(context);
        jmethodID getPM = env->GetMethodID(contextClass, "getPackageManager", 
                                          "()Landroid/content/pm/PackageManager;");
        if (!getPM) {
            LOGE("Failed to get getPackageManager method");
            return "";
        }
        
        jobject pm = env->CallObjectMethod(context, getPM);
        if (!pm) {
            LOGE("Failed to get PackageManager");
            return "";
        }

        // Get PackageInfo with signatures
        jclass pmClass = env->GetObjectClass(pm);
        jmethodID getInfo = env->GetMethodID(pmClass, "getPackageInfo", 
                                           "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        if (!getInfo) {
            LOGE("Failed to get getPackageInfo method");
            return "";
        }

        // GET_SIGNATURES = 0x40
        jobject pkgInfo = env->CallObjectMethod(pm, getInfo, packageName, 0x40);
        if (!pkgInfo) {
            LOGE("Failed to get PackageInfo");
            return "";
        }

        // Get signatures array
        jclass pkgInfoClass = env->GetObjectClass(pkgInfo);
        jfieldID sigField = env->GetFieldID(pkgInfoClass, "signatures", 
                                          "[Landroid/content/pm/Signature;");
        if (!sigField) {
            LOGE("Failed to get signatures field");
            return "";
        }
        
        jobjectArray sigs = (jobjectArray)env->GetObjectField(pkgInfo, sigField);
        if (!sigs) {
            LOGE("No signatures found");
            return "";
        }

        // Get first signature
        jobject sig = env->GetObjectArrayElement(sigs, 0);
        if (!sig) {
            LOGE("Failed to get first signature");
            return "";
        }

        // Get signature bytes
        jclass sigClass = env->GetObjectClass(sig);
        jmethodID toByteArray = env->GetMethodID(sigClass, "toByteArray", "()[B");
        if (!toByteArray) {
            LOGE("Failed to get toByteArray method");
            return "";
        }
        
        jbyteArray byteArray = (jbyteArray)env->CallObjectMethod(sig, toByteArray);
        if (!byteArray) {
            LOGE("Failed to get signature byte array");
            return "";
        }

        // Get byte array data
        jsize length = env->GetArrayLength(byteArray);
        jbyte* bytes = env->GetByteArrayElements(byteArray, nullptr);
        if (!bytes) {
            LOGE("Failed to get byte array elements");
            return "";
        }

        // Calculate SHA-256 hash
        std::string hash = calculateSHA256((unsigned char*)bytes, length);
        
        // Release byte array
        env->ReleaseByteArrayElements(byteArray, bytes, JNI_ABORT);
        
        LOGI("Package signature hash: %s", hash.c_str());
        return hash;
        
    } catch (...) {
        LOGE("Exception in getPackageSignatureHash");
        return "";
    }
}

/**
 * Verify loader app signature
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_loader_security_NativeSecurityManager_verifyLoaderSignature(
        JNIEnv* env, jobject thiz, jobject context) {
    
    LOGI("Verifying loader app signature");
    
    // Get current package name
    jclass contextClass = env->GetObjectClass(context);
    jmethodID getPkg = env->GetMethodID(contextClass, "getPackageName", "()Ljava/lang/String;");
    jstring pkgName = (jstring)env->CallObjectMethod(context, getPkg);
    
    // Get signature hash
    std::string hash = getPackageSignatureHash(env, context, pkgName);
    if (hash.empty()) {
        LOGE("Failed to get loader signature hash");
        return JNI_FALSE;
    }
    
    // Check against trusted hashes
    for (const auto& trustedHash : TRUSTED_LOADER_HASHES) {
        if (hash == trustedHash) {
            LOGI("Loader signature verified successfully");
            return JNI_TRUE;
        }
    }
    
    LOGE("Loader signature verification failed");
    return JNI_FALSE;
}

/**
 * Verify PUBG Mobile app signature
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_loader_security_NativeSecurityManager_verifyPubgSignature(
        JNIEnv* env, jobject thiz, jobject context, jstring packageName) {
    
    const char* pkgNameStr = env->GetStringUTFChars(packageName, nullptr);
    LOGI("Verifying PUBG signature for: %s", pkgNameStr);
    
    // Get signature hash
    std::string hash = getPackageSignatureHash(env, context, packageName);
    if (hash.empty()) {
        LOGE("Failed to get PUBG signature hash for %s", pkgNameStr);
        env->ReleaseStringUTFChars(packageName, pkgNameStr);
        return JNI_FALSE;
    }
    
    // Check against trusted PUBG hashes
    for (const auto& trustedHash : TRUSTED_PUBG_HASHES) {
        if (hash == trustedHash) {
            LOGI("PUBG signature verified successfully for %s", pkgNameStr);
            env->ReleaseStringUTFChars(packageName, pkgNameStr);
            return JNI_TRUE;
        }
    }
    
    LOGE("PUBG signature verification failed for %s", pkgNameStr);
    env->ReleaseStringUTFChars(packageName, pkgNameStr);
    return JNI_FALSE;
}

/**
 * Verify shared certificate between loader and PUBG
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_loader_security_NativeSecurityManager_verifySharedCertificate(
        JNIEnv* env, jobject thiz, jobject context, jstring pubgPackageName) {
    
    LOGI("Verifying shared certificate");
    
    // Verify loader signature first
    if (!Java_com_bearmod_loader_security_NativeSecurityManager_verifyLoaderSignature(
            env, thiz, context)) {
        LOGE("Loader signature verification failed");
        return JNI_FALSE;
    }
    
    // Verify PUBG signature
    if (!Java_com_bearmod_loader_security_NativeSecurityManager_verifyPubgSignature(
            env, thiz, context, pubgPackageName)) {
        LOGE("PUBG signature verification failed");
        return JNI_FALSE;
    }
    
    LOGI("Shared certificate verification successful");
    return JNI_TRUE;
}

/**
 * Get signature hash for debugging
 */
extern "C" JNIEXPORT jstring JNICALL
Java_com_bearmod_loader_security_NativeSecurityManager_getSignatureHash(
        JNIEnv* env, jobject thiz, jobject context, jstring packageName) {
    
    std::string hash = getPackageSignatureHash(env, context, packageName);
    if (hash.empty()) {
        return nullptr;
    }
    
    return env->NewStringUTF(hash.c_str());
}

/**
 * Anti-tampering check
 */
extern "C" JNIEXPORT jboolean JNICALL
Java_com_bearmod_loader_security_NativeSecurityManager_performAntiTamperingCheck(
        JNIEnv* env, jobject thiz, jobject context) {
    
    LOGI("Performing anti-tampering check");
    
    // Check if running in debugger
    if (android_get_device_api_level() >= 23) {
        // Additional checks for newer Android versions
        // Check for Xposed, Frida, etc.
    }
    
    // Verify loader signature
    if (!Java_com_bearmod_loader_security_NativeSecurityManager_verifyLoaderSignature(
            env, thiz, context)) {
        LOGE("Anti-tampering check failed: Invalid signature");
        return JNI_FALSE;
    }
    
    LOGI("Anti-tampering check passed");
    return JNI_TRUE;
}
