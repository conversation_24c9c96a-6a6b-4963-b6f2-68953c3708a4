LOCAL_PATH := $(call my-dir)

# ==================== Main Library ====================
include $(CLEAR_VARS)

# Module name (must match Java System.loadLibrary())
LOCAL_MODULE := bearmod

# Compiler flags (preserve all your original flags)
LOCAL_CFLAGS := \
    -Wno-error=format-security \
    -fvisibility=hidden \
    -ffunction-sections \
    -fdata-sections \
    -w \
    -fpermissive

LOCAL_CPPFLAGS := \
    -std=c++17 \
    -Wno-error=c++11-narrowing \
    -fms-extensions \
    -frtti \
    -fexceptions

# Linker flags
LOCAL_LDFLAGS := -Wl,--gc-sections,--strip-all
LOCAL_LDLIBS := -llog -landroid -lz

# Source files (BearMod Loader + BearTrust integration)
LOCAL_SRC_FILES := \
    Main.cpp \
    Tools.cpp \
    md5.cpp \
    bear_trust_verifier.cpp

# Include paths
LOCAL_C_INCLUDES := \
    $(LOCAL_PATH)

# Build as shared library
include $(BUILD_SHARED_LIBRARY)
