package com.bearmod.targetapp;

import android.content.Context;
import android.util.Log;

/**
 * Native authentication interface for target mod apps
 * Provides JNI bridge to native shared certificate verification
 */
public class NativeAuth {
    
    private static final String TAG = "NativeAuth";
    private static boolean isLibraryLoaded = false;
    
    static {
        try {
            // Load your native library (adjust name as needed)
            System.loadLibrary("bearmod_target");
            isLibraryLoaded = true;
            Log.d(TAG, "Native authentication library loaded successfully");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "Failed to load native authentication library", e);
            isLibraryLoaded = false;
        }
    }
    
    /**
     * Check if native library is available
     * @return true if native library is loaded, false otherwise
     */
    public static boolean isNativeLibraryAvailable() {
        return isLibraryLoaded;
    }
    
    /**
     * Verify shared certificate between BearMod Loader and target app
     * This is the main function you call to check if the app was launched by trusted loader
     * 
     * @param context Application context
     * @return true if launched by trusted BearMod Loader, false otherwise
     */
    public static boolean verifySharedCertificate(Context context) {
        if (!isLibraryLoaded) {
            Log.e(TAG, "Native library not available for shared certificate verification");
            return false;
        }
        
        try {
            boolean result = nativeVerifySharedCertificate(context);
            Log.d(TAG, "Shared certificate verification result: " + result);
            return result;
        } catch (Exception e) {
            Log.e(TAG, "Shared certificate verification failed", e);
            return false;
        }
    }
    
    /**
     * Check if BearMod Loader is trusted (has valid signature)
     * @param context Application context
     * @return true if loader is trusted, false otherwise
     */
    public static boolean isLoaderTrusted(Context context) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available for loader trust check");
            return false;
        }
        
        try {
            return nativeIsLoaderTrusted(context);
        } catch (Exception e) {
            Log.e(TAG, "Loader trust check failed", e);
            return false;
        }
    }
    
    /**
     * Get signature hash for any package (debugging purposes)
     * @param context Application context
     * @param packageName Package name to get signature for
     * @return Signature hash or null if failed
     */
    public static String getPackageSignatureHash(Context context, String packageName) {
        if (!isLibraryLoaded) {
            Log.w(TAG, "Native library not available for signature hash retrieval");
            return null;
        }
        
        try {
            return nativeGetPackageSignatureHash(context, packageName);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get package signature hash for " + packageName, e);
            return null;
        }
    }
    
    /**
     * Comprehensive authentication check for target mod
     * This combines shared certificate verification with additional security checks
     * 
     * @param context Application context
     * @return AuthenticationResult with detailed information
     */
    public static AuthenticationResult performAuthentication(Context context) {
        Log.d(TAG, "Performing comprehensive authentication check");
        
        // Check if native library is available
        if (!isLibraryLoaded) {
            return new AuthenticationResult(false, "LIBRARY_ERROR", 
                "Native authentication library not available", 
                "Enhanced security features require native library");
        }
        
        try {
            // 1. Verify shared certificate
            boolean sharedCertValid = verifySharedCertificate(context);
            
            if (sharedCertValid) {
                // Trusted launch from BearMod Loader
                Log.d(TAG, "Trusted launch detected - BearMod Loader verification passed");
                return new AuthenticationResult(true, "TRUSTED_LOADER", 
                    "Authenticated via BearMod Loader", 
                    "App launched by trusted BearMod Loader with valid signature");
            } else {
                // Not launched by trusted loader
                Log.d(TAG, "Not launched by trusted loader - checking loader availability");
                
                // Check if loader is installed but not trusted
                boolean loaderTrusted = isLoaderTrusted(context);
                if (!loaderTrusted) {
                    return new AuthenticationResult(false, "UNTRUSTED_LOADER", 
                        "BearMod Loader signature verification failed", 
                        "BearMod Loader is not installed or has invalid signature");
                } else {
                    return new AuthenticationResult(false, "UNAUTHORIZED_LAUNCH", 
                        "App not launched by BearMod Loader", 
                        "App must be launched through BearMod Loader for trusted access");
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Authentication check failed", e);
            return new AuthenticationResult(false, "AUTH_ERROR", 
                "Authentication error: " + e.getMessage(), 
                "An error occurred during authentication verification");
        }
    }
    
    /**
     * Log signature information for debugging
     * Call this during development to get signature hashes for configuration
     * 
     * @param context Application context
     */
    public static void logSignatureInformation(Context context) {
        Log.d(TAG, "=== Native Signature Information ===");
        
        // Log current app signature
        String currentAppHash = getPackageSignatureHash(context, context.getPackageName());
        Log.d(TAG, "Current app signature: " + currentAppHash);
        
        // Log BearMod Loader signature if installed
        String loaderHash = getPackageSignatureHash(context, "com.bearmod.loader");
        Log.d(TAG, "BearMod Loader signature: " + loaderHash);
        
        // Log PUBG Mobile signatures if installed
        String[] pubgPackages = {
            "com.tencent.ig",
            "com.pubg.krmobile",
            "com.rekoo.pubgm",
            "com.vng.pubgmobile"
        };
        
        for (String pkg : pubgPackages) {
            String hash = getPackageSignatureHash(context, pkg);
            if (hash != null) {
                Log.d(TAG, "PUBG " + pkg + " signature: " + hash);
            }
        }
        
        Log.d(TAG, "=== End Native Signature Information ===");
    }
    
    // Native method declarations
    private static native boolean nativeVerifySharedCertificate(Context context);
    private static native boolean nativeIsLoaderTrusted(Context context);
    private static native String nativeGetPackageSignatureHash(Context context, String packageName);
    
    /**
     * Authentication result class
     */
    public static class AuthenticationResult {
        public final boolean isAuthenticated;
        public final String resultCode;
        public final String message;
        public final String details;
        
        public AuthenticationResult(boolean isAuthenticated, String resultCode, String message, String details) {
            this.isAuthenticated = isAuthenticated;
            this.resultCode = resultCode;
            this.message = message;
            this.details = details;
        }
        
        /**
         * Check if app was launched by trusted loader
         */
        public boolean isTrustedLaunch() {
            return isAuthenticated && "TRUSTED_LOADER".equals(resultCode);
        }
        
        /**
         * Check if authentication failed due to security issues
         */
        public boolean isSecurityFailure() {
            return !isAuthenticated && ("UNTRUSTED_LOADER".equals(resultCode) || 
                                       "UNAUTHORIZED_LAUNCH".equals(resultCode));
        }
        
        /**
         * Check if authentication failed due to technical issues
         */
        public boolean isTechnicalFailure() {
            return !isAuthenticated && ("LIBRARY_ERROR".equals(resultCode) || 
                                       "AUTH_ERROR".equals(resultCode));
        }
        
        @Override
        public String toString() {
            return "AuthenticationResult{" +
                    "isAuthenticated=" + isAuthenticated +
                    ", resultCode='" + resultCode + '\'' +
                    ", message='" + message + '\'' +
                    ", details='" + details + '\'' +
                    '}';
        }
    }
}
