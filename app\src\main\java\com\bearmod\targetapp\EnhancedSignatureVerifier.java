package com.bearmod.targetapp;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.util.Log;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Enhanced signature verifier with BearMod integration and anti-bypass protection
 * This class provides multiple layers of signature verification to prevent Frida bypasses
 */
public class EnhancedSignatureVerifier {
    private static final String TAG = "EnhancedSignatureVerifier";
    
    // Expected signature hashes for your target app (replace with actual values)
    private static final Set<String> TRUSTED_SIGNATURES = new HashSet<>(Arrays.asList(
        "YOUR_TARGET_APP_RELEASE_SIGNATURE_SHA256",
        "YOUR_TARGET_APP_DEBUG_SIGNATURE_SHA256"
    ));
    
    // BearMod Loader signature for verification
    private static final Set<String> TRUSTED_LOADER_SIGNATURES = new HashSet<>(Arrays.asList(
        "YOUR_BEARMOD_LOADER_SIGNATURE_SHA256"
    ));
    
    // Anti-bypass verification counter
    private static int verificationCallCount = 0;
    private static boolean lastVerificationResult = false;
    
    /**
     * Enhanced signature verification with anti-bypass protection
     * @param context Application context
     * @return true if signature is valid and not bypassed, false otherwise
     */
    public static boolean isSignatureValidEnhanced(Context context) {
        verificationCallCount++;
        
        try {
            // Multiple verification methods to prevent bypasses
            boolean method1 = verifySignatureMethod1(context);
            boolean method2 = verifySignatureMethod2(context);
            boolean method3 = verifySignatureNative(context);
            
            // All methods must agree
            boolean result = method1 && method2 && method3;
            
            // Anti-bypass check: verify consistency
            if (verificationCallCount > 1 && result != lastVerificationResult) {
                Log.e(TAG, "Signature verification result inconsistency detected - possible bypass");
                return false;
            }
            
            lastVerificationResult = result;
            
            if (result) {
                Log.d(TAG, "Enhanced signature verification passed");
            } else {
                Log.e(TAG, "Enhanced signature verification failed");
            }
            
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "Enhanced signature verification error", e);
            return false;
        }
    }
    
    /**
     * Primary signature verification method
     */
    private static boolean verifySignatureMethod1(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                Log.e(TAG, "Method1: No signatures found");
                return false;
            }
            
            String signatureHash = getSignatureHash(packageInfo.signatures[0]);
            boolean isValid = TRUSTED_SIGNATURES.contains(signatureHash);
            
            Log.d(TAG, "Method1: Signature hash: " + signatureHash + ", Valid: " + isValid);
            return isValid;
            
        } catch (Exception e) {
            Log.e(TAG, "Method1: Signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Secondary signature verification method (different approach)
     */
    private static boolean verifySignatureMethod2(Context context) {
        try {
            // Use different flags to get signatures
            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
                    context.getPackageName(), 0x40); // GET_SIGNATURES constant
            
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                Log.e(TAG, "Method2: No signatures found");
                return false;
            }
            
            // Calculate hash differently to detect tampering
            byte[] signatureBytes = packageInfo.signatures[0].toByteArray();
            String signatureHash = calculateSHA256(signatureBytes);
            boolean isValid = TRUSTED_SIGNATURES.contains(signatureHash);
            
            Log.d(TAG, "Method2: Signature hash: " + signatureHash + ", Valid: " + isValid);
            return isValid;
            
        } catch (Exception e) {
            Log.e(TAG, "Method2: Signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Native signature verification (if available)
     */
    private static boolean verifySignatureNative(Context context) {
        try {
            // Try to use native verification if available
            // This would call your native library for signature verification
            // For now, we'll use a Java fallback
            
            return verifySignatureFallback(context);
            
        } catch (Exception e) {
            Log.e(TAG, "Native signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Fallback signature verification
     */
    private static boolean verifySignatureFallback(Context context) {
        try {
            // Direct signature verification without using PackageManager methods
            // that are commonly hooked by Frida
            
            String packageName = context.getPackageName();
            PackageManager pm = context.getPackageManager();
            
            // Get package info
            PackageInfo info = pm.getPackageInfo(packageName, PackageManager.GET_SIGNATURES);
            
            if (info.signatures != null && info.signatures.length > 0) {
                String hash = getSignatureHash(info.signatures[0]);
                return TRUSTED_SIGNATURES.contains(hash);
            }
            
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "Fallback signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Verify BearMod Loader signature
     * @param context Application context
     * @return true if BearMod Loader has valid signature, false otherwise
     */
    public static boolean verifyBearModLoaderSignature(Context context) {
        try {
            String loaderPackageName = "com.bearmod.loader"; // Your loader package name
            
            // Check if loader is installed
            PackageInfo loaderInfo = context.getPackageManager().getPackageInfo(
                    loaderPackageName, PackageManager.GET_SIGNATURES);
            
            if (loaderInfo.signatures == null || loaderInfo.signatures.length == 0) {
                Log.e(TAG, "BearMod Loader has no signatures");
                return false;
            }
            
            String loaderSignatureHash = getSignatureHash(loaderInfo.signatures[0]);
            boolean isValid = TRUSTED_LOADER_SIGNATURES.contains(loaderSignatureHash);
            
            Log.d(TAG, "BearMod Loader signature: " + loaderSignatureHash + ", Valid: " + isValid);
            return isValid;
            
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "BearMod Loader not installed", e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "BearMod Loader signature verification failed", e);
            return false;
        }
    }
    
    /**
     * Comprehensive security check including BearToken and signature verification
     * @param context Application context
     * @return SecurityResult with detailed information
     */
    public static SecurityResult performComprehensiveSecurityCheck(Context context) {
        Log.d(TAG, "Performing comprehensive security check");
        
        // 1. Check target app signature
        if (!isSignatureValidEnhanced(context)) {
            return new SecurityResult(false, "Target app signature verification failed", 
                "App signature does not match trusted signatures");
        }
        
        // 2. Check BearMod Loader signature
        if (!verifyBearModLoaderSignature(context)) {
            Log.w(TAG, "BearMod Loader signature verification failed - may not be installed");
            // Don't fail here as loader might not be installed
        }
        
        // 3. Check for BearToken (if available)
        BearTokenResult bearTokenResult = checkBearToken(context);
        
        // 4. Anti-hooking detection
        if (detectPotentialHooking()) {
            return new SecurityResult(false, "Potential hooking detected", 
                "Suspicious activity detected that may indicate tampering");
        }
        
        return new SecurityResult(true, "Comprehensive security check passed", 
            "Auth method: " + (bearTokenResult.isValid ? "BearToken" : "Standard"));
    }
    
    /**
     * Check for BearToken from BearMod Loader
     */
    private static BearTokenResult checkBearToken(Context context) {
        try {
            // This would integrate with your TargetModAuth class
            // For now, we'll return a placeholder result
            return new BearTokenResult(false, "BearToken check not implemented");
        } catch (Exception e) {
            Log.e(TAG, "BearToken check failed", e);
            return new BearTokenResult(false, "BearToken check error: " + e.getMessage());
        }
    }
    
    /**
     * Detect potential hooking/tampering
     */
    private static boolean detectPotentialHooking() {
        try {
            // Check for suspicious call patterns
            if (verificationCallCount > 10) {
                Log.w(TAG, "Excessive signature verification calls detected");
                return true;
            }
            
            // Check for Frida-related processes (basic check)
            // This would be enhanced with native detection
            
            return false;
            
        } catch (Exception e) {
            Log.e(TAG, "Hooking detection failed", e);
            return false;
        }
    }
    
    /**
     * Get signature hash using SHA-256
     */
    public static String getSignatureHash(Signature signature) {
        try {
            byte[] signatureBytes = signature.toByteArray();
            return calculateSHA256(signatureBytes);
        } catch (Exception e) {
            Log.e(TAG, "Failed to get signature hash", e);
            return "";
        }
    }
    
    /**
     * Calculate SHA-256 hash
     */
    private static String calculateSHA256(byte[] data) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] digest = md.digest(data);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "SHA-256 algorithm not found", e);
            return "";
        }
    }
    
    /**
     * Security check result
     */
    public static class SecurityResult {
        public final boolean isSecure;
        public final String message;
        public final String details;
        
        public SecurityResult(boolean isSecure, String message, String details) {
            this.isSecure = isSecure;
            this.message = message;
            this.details = details;
        }
        
        @Override
        public String toString() {
            return "SecurityResult{isSecure=" + isSecure + ", message='" + message + "'}";
        }
    }
    
    /**
     * BearToken check result
     */
    private static class BearTokenResult {
        public final boolean isValid;
        public final String message;
        
        public BearTokenResult(boolean isValid, String message) {
            this.isValid = isValid;
            this.message = message;
        }
    }
}
