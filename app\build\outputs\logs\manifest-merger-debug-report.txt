-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:99:9-107:20
	android:grantUriPermissions
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:13-47
	android:authorities
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:101:13-64
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:102:13-37
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:100:13-62
manifest
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-111:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-111:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-111:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-111:12
MERGED from [androidx.databinding:viewbinding:8.10.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3ed499b067677bc8aba1f7638863683\transformed\viewbinding-8.10.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5726a729d57e3701914f576476bac454\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5606969ee47eab2a73779a52b526778\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0817d30fadf18d76b47bcdf841f67615\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4a1e893a995f6a58f7d5e0e2b76f6f2\transformed\navigation-ui-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c66bbe2ba25b5a719c47c2efc8dba1b4\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ef7b832545248bde80afca51104877d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0957f9b8573f6b47549742aecd39688e\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\93651dcfea9155ae73e717938583485b\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5798c03ce18352d930661264524dff86\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b7fdc3266dd2bd73d6604182feb99497\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f313c980208ef502340a69b9d5b3a65c\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\99c6c94c3d8e08f53ea550fcad6e7157\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bab17e508190d53482501d234406c6b5\transformed\fragment-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6356cbc0e65a962ba247e25511f5d906\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\45f22f727c679be5214bf583be8ed144\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\339f0ea61d8a6306bd55f549bb75c4d5\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cdb4f4d97de4f3bb947c4bde4dbf1220\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96ee932b5376bae21d77b4fc6db89631\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\34b734569ec2fe2b468b73ce2bb9b529\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\922d33f919fe961fe77679f6d2f35f9d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0fbb3b211719ae958d97451a3183ace9\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6695614451976994491f11c39abcce61\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3af46e84777bcd36e645549e75b56457\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\26be560e9ef01f7e364ba7c5215432bc\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\22791bd86d76463413ecfdee575332ad\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3fcb5dc4e9abc517aedcb5fc52a1729\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41ecbfe5a5e3508966db1fbd732a1548\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4513d30e06476a0f1149dafe7a246eac\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4e672e8c6d97ea00c84bd8e756ea36e\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4808c427e838e98fe6acbf0a4278ade\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5bbdf0c494f35e618481c6837f0a746\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6441eb97bf9e700a142cb39f30110b83\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\806fe9648c6cce1cb4bf5b8eec4e4e97\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\66a9ccbd348e1d6b7897ad4ba64e258a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0887e407a2dd67a120df4aa0d7ccdffa\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c0a00c0c42a46d30408269b4088e9950\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4a7ec5e033837f3f68379b265aea0192\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f6c54e1da2ec9c8ee275643a44333038\transformed\shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5984dfa9289185364acc030aade2db8a\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bcd0827a4251f546bfdf7f4c32f9ef6f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\97535ac83451f328bbaa041d0d70e945\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fd5b71f0f43b49ea68ded67cd5b2f138\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1dd6d02f59940b6d75faa9312e99b8e3\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e26a53be7a7916f10f130fb66f1ee131\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7b93ebc42d048d238f4adb237df20227\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cb13b020c0d38d87f0347711efcc50ec\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cd0d0e6726c53a43dc29e6c7c159172f\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\43f391cecb985a27d6bb078c5ee1b4a8\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4ff71d02c4960793b0365d811a66bb3\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ba5e66eb6f83825b0d07643743a9aa7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ded6a0e4e424f1acfc5a5e666a7ca955\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:16:5-76
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:16:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-75
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:5-90
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:21:22-87
uses-permission#android.permission.VIBRATE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:24:5-66
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:24:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:27:5-68
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:27:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-77
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-74
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-80
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-77
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:5-83
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:32:22-80
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-34:47
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:9-44
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-72
application
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:36:5-109:19
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:36:5-109:19
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-54
	android:icon
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-43
	android:networkSecurityConfig
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-35
	android:label
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-41
	android:fullBackupContent
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-54
	tools:targetApi
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:48:9-29
	android:allowBackup
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-35
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-48
	android:dataExtractionRules
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-65
	android:usesCleartextTraffic
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-44
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:9-46
activity#com.bearmod.loader.ui.splash.SplashActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:51:9-60:20
	android:screenOrientation
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-52
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-36
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:13-59:29
action#android.intent.action.MAIN
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:17-69
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:17-77
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:27-74
activity#com.bearmod.loader.ui.auth.LoginActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:63:9-67:58
	android:windowSoftInputMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:67:13-55
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:64:13-50
activity#com.bearmod.loader.ui.auth.TapToUnlockActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:70:9-73:67
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:71:13-56
activity#com.bearmod.loader.ui.main.MainLoaderActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:76:9-80:46
	android:launchMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:80:13-43
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:77:13-55
activity#com.bearmod.loader.ui.download.ModernDownloadActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:83:9-87:46
	android:launchMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:87:13-43
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:84:13-63
activity#com.bearmod.loader.ui.settings.SettingsActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:92:9-96:55
	android:screenOrientation
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:13-52
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:94:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:95:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:93:13-57
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-106:54
	android:resource
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:17-51
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:17-67
uses-sdk
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3ed499b067677bc8aba1f7638863683\transformed\viewbinding-8.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3ed499b067677bc8aba1f7638863683\transformed\viewbinding-8.10.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5726a729d57e3701914f576476bac454\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5726a729d57e3701914f576476bac454\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5606969ee47eab2a73779a52b526778\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5606969ee47eab2a73779a52b526778\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0817d30fadf18d76b47bcdf841f67615\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0817d30fadf18d76b47bcdf841f67615\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4a1e893a995f6a58f7d5e0e2b76f6f2\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4a1e893a995f6a58f7d5e0e2b76f6f2\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\36f784ecfa4226c5c1c4c160bbf08ede\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7f2ef758f6a04afb9302eca7513c15a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfa176bbe8c0532b953ba0d02808261e\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3d9447df2a5d6146254c6a3be3ac0218\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2baa16814b930be5ec3f7d400737a6ef\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c66bbe2ba25b5a719c47c2efc8dba1b4\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c66bbe2ba25b5a719c47c2efc8dba1b4\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ef7b832545248bde80afca51104877d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5ef7b832545248bde80afca51104877d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0957f9b8573f6b47549742aecd39688e\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0957f9b8573f6b47549742aecd39688e\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\93651dcfea9155ae73e717938583485b\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\93651dcfea9155ae73e717938583485b\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5798c03ce18352d930661264524dff86\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5798c03ce18352d930661264524dff86\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b7fdc3266dd2bd73d6604182feb99497\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b7fdc3266dd2bd73d6604182feb99497\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f313c980208ef502340a69b9d5b3a65c\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f313c980208ef502340a69b9d5b3a65c\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\99c6c94c3d8e08f53ea550fcad6e7157\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\99c6c94c3d8e08f53ea550fcad6e7157\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bab17e508190d53482501d234406c6b5\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bab17e508190d53482501d234406c6b5\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5496ec63c9174abd1dc8b0fc3710ed02\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6356cbc0e65a962ba247e25511f5d906\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6356cbc0e65a962ba247e25511f5d906\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\45f22f727c679be5214bf583be8ed144\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\45f22f727c679be5214bf583be8ed144\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\339f0ea61d8a6306bd55f549bb75c4d5\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\339f0ea61d8a6306bd55f549bb75c4d5\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cdb4f4d97de4f3bb947c4bde4dbf1220\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cdb4f4d97de4f3bb947c4bde4dbf1220\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96ee932b5376bae21d77b4fc6db89631\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\96ee932b5376bae21d77b4fc6db89631\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\34b734569ec2fe2b468b73ce2bb9b529\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\34b734569ec2fe2b468b73ce2bb9b529\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b864ccb2f822d002c406cc2f985c1a8e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f565edc63d13869b58e36478c64896bd\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\922d33f919fe961fe77679f6d2f35f9d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\922d33f919fe961fe77679f6d2f35f9d\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cecee5ff19b1c9dd8043a856b50a6761\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a217ecd4781a3ce11743e4dbcb102f86\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\484ec91c8796ba9c3a775e49700d03f7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2709039af580017be576c1c513f645f9\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ae90f494b6c922c820e60a9a2464e118\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\24e7b313ab1ad1f3b82d240cba851985\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a6e8d28a1b8946311be523d5e5dad64d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\29d049250d15d1cc2e3e6acdb70ade9b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0fbb3b211719ae958d97451a3183ace9\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0fbb3b211719ae958d97451a3183ace9\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6695614451976994491f11c39abcce61\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6695614451976994491f11c39abcce61\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3af46e84777bcd36e645549e75b56457\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3af46e84777bcd36e645549e75b56457\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\26be560e9ef01f7e364ba7c5215432bc\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\26be560e9ef01f7e364ba7c5215432bc\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fdb62b6838fcf913e5e5ca4d6ff4db5\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\22791bd86d76463413ecfdee575332ad\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\22791bd86d76463413ecfdee575332ad\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3fcb5dc4e9abc517aedcb5fc52a1729\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c3fcb5dc4e9abc517aedcb5fc52a1729\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41ecbfe5a5e3508966db1fbd732a1548\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\41ecbfe5a5e3508966db1fbd732a1548\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4513d30e06476a0f1149dafe7a246eac\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4513d30e06476a0f1149dafe7a246eac\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4e672e8c6d97ea00c84bd8e756ea36e\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4e672e8c6d97ea00c84bd8e756ea36e\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4808c427e838e98fe6acbf0a4278ade\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4808c427e838e98fe6acbf0a4278ade\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5bbdf0c494f35e618481c6837f0a746\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5bbdf0c494f35e618481c6837f0a746\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6441eb97bf9e700a142cb39f30110b83\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6441eb97bf9e700a142cb39f30110b83\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\806fe9648c6cce1cb4bf5b8eec4e4e97\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\806fe9648c6cce1cb4bf5b8eec4e4e97\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\66a9ccbd348e1d6b7897ad4ba64e258a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\66a9ccbd348e1d6b7897ad4ba64e258a\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0887e407a2dd67a120df4aa0d7ccdffa\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0887e407a2dd67a120df4aa0d7ccdffa\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c0a00c0c42a46d30408269b4088e9950\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c0a00c0c42a46d30408269b4088e9950\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4a7ec5e033837f3f68379b265aea0192\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4a7ec5e033837f3f68379b265aea0192\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f6c54e1da2ec9c8ee275643a44333038\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f6c54e1da2ec9c8ee275643a44333038\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5984dfa9289185364acc030aade2db8a\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5984dfa9289185364acc030aade2db8a\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bcd0827a4251f546bfdf7f4c32f9ef6f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bcd0827a4251f546bfdf7f4c32f9ef6f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\97535ac83451f328bbaa041d0d70e945\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\97535ac83451f328bbaa041d0d70e945\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fd5b71f0f43b49ea68ded67cd5b2f138\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fd5b71f0f43b49ea68ded67cd5b2f138\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1dd6d02f59940b6d75faa9312e99b8e3\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1dd6d02f59940b6d75faa9312e99b8e3\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\550c9b8c1d2b0a348b84b516683c5512\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d11e81520834d9dfb0895653b94937a2\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dec98ac4ba765f12a9785ccdd5ecb941\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\71efae2bfde541ccae36b046a3cfada7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d177f6cb58feef797cae4f05b9f81805\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e26a53be7a7916f10f130fb66f1ee131\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e26a53be7a7916f10f130fb66f1ee131\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7b93ebc42d048d238f4adb237df20227\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\7b93ebc42d048d238f4adb237df20227\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4af8c4b53958b4d960f129d231e06c9c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cb13b020c0d38d87f0347711efcc50ec\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cb13b020c0d38d87f0347711efcc50ec\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cd0d0e6726c53a43dc29e6c7c159172f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cd0d0e6726c53a43dc29e6c7c159172f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\305eb8a0c88c9db190f7307baa693330\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\73f24adaf1d66e3610346a3af9c9b2d4\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5762835ff59d5aff87507c49184b5bb3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\43f391cecb985a27d6bb078c5ee1b4a8\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\43f391cecb985a27d6bb078c5ee1b4a8\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4ff71d02c4960793b0365d811a66bb3\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c4ff71d02c4960793b0365d811a66bb3\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ba5e66eb6f83825b0d07643743a9aa7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ba5e66eb6f83825b0d07643743a9aa7\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ded6a0e4e424f1acfc5a5e666a7ca955\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ded6a0e4e424f1acfc5a5e666a7ca955\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ee04c43307996fe60aaa8b18a7041ad5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d7c0aef1852a339416f0e53fc8642d9b\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\30f96c7e160663e485965093010292db\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c26bd73939d67d44b19abf2d837cad8\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\cfcf24ccfb230d11ef9bef592956f67b\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8289b77b3b2a4016322b2c4888858c15\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\47d2512ee5e96132f85db12b66ec774d\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\613bfd817d95baf9703fcf5f9610c97e\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
