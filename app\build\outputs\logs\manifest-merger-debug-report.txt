-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:102:9-110:20
	android:grantUriPermissions
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:106:13-47
	android:authorities
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:104:13-64
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:105:13-37
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:103:13-62
manifest
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:1-114:12
MERGED from [androidx.databinding:viewbinding:8.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4bbcc1fb3c06e7b70aa5915c974a846\transformed\viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5a5909ca6a2b6662eeddc7e2dc5f5205\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b966acc70408c9538eb7dab90b53a08\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2e4e8285ce98bf95914496884c66bc1a\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8fcff5946e9cb42bb9e1450a210c2616\transformed\navigation-ui-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9f99db3bdee5091140633361bed826be\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7d65cf44c341bdeaf24467708c7f0f6\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a3916939ede9d9482ba416c66b21778\transformed\lottie-6.6.6\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68cd95bb7c20f8a65475dd90790a85f2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\21d61e9b45cfab2ee4b69c56a0b52398\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\67870811a409034376e44ceca177f729\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\545b593b4e826b1db6b9c41cd1cc5e09\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e50512e0cf6fcbdd1c74ca21f173cef3\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\98abb0607de596cfeff3dfe20eb3948c\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\193256b637e2d97f6c391bcf568467d4\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8c95fb1bf71448479a8aeb756e068ef5\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\447f28505bcf9d53ca7c0e300fee33db\transformed\recyclerview-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5462eaf29936baed5e145b725030de94\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\06f0b412b6fc7c9c8ed484fb19a528a2\transformed\fragment-1.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3fe42a804cb5af25dea3fc8d65d3d68\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\793839e92b57d80f2e40e130d0af7c6a\transformed\activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c81e199687af6aeb4b83eabecac6cc95\transformed\activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc139b5a61a792785e024a87543773be\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b52b2a4da0d92372a1f82cdd58358bd6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fac29afda0ad1022119fc7d47a0eef0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\593add07a452c801b697195937a18578\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4b15690e97d29d3c35909bd6ff61cfee\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fc00f2efc926a01364041610e9aebae\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ffd8c4e10502b265d2b5073b22332982\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dac2e46243e93ae19dab1851f921943a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0be0b8c4cdb946796e8dbbc99cfb9ae9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\463f503ae32afb9a2de5da3dc8464499\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1cd3501eecb59b5fa35934048a4d8046\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6982b2f75d56037316f8252d2512229b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\48260ca17b137f37e3e28513476807a1\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bb7078a00c65debf8f97887d40594730\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\813f8bb037f8627d7b4a7e1aa1a7d1b1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d43b932bc584af2a05c4860e51480c8\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\09ce69ef417a64b32cd3325b7ee0536a\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec874383389a1efb16f74c2e3a0738ff\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3c15d353adc78fed5a819759474b0fb8\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\eef25481716fc7890be7aca21c0506bf\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7c7ef9a9d85798cd48d7b90be7c3775\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfbbf048d6f0b90a71e31c29dcd80787\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d178b2a58bc54e5baaea6a1e0b1b266c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8af36b0db4602ae08f98baa9f04a6259\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c651d4ce5e7ad42ae371fe4ed21e365b\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3cbc40a7c21a93b0ec050bd489ff2274\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\98c8fe482b576c279f955e254c804219\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ceaf8f83bb4865de959da2abbb6208a\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ceb8875b6bbadf52ff036bfa73df36f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\05a0de893e977070e3ae6a5d2b874d38\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9fc6bc21b2f8c070d0b20c407b971ae7\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2c98378d07946bd6d1cc1c50bef38ec6\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\51c8db7fe8e70cfb879a5c4442ff2c55\transformed\shimmer-0.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fed4f28d03252dc11237901a3ab86cce\transformed\room-ktx-2.7.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1472e30982fc6c6f85e544376c621099\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f00a452c1fa629d99407b8a5c016f99f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\42060d313688d1f4cb1c99bc53032ca9\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5531d388bd07a39db47990824789124\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e784328f42b70f4fae8c82c1687066bb\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\573843d77d4a88fcda0ded0a8cbbf042\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c34f2234addedd19ec24b7a3344aa0c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2d8d7dcd1d1acfbca97f977538c7a854\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd0401f989ab95fe9f964623ff94007\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\046a69a06664e4450d93dbcddc9fcf72\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\89482ec0869eb517a3ad4d95dd17c65f\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\53a6d82573805f897e4f0e6b5e7b9d94\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aaa37a81498d6eabdb9cf9fffe43f4a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\06fface0e3f5c6ec1135f468ac3ebb4e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d076bc2c78031033c89deaf3d4553991\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\79ecfa30b82b5ca3cb4e21186ea2a78c\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a41c8f74917e3a48a13b97ebaea973a2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f30d4a7a42cb77abd4ef34f13bf5aef2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3d69e8b3f15130dc9e646535e2143e3\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0f1d202f6b39ba837b6128b22dfad4ad\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6d4bb93bcd09957324150d908ac1bd7f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1123ea94c0e9d129b784050deb0865f7\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\eef25481716fc7890be7aca21c0506bf\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\eef25481716fc7890be7aca21c0506bf\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\573843d77d4a88fcda0ded0a8cbbf042\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\573843d77d4a88fcda0ded0a8cbbf042\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:5-11:38
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:11:9-35
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:5-14:40
	android:maxSdkVersion
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:13:9-35
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:14:9-37
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:5-76
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:17:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:5-75
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:18:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:5-75
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:19:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:5-90
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:22:22-87
uses-permission#android.permission.VIBRATE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:5-66
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:25:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:5-68
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:28:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:5-77
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:29:22-74
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:5-80
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:30:22-77
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:5-83
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:33:22-80
uses-permission#android.permission.INSTALL_PACKAGES
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:5-35:47
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:35:9-44
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:34:22-72
application
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-112:19
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:37:5-112:19
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9f99db3bdee5091140633361bed826be\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9f99db3bdee5091140633361bed826be\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7d65cf44c341bdeaf24467708c7f0f6\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7d65cf44c341bdeaf24467708c7f0f6\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a3916939ede9d9482ba416c66b21778\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a3916939ede9d9482ba416c66b21778\transformed\lottie-6.6.6\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aaa37a81498d6eabdb9cf9fffe43f4a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aaa37a81498d6eabdb9cf9fffe43f4a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:44:9-54
	android:icon
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:42:9-43
	android:networkSecurityConfig
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:48:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:45:9-35
	android:label
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:43:9-41
	android:fullBackupContent
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:41:9-54
	tools:targetApi
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:49:9-29
	android:allowBackup
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:39:9-35
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:46:9-48
	android:dataExtractionRules
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:40:9-65
	android:usesCleartextTraffic
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:47:9-44
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:38:9-46
activity#com.bearmod.loader.ui.splash.SplashActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:52:9-62:20
	android:screenOrientation
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:56:13-52
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:54:13-36
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:57:13-42
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:55:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:53:13-53
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:58:13-61:29
action#android.intent.action.MAIN
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:17-69
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:59:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:17-77
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:60:27-74
activity#com.bearmod.loader.ui.auth.LoginActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:65:9-69:58
	android:windowSoftInputMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:69:13-55
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:67:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:68:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:66:13-50
activity#com.bearmod.loader.ui.auth.TapToUnlockActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:72:9-75:67
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:74:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:75:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:73:13-56
activity#com.bearmod.loader.ui.main.MainLoaderActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:78:9-82:46
	android:launchMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:82:13-43
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:80:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:81:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:79:13-55
activity#com.bearmod.loader.ui.download.ModernDownloadActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:85:9-89:46
	android:launchMode
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:89:13-43
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:87:13-37
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:88:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:86:13-63
activity#com.bearmod.loader.ui.settings.SettingsActivity
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:94:9-99:45
	android:screenOrientation
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:98:13-52
	android:exported
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:99:13-42
	android:theme
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:97:13-64
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:95:13-57
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:107:13-109:54
	android:resource
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:109:17-51
	android:name
		ADDED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml:108:17-67
uses-sdk
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4bbcc1fb3c06e7b70aa5915c974a846\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d4bbcc1fb3c06e7b70aa5915c974a846\transformed\viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5a5909ca6a2b6662eeddc7e2dc5f5205\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5a5909ca6a2b6662eeddc7e2dc5f5205\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b966acc70408c9538eb7dab90b53a08\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8b966acc70408c9538eb7dab90b53a08\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2e4e8285ce98bf95914496884c66bc1a\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2e4e8285ce98bf95914496884c66bc1a\transformed\navigation-fragment-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8fcff5946e9cb42bb9e1450a210c2616\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8fcff5946e9cb42bb9e1450a210c2616\transformed\navigation-ui-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9f99db3bdee5091140633361bed826be\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9f99db3bdee5091140633361bed826be\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7d65cf44c341bdeaf24467708c7f0f6\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7d65cf44c341bdeaf24467708c7f0f6\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a3916939ede9d9482ba416c66b21778\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.6.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3a3916939ede9d9482ba416c66b21778\transformed\lottie-6.6.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68cd95bb7c20f8a65475dd90790a85f2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68cd95bb7c20f8a65475dd90790a85f2\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\21d61e9b45cfab2ee4b69c56a0b52398\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\21d61e9b45cfab2ee4b69c56a0b52398\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\67870811a409034376e44ceca177f729\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\67870811a409034376e44ceca177f729\transformed\work-runtime-ktx-2.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\545b593b4e826b1db6b9c41cd1cc5e09\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\545b593b4e826b1db6b9c41cd1cc5e09\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e50512e0cf6fcbdd1c74ca21f173cef3\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e50512e0cf6fcbdd1c74ca21f173cef3\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\98abb0607de596cfeff3dfe20eb3948c\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\98abb0607de596cfeff3dfe20eb3948c\transformed\fragment-ktx-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\193256b637e2d97f6c391bcf568467d4\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\193256b637e2d97f6c391bcf568467d4\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8c95fb1bf71448479a8aeb756e068ef5\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8c95fb1bf71448479a8aeb756e068ef5\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\447f28505bcf9d53ca7c0e300fee33db\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\447f28505bcf9d53ca7c0e300fee33db\transformed\recyclerview-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5462eaf29936baed5e145b725030de94\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5462eaf29936baed5e145b725030de94\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\06f0b412b6fc7c9c8ed484fb19a528a2\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.8.7] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\06f0b412b6fc7c9c8ed484fb19a528a2\transformed\fragment-1.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3fe42a804cb5af25dea3fc8d65d3d68\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3fe42a804cb5af25dea3fc8d65d3d68\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\793839e92b57d80f2e40e130d0af7c6a\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\793839e92b57d80f2e40e130d0af7c6a\transformed\activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c81e199687af6aeb4b83eabecac6cc95\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c81e199687af6aeb4b83eabecac6cc95\transformed\activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc139b5a61a792785e024a87543773be\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dc139b5a61a792785e024a87543773be\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b52b2a4da0d92372a1f82cdd58358bd6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b52b2a4da0d92372a1f82cdd58358bd6\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fac29afda0ad1022119fc7d47a0eef0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2fac29afda0ad1022119fc7d47a0eef0\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\593add07a452c801b697195937a18578\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\593add07a452c801b697195937a18578\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4b15690e97d29d3c35909bd6ff61cfee\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4b15690e97d29d3c35909bd6ff61cfee\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fc00f2efc926a01364041610e9aebae\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4fc00f2efc926a01364041610e9aebae\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ffd8c4e10502b265d2b5073b22332982\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ffd8c4e10502b265d2b5073b22332982\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dac2e46243e93ae19dab1851f921943a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\dac2e46243e93ae19dab1851f921943a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0be0b8c4cdb946796e8dbbc99cfb9ae9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0be0b8c4cdb946796e8dbbc99cfb9ae9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\463f503ae32afb9a2de5da3dc8464499\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\463f503ae32afb9a2de5da3dc8464499\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1cd3501eecb59b5fa35934048a4d8046\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1cd3501eecb59b5fa35934048a4d8046\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6982b2f75d56037316f8252d2512229b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6982b2f75d56037316f8252d2512229b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\48260ca17b137f37e3e28513476807a1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\48260ca17b137f37e3e28513476807a1\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bb7078a00c65debf8f97887d40594730\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bb7078a00c65debf8f97887d40594730\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\813f8bb037f8627d7b4a7e1aa1a7d1b1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\813f8bb037f8627d7b4a7e1aa1a7d1b1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d43b932bc584af2a05c4860e51480c8\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d43b932bc584af2a05c4860e51480c8\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\09ce69ef417a64b32cd3325b7ee0536a\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\09ce69ef417a64b32cd3325b7ee0536a\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec874383389a1efb16f74c2e3a0738ff\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\ec874383389a1efb16f74c2e3a0738ff\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3c15d353adc78fed5a819759474b0fb8\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3c15d353adc78fed5a819759474b0fb8\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\eef25481716fc7890be7aca21c0506bf\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\eef25481716fc7890be7aca21c0506bf\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7c7ef9a9d85798cd48d7b90be7c3775\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c7c7ef9a9d85798cd48d7b90be7c3775\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfbbf048d6f0b90a71e31c29dcd80787\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\bfbbf048d6f0b90a71e31c29dcd80787\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d178b2a58bc54e5baaea6a1e0b1b266c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d178b2a58bc54e5baaea6a1e0b1b266c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8af36b0db4602ae08f98baa9f04a6259\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\8af36b0db4602ae08f98baa9f04a6259\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c651d4ce5e7ad42ae371fe4ed21e365b\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\c651d4ce5e7ad42ae371fe4ed21e365b\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3cbc40a7c21a93b0ec050bd489ff2274\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3cbc40a7c21a93b0ec050bd489ff2274\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\98c8fe482b576c279f955e254c804219\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\98c8fe482b576c279f955e254c804219\transformed\lifecycle-service-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ceaf8f83bb4865de959da2abbb6208a\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4ceaf8f83bb4865de959da2abbb6208a\transformed\lifecycle-livedata-core-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ceb8875b6bbadf52ff036bfa73df36f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2ceb8875b6bbadf52ff036bfa73df36f\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\05a0de893e977070e3ae6a5d2b874d38\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\05a0de893e977070e3ae6a5d2b874d38\transformed\lifecycle-livedata-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9fc6bc21b2f8c070d0b20c407b971ae7\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\9fc6bc21b2f8c070d0b20c407b971ae7\transformed\lifecycle-viewmodel-2.9.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2c98378d07946bd6d1cc1c50bef38ec6\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2c98378d07946bd6d1cc1c50bef38ec6\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\51c8db7fe8e70cfb879a5c4442ff2c55\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.shimmer:shimmer:0.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\51c8db7fe8e70cfb879a5c4442ff2c55\transformed\shimmer-0.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fed4f28d03252dc11237901a3ab86cce\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\fed4f28d03252dc11237901a3ab86cce\transformed\room-ktx-2.7.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1472e30982fc6c6f85e544376c621099\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1472e30982fc6c6f85e544376c621099\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f00a452c1fa629d99407b8a5c016f99f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f00a452c1fa629d99407b8a5c016f99f\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\42060d313688d1f4cb1c99bc53032ca9\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\42060d313688d1f4cb1c99bc53032ca9\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5531d388bd07a39db47990824789124\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f5531d388bd07a39db47990824789124\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e784328f42b70f4fae8c82c1687066bb\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\e784328f42b70f4fae8c82c1687066bb\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\573843d77d4a88fcda0ded0a8cbbf042\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\573843d77d4a88fcda0ded0a8cbbf042\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c34f2234addedd19ec24b7a3344aa0c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\5c34f2234addedd19ec24b7a3344aa0c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2d8d7dcd1d1acfbca97f977538c7a854\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\2d8d7dcd1d1acfbca97f977538c7a854\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd0401f989ab95fe9f964623ff94007\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3dd0401f989ab95fe9f964623ff94007\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\046a69a06664e4450d93dbcddc9fcf72\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\046a69a06664e4450d93dbcddc9fcf72\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\89482ec0869eb517a3ad4d95dd17c65f\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\89482ec0869eb517a3ad4d95dd17c65f\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\53a6d82573805f897e4f0e6b5e7b9d94\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\53a6d82573805f897e4f0e6b5e7b9d94\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aaa37a81498d6eabdb9cf9fffe43f4a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\aaa37a81498d6eabdb9cf9fffe43f4a4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\06fface0e3f5c6ec1135f468ac3ebb4e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\06fface0e3f5c6ec1135f468ac3ebb4e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d076bc2c78031033c89deaf3d4553991\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d076bc2c78031033c89deaf3d4553991\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\79ecfa30b82b5ca3cb4e21186ea2a78c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\79ecfa30b82b5ca3cb4e21186ea2a78c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a41c8f74917e3a48a13b97ebaea973a2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\a41c8f74917e3a48a13b97ebaea973a2\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f30d4a7a42cb77abd4ef34f13bf5aef2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\f30d4a7a42cb77abd4ef34f13bf5aef2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3d69e8b3f15130dc9e646535e2143e3\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\d3d69e8b3f15130dc9e646535e2143e3\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0f1d202f6b39ba837b6128b22dfad4ad\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\0f1d202f6b39ba837b6128b22dfad4ad\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6d4bb93bcd09957324150d908ac1bd7f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6d4bb93bcd09957324150d908ac1bd7f\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1123ea94c0e9d129b784050deb0865f7\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\1123ea94c0e9d129b784050deb0865f7\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Augment_Code\BearMod-Loader\app\src\main\AndroidManifest.xml
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\6102e147f2897719b4cacf45a763acd5\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\3454c984bd043192fe343ad80a7c610f\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\333a4b82f1858e8bb4041f17c0da4893\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\68985de5d4cda068757447934da9e6db\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.bearmod.loader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\50cbe4bff5d1390b137919b7462144e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\56fd4ca38ea117ea16bae0ecd3ba01c3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\b6dd7f450608f9ede61641f3b92664f7\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] D:\AndroidBuildEnv\.gradle\caches\8.11.1\transforms\4d94c611e3c6fbb79727fb59d4e77f22\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
