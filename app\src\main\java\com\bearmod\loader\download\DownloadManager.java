package com.bearmod.loader.download;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkInfo;
import androidx.work.WorkManager;

import java.util.List;

import com.bearmod.loader.auth.KeyAuthManager;
import com.bearmod.loader.ui.download.DownloadProgressDialog;
import com.bearmod.loader.utils.ApkInstaller;

import java.io.File;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.Locale;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Download manager
 * Handles downloading APK and OBB files
 */
public class DownloadManager {

    private static final String TAG = "DownloadManager";
    private static final String DOWNLOAD_WORK_NAME = "patch_download_work";

    private static DownloadManager instance;
    private final Executor executor = Executors.newSingleThreadExecutor();
    private final Handler handler = new Handler(Looper.getMainLooper());

    private Context context;
    private boolean isDownloading = false;
    private UUID currentWorkId;
    private DownloadProgressListener progressListener;
    private WorkManager workManager;
    private final KeyAuthManager keyAuthManager;
    private DownloadProgressDialog progressDialog;

    /**
     * Private constructor to enforce singleton pattern
     */
    private DownloadManager() {
        // Initialize KeyAuth manager
        keyAuthManager = KeyAuthManager.getInstance();
    }

    /**
     * Get DownloadManager instance
     * @return DownloadManager instance
     */
    public static synchronized DownloadManager getInstance() {
        if (instance == null) {
            instance = new DownloadManager();
        }
        return instance;
    }

    /**
     * Initialize download manager
     * @param context Application context
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        this.workManager = WorkManager.getInstance(context);

        // Observe any existing work
        observeOngoingDownloads();
    }

    /**
     * Observe ongoing downloads
     * This allows the app to reconnect to ongoing downloads after process death
     */
    private void observeOngoingDownloads() {
        LiveData<List<WorkInfo>> workInfoLiveData = workManager.getWorkInfosForUniqueWorkLiveData(DOWNLOAD_WORK_NAME);

        // This would typically be observed by a ViewModel or Activity
        // For demonstration, we'll just log the status
        workInfoLiveData.observeForever(workInfoList -> {
            if (workInfoList != null && !workInfoList.isEmpty()) {
                WorkInfo workInfo = workInfoList.get(0);

                if (workInfo.getState() == WorkInfo.State.RUNNING) {
                    // We have an ongoing download
                    isDownloading = true;
                    currentWorkId = workInfo.getId();
                    Log.d(TAG, "Reconnected to ongoing download: " + currentWorkId);
                } else if (workInfo.getState().isFinished()) {
                    // Download finished (completed, failed, or cancelled)
                    isDownloading = false;
                    currentWorkId = null;
                    Log.d(TAG, "Download finished with state: " + workInfo.getState());
                }
            }
        });
    }

    /**
     * Validate patch URL to ensure it's from a trusted source
     * @param url URL to validate
     * @return True if URL is valid, false otherwise
     */

    public boolean validatePatchUrl(String url) {
        if (url == null || url.isEmpty()) {
            return false; // Empty/null URLs should fail validation
        }

        // Normalize the URL for comparison
        String normalizedUrl = url.toLowerCase(Locale.ROOT).trim();

        // List of trusted domains (without protocols/subdomains)
        String[] trustedDomains = {
                "github.com",
                "api.github.com",
                "raw.githubusercontent.com",
                "bearmod.com",
                "cdn.bearmod.com",
                "mod-key.click", // Base domain
                "storage.googleapis.com",
                "firebasestorage.googleapis.com"
        };

        for (String domain : trustedDomains) {
            if (url.startsWith("https://" + domain) ||
                url.startsWith("https://www." + domain)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Download game files using WorkManager for reliable background processing
     * @param gameUrl Game download URL
     * @param fileName File name
     * @param totalSizeMB Total size in MB
     * @param listener Download listener
     */
    public void downloadGameFile(String gameUrl, String fileName, double totalSizeMB, DownloadListener listener) {
        // Check if already downloading
        if (isDownloading) {
            listener.onError("Download already in progress");
            return;
        }

        // Validate download URL
        if (validatePatchUrl(gameUrl)) {
            listener.onError("Invalid or untrusted download URL");
            return;
        }

        try {
            // Create input data for WorkManager
            Data inputData = new Data.Builder()
                    .putString("downloadUrl", gameUrl)
                    .putString("fileName", fileName)
                    .putDouble("totalSizeMB", totalSizeMB)
                    .build();

            // Create work request using unified GameDownloadWorker
            OneTimeWorkRequest downloadWorkRequest = new OneTimeWorkRequest.Builder(GameDownloadWorker.class)
                    .setInputData(inputData)
                    .build();

            // Store work ID for cancellation
            currentWorkId = downloadWorkRequest.getId();

            // Set progress listener for the worker
            GameDownloadWorker.setProgressListener((progress, downloadedMB, totalSizeMB_param, speedMBps, etaMinutes, etaSeconds) -> {
                if (progressListener != null) {
                    // Forward progress updates to our listener with enhanced information
                    handler.post(() -> progressListener.onProgressUpdate(
                            progress, downloadedMB, totalSizeMB_param, speedMBps, etaMinutes, etaSeconds));
                }
            });

            // Observe work status
            workManager.getWorkInfoByIdLiveData(currentWorkId).observeForever(workInfo -> {
                if (workInfo != null) {
                    if (workInfo.getState() == WorkInfo.State.SUCCEEDED) {
                        // Get output file path
                        String filePath = workInfo.getOutputData().getString("filePath");
                        if (filePath != null) {
                            File outputFile = new File(filePath);
                            handler.post(() -> {
                                isDownloading = false;
                                listener.onSuccess(outputFile);
                            });
                        }
                    } else if (workInfo.getState() == WorkInfo.State.FAILED) {
                        // Get error message
                        String error = workInfo.getOutputData().getString("error");
                        if (error == null) {
                            error = "Download failed";
                        }
                        final String finalError = error;
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError(finalError);
                        });
                    } else if (workInfo.getState() == WorkInfo.State.CANCELLED) {
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError("Download cancelled");
                        });
                    }
                }
            });

            // Enqueue work (replace any existing work)
            workManager.enqueueUniqueWork(
                    DOWNLOAD_WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    downloadWorkRequest);

            // Update state
            isDownloading = true;

            // Initial progress update
            if (progressListener != null) {
                handler.post(() -> progressListener.onProgressUpdate(0, 0, totalSizeMB, 0, 0, 0));
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to start download", e);
            listener.onError("Failed to start download: " + e.getMessage());
        }
    }

    /**
     * Cancel download
     */
    public void cancelDownload() {
        if (isDownloading && currentWorkId != null) {
            // Cancel the work
            workManager.cancelWorkById(currentWorkId);

            // Update state
            isDownloading = false;

            Log.d(TAG, "Download cancelled: " + currentWorkId);
        }
    }

    /**
     * Check if download is in progress
     * @return true if downloading, false otherwise
     */
    public boolean isDownloading() {
        return isDownloading;
    }

    /**
     * Set download progress listener
     * @param listener Download progress listener
     */
    public void setProgressListener(DownloadProgressListener listener) {
        this.progressListener = listener;
    }

    /**
     * Download game files (APK/OBB) based on download type
     * @param gameName Game name
     * @param downloadType Type of download (APK only, APK+OBB, OBB only)
     * @param listener Download listener
     */
    public void downloadGameFiles(String gameName, DownloadType downloadType, DownloadListener listener) {
        // Check if already downloading
        if (isDownloading) {
            listener.onError("Download already in progress");
            return;
        }

        try {
            // Determine what to download based on type
            String fileName;
            String downloadUrl;
            double totalSizeMB;

            String baseName = gameName.replaceAll("\\s+", "-").toLowerCase(Locale.ROOT);

            switch (downloadType) {
                case APK_ONLY:
                    fileName = baseName + ".apk";
                    downloadUrl = "https://github.com/mozilla-mobile/fenix/releases/download/v121.1.0/fenix-121.1.0-arm64-v8a.apk";
                    totalSizeMB = 120.5;
                    break;
                case APK_AND_OBB:
                    fileName = baseName + "_full.zip";
                    downloadUrl = "https://releases.ubuntu.com/20.04/ubuntu-20.04.6-desktop-amd64.iso";
                    totalSizeMB = 1100.8;
                    break;
                case OBB_ONLY:
                    fileName = baseName + ".obb";
                    downloadUrl = "https://archive.org/download/SampleVideo1280x7205mb/SampleVideo_1280x720_5mb.mp4";
                    totalSizeMB = 980.3;
                    break;
                default:
                    listener.onError("Invalid download type");
                    return;
            }

            // Validate download URL
            if (validatePatchUrl(downloadUrl)) {
                listener.onError("Invalid or untrusted download URL");
                return;
            }

            // Create input data for WorkManager
            Data inputData = new Data.Builder()
                    .putString("downloadUrl", downloadUrl)
                    .putString("fileName", fileName)
                    .putDouble("totalSizeMB", totalSizeMB)
                    .putString("downloadType", downloadType.name())
                    .build();

            // Create work request
            OneTimeWorkRequest downloadWorkRequest = new OneTimeWorkRequest.Builder(GameDownloadWorker.class)
                    .setInputData(inputData)
                    .build();

            // Store work ID for cancellation
            currentWorkId = downloadWorkRequest.getId();

            // Set progress listener for the worker
            GameDownloadWorker.setProgressListener((progress, downloadedMB, totalSizeMB_param, speedMBps, etaMinutes, etaSeconds) -> {
                if (progressListener != null) {
                    // Forward progress updates to our listener with enhanced information
                    handler.post(() -> progressListener.onProgressUpdate(
                            progress, downloadedMB, totalSizeMB_param, speedMBps, etaMinutes, etaSeconds));
                }
            });

            // Observe work status
            workManager.getWorkInfoByIdLiveData(currentWorkId).observeForever(workInfo -> {
                if (workInfo != null) {
                    if (workInfo.getState() == WorkInfo.State.SUCCEEDED) {
                        // Get output file path
                        String filePath = workInfo.getOutputData().getString("filePath");
                        if (filePath != null) {
                            File outputFile = new File(filePath);
                            handler.post(() -> {
                                isDownloading = false;
                                listener.onSuccess(outputFile);
                            });
                        }
                    } else if (workInfo.getState() == WorkInfo.State.FAILED) {
                        // Get error message
                        String error = workInfo.getOutputData().getString("error");
                        if (error == null) {
                            error = "Download failed";
                        }
                        final String finalError = error;
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError(finalError);
                        });
                    } else if (workInfo.getState() == WorkInfo.State.CANCELLED) {
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError("Download cancelled");
                        });
                    }
                }
            });

            // Enqueue work (replace any existing work)
            workManager.enqueueUniqueWork(
                    DOWNLOAD_WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    downloadWorkRequest);

            // Update state
            isDownloading = true;

            // Initial progress update
            if (progressListener != null) {
                handler.post(() -> progressListener.onProgressUpdate(0, 0, totalSizeMB, 0, 0, 0));
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to start game download", e);
            listener.onError("Failed to start download: " + e.getMessage());
        }
    }

    /**
     * Enhanced APK/OBB download with progress dialog support
     * @param context Application context
     * @param gameName Game name
     * @param downloadType Type of download
     * @param listener Download completion listener
     */
    public void downloadGameFilesEnhanced(Context context, String gameName, DownloadType downloadType,
                                         EnhancedDownloadListener listener) {

        if (isDownloading) {
            Log.w(TAG, "Download already in progress");
            listener.onError("Download already in progress");
            return;
        }

        Log.d(TAG, "Starting enhanced download for: " + gameName + " with type: " + downloadType);

        try {

        // Create download info
        DownloadInfo downloadInfo = createDownloadInfo(gameName, downloadType);

        // Show progress dialog
        showProgressDialog(context, downloadInfo);

        // Create work request
        Data inputData = new Data.Builder()
                .putString("gameName", gameName)
                .putString("downloadType", downloadType.name())
                .putString("downloadUrl", downloadInfo.downloadUrl)
                .putString("fileName", downloadInfo.fileName)
                .putDouble("totalSizeMB", downloadInfo.totalSizeMB)
                .build();

        OneTimeWorkRequest downloadWorkRequest = new OneTimeWorkRequest.Builder(GameDownloadWorker.class)
                .setInputData(inputData)
                .build();

        // Store work ID for cancellation
        currentWorkId = downloadWorkRequest.getId();

        // Set up progress tracking
        GameDownloadWorker.setProgressListener((progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds) -> {
            handler.post(() -> {
                if (progressDialog != null) {
                    progressDialog.updateProgress(progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds);
                }
                if (progressListener != null) {
                    progressListener.onProgressUpdate(progress, downloadedMB, totalSizeMB, speedMBps, etaMinutes, etaSeconds);
                }
            });
        });

        // Observe work status
        workManager.getWorkInfoByIdLiveData(downloadWorkRequest.getId()).observeForever(workInfo -> {
            if (workInfo != null) {
                if (workInfo.getState() == WorkInfo.State.SUCCEEDED) {
                    handler.post(() -> {
                        isDownloading = false;
                        String filePath = workInfo.getOutputData().getString("filePath");
                        handleDownloadSuccess(context, downloadType, filePath, listener);
                    });
                } else if (workInfo.getState() == WorkInfo.State.FAILED) {
                    handler.post(() -> {
                        isDownloading = false;
                        String error = workInfo.getOutputData().getString("error");
                        handleDownloadError(error != null ? error : "Download failed", listener);
                    });
                } else if (workInfo.getState() == WorkInfo.State.CANCELLED) {
                    handler.post(() -> {
                        isDownloading = false;
                        handleDownloadError("Download cancelled", listener);
                    });
                }
            }
        });

        // Set cancel listener
        if (progressDialog != null) {
            progressDialog.setDownloadCancelListener(() -> {
                Log.d(TAG, "User requested download cancellation");
                workManager.cancelUniqueWork(DOWNLOAD_WORK_NAME);
            });
        }

        // Enqueue work
        workManager.enqueueUniqueWork(
                DOWNLOAD_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                downloadWorkRequest);

        isDownloading = true;

        } catch (Exception e) {
            Log.e(TAG, "Failed to start enhanced download", e);
            if (progressDialog != null) {
                progressDialog.dismiss();
                progressDialog = null;
            }
            listener.onError("Failed to start download: " + e.getMessage());
        }
    }

    /**
     * Download patch using KeyAuth API
     * @param fileId KeyAuth file ID
     * @param listener Download listener
     */
    public void downloadKeyAuthFile(String fileId, DownloadListener listener) {
        // Check if already downloading
        if (isDownloading) {
            listener.onError("Download already in progress");
            return;
        }

        // Update state
        isDownloading = true;

        // Download using KeyAuthManager webhook (example: GET, no POST data)
        keyAuthManager.callWebhook(context, "<WEBHOOK_ID>", "&fileid=" + fileId, null, null, new KeyAuthManager.WebhookCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    // Here, response should be the file content (if API returns raw bytes, adjust accordingly)
                    // For demonstration, save as text file
                    String fileName = "keyauth_" + fileId + ".bin";
                    File downloadDir = new File(context.getExternalFilesDir(null), "downloads");
                    if (!downloadDir.exists() && !downloadDir.mkdirs()) {
                        throw new IOException("Failed to create download directory");
                    }
                    File outputFile = new File(downloadDir, fileName);
                    try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                        fos.write(response.getBytes(java.nio.charset.StandardCharsets.UTF_8));
                    }
                    isDownloading = false;
                    handler.post(() -> listener.onSuccess(outputFile));
                } catch (Exception e) {
                    Log.e(TAG, "Failed to save downloaded file", e);
                    isDownloading = false;
                    handler.post(() -> listener.onError("Failed to save file: " + e.getMessage()));
                }
            }
            @Override
            public void onError(String error) {
                isDownloading = false;
                handler.post(() -> listener.onError(error));
            }
        });
    }

    // Removed simulateDownloadProgress method as we now use real downloads with WorkManager

    /**
     * Create download info based on game name and download type
     * @param gameName Game name
     * @param downloadType Download type
     * @return Download info
     */
    private DownloadInfo createDownloadInfo(String gameName, DownloadType downloadType) {
        DownloadInfo info = new DownloadInfo();

        String baseName = gameName.replaceAll("\\s+", "-").toLowerCase();

        switch (downloadType) {
            case APK_ONLY:
                info.fileName = baseName + ".apk";
                // Use a working demo APK URL for testing (small file)
                info.downloadUrl = "https://github.com/mozilla-mobile/fenix/releases/download/v121.1.0/fenix-121.1.0-arm64-v8a.apk";
                info.totalSizeMB = 120.5;
                break;
            case APK_AND_OBB:
                info.fileName = baseName + "_full.zip";
                // Use a working demo large file URL for testing
                info.downloadUrl = "https://releases.ubuntu.com/20.04/ubuntu-20.04.6-desktop-amd64.iso";
                info.totalSizeMB = 1100.8;
                break;
            case OBB_ONLY:
                info.fileName = baseName + ".obb";
                // Use a working demo medium file URL for testing
                info.downloadUrl = "https://archive.org/download/SampleVideo1280x7205mb/SampleVideo_1280x720_5mb.mp4";
                info.totalSizeMB = 980.3;
                break;
        }

        return info;
    }

    /**
     * Show progress dialog
     * @param context Application context
     * @param downloadInfo Download information
     */
    private void showProgressDialog(Context context, DownloadInfo downloadInfo) {
        String downloadTypeText = getDownloadTypeText(downloadInfo.fileName);

        // Use PUBG icon for the dialog
        progressDialog = new DownloadProgressDialog(context, downloadTypeText, downloadInfo.fileName,
            com.bearmod.loader.R.drawable.ic_pubg_global_vector);
        progressDialog.show();
    }

    /**
     * Get download type text for display
     * @param fileName File name
     * @return Display text
     */
    private String getDownloadTypeText(String fileName) {
        if (fileName.endsWith(".apk")) {
            return "APK Only";
        } else if (fileName.contains("_full")) {
            return "APK + OBB";
        } else if (fileName.endsWith(".obb")) {
            return "OBB Only";
        }
        return "Game Files";
    }

    /**
     * Handle download success
     * @param context Application context
     * @param downloadType Download type
     * @param filePath Downloaded file path
     * @param listener Completion listener
     */
    private void handleDownloadSuccess(Context context, DownloadType downloadType, String filePath,
                                     EnhancedDownloadListener listener) {
        Log.d(TAG, "Download completed: " + filePath);

        if (progressDialog != null) {
            progressDialog.showCompletion(true, "Download completed successfully");
        }

        // Auto-install APK if applicable
        if (downloadType == DownloadType.APK_ONLY || downloadType == DownloadType.APK_AND_OBB) {
            File downloadedFile = new File(filePath);
            if (downloadedFile.getName().endsWith(".apk")) {
                autoInstallApk(context, downloadedFile);
            }
        }

        listener.onSuccess(filePath);
    }

    /**
     * Handle download error
     * @param error Error message
     * @param listener Completion listener
     */
    private void handleDownloadError(String error, EnhancedDownloadListener listener) {
        Log.e(TAG, "Download failed: " + error);

        if (progressDialog != null) {
            progressDialog.showCompletion(false, error);
        }

        listener.onError(error);
    }

    /**
     * Auto-install APK file
     * @param context Application context
     * @param apkFile APK file to install
     */
    private void autoInstallApk(Context context, File apkFile) {
        Log.d(TAG, "Auto-installing APK: " + apkFile.getAbsolutePath());

        ApkInstaller.installApk(context, apkFile, new ApkInstaller.InstallationCallback() {
            @Override
            public void onSuccess(String message) {
                Log.d(TAG, "APK installation started: " + message);
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "APK installation failed: " + error);
            }
        });
    }

    /**
     * Download listener interface
     */
    public interface DownloadListener {
        void onSuccess(File downloadedFile);
        void onError(String error);
    }

    /**
     * Enhanced download listener interface
     */
    public interface EnhancedDownloadListener {
        void onSuccess(String filePath);
        void onError(String error);
    }

    /**
     * Download progress listener interface with enhanced information
     */
    public interface DownloadProgressListener {
        /**
         * Called when download progress is updated
         * @param progress Download progress (0-100)
         * @param downloadedMB Downloaded size in MB
         * @param totalSizeMB Total size in MB
         * @param speedMBps Download speed in MB/s
         * @param etaMinutes Estimated time remaining (minutes)
         * @param etaSeconds Estimated time remaining (seconds)
         */
        void onProgressUpdate(int progress, double downloadedMB, double totalSizeMB,
                             double speedMBps, int etaMinutes, int etaSeconds);
    }

    /**
     * Download type enum
     */
    public enum DownloadType {
        APK_ONLY,
        APK_AND_OBB,
        OBB_ONLY
    }

    /**
     * Download info class
     */
    private static class DownloadInfo {
        String fileName;
        String downloadUrl;
        double totalSizeMB;
    }
}
