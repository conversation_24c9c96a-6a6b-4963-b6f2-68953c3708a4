package com.bearmod.loader.download;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkInfo;
import androidx.work.WorkManager;

import java.util.List;

import com.bearmod.loader.auth.KeyAuthManager;
import com.bearmod.loader.auth.BearTokenManager;
import com.bearmod.loader.ui.download.DownloadProgressDialog;
import com.bearmod.loader.utils.ApkInstaller;
import com.bearmod.loader.utils.StorageManager;
import com.bearmod.loader.security.AppSecurityManager;

import java.io.File;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.Locale;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Download manager
 * Handles downloading APK and OBB files
 */
public class DownloadManager {

    private static final String TAG = "DownloadManager";
    private static final String DOWNLOAD_WORK_NAME = "patch_download_work";

    private static DownloadManager instance;
    private final Executor executor = Executors.newSingleThreadExecutor();
    private final Handler handler = new Handler(Looper.getMainLooper());

    private Context context;
    private boolean isDownloading = false;
    private UUID currentWorkId;
    private DownloadProgressListener progressListener;
    private WorkManager workManager;
    private final KeyAuthManager keyAuthManager;
    private DownloadProgressDialog progressDialog;

    /**
     * Private constructor to enforce singleton pattern
     */
    private DownloadManager() {
        // Initialize KeyAuth manager
        keyAuthManager = KeyAuthManager.getInstance();
    }

    /**
     * Get DownloadManager instance
     * @return DownloadManager instance
     */
    public static synchronized DownloadManager getInstance() {
        if (instance == null) {
            instance = new DownloadManager();
        }
        return instance;
    }

    /**
     * Initialize download manager
     * @param context Application context
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        this.workManager = WorkManager.getInstance(context);

        // Observe any existing work
        observeOngoingDownloads();
    }

    /**
     * Observe ongoing downloads
     * This allows the app to reconnect to ongoing downloads after process death
     */
    private void observeOngoingDownloads() {
        LiveData<List<WorkInfo>> workInfoLiveData = workManager.getWorkInfosForUniqueWorkLiveData(DOWNLOAD_WORK_NAME);

        // This would typically be observed by a ViewModel or Activity
        // For demonstration, we'll just log the status
        workInfoLiveData.observeForever(workInfoList -> {
            if (workInfoList != null && !workInfoList.isEmpty()) {
                WorkInfo workInfo = workInfoList.get(0);

                if (workInfo.getState() == WorkInfo.State.RUNNING) {
                    // We have an ongoing download
                    isDownloading = true;
                    currentWorkId = workInfo.getId();
                    Log.d(TAG, "Reconnected to ongoing download: " + currentWorkId);
                } else if (workInfo.getState().isFinished()) {
                    // Download finished (completed, failed, or cancelled)
                    isDownloading = false;
                    currentWorkId = null;
                    Log.d(TAG, "Download finished with state: " + workInfo.getState());
                }
            }
        });
    }

    /**
     * Validate patch URL to ensure it's from a trusted source
     * @param url URL to validate
     * @return True if URL is valid, false otherwise
     */

    public boolean validatePatchUrl(String url) {
        if (url == null || url.isEmpty()) {
            return false; // Empty/null URLs should fail validation
        }

        // Normalize the URL for comparison
        String normalizedUrl = url.toLowerCase(Locale.ROOT).trim();

        // List of trusted domains (without protocols/subdomains)
        String[] trustedDomains = {
                "github.com",
                "api.github.com",
                "raw.githubusercontent.com",
                "bearmod.com",
                "cdn.bearmod.com",
                "mod-key.click", // Base domain
                "storage.googleapis.com",
                "firebasestorage.googleapis.com"
        };

        for (String domain : trustedDomains) {
            if (url.startsWith("https://" + domain) ||
                url.startsWith("https://www." + domain)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Download game files using WorkManager for reliable background processing
     * @param gameUrl Game download URL
     * @param fileName File name
     * @param totalSizeMB Total size in MB
     * @param listener Download listener
     */
    public void downloadGameFile(String gameUrl, String fileName, double totalSizeMB, DownloadListener listener) {
        // Check if already downloading
        if (isDownloading) {
            listener.onError("Download already in progress");
            return;
        }

        // Validate download URL
        if (validatePatchUrl(gameUrl)) {
            listener.onError("Invalid or untrusted download URL");
            return;
        }

        try {
            // Create input data for WorkManager
            Data inputData = new Data.Builder()
                    .putString("downloadUrl", gameUrl)
                    .putString("fileName", fileName)
                    .putDouble("totalSizeMB", totalSizeMB)
                    .build();

            // Create work request using unified GameDownloadWorker
            OneTimeWorkRequest downloadWorkRequest = new OneTimeWorkRequest.Builder(GameDownloadWorker.class)
                    .setInputData(inputData)
                    .build();

            // Store work ID for cancellation
            currentWorkId = downloadWorkRequest.getId();

            // Set progress listener for the worker
            GameDownloadWorker.setProgressListener((progress, downloadedMB, totalSizeMB_param, speedMBps, etaMinutes, etaSeconds) -> {
                if (progressListener != null) {
                    // Forward progress updates to our listener with enhanced information
                    handler.post(() -> progressListener.onProgressUpdate(
                            progress, downloadedMB, totalSizeMB_param, speedMBps, etaMinutes, etaSeconds));
                }
            });

            // Observe work status
            workManager.getWorkInfoByIdLiveData(currentWorkId).observeForever(workInfo -> {
                if (workInfo != null) {
                    if (workInfo.getState() == WorkInfo.State.SUCCEEDED) {
                        // Get output file path
                        String filePath = workInfo.getOutputData().getString("filePath");
                        if (filePath != null) {
                            File outputFile = new File(filePath);
                            handler.post(() -> {
                                isDownloading = false;
                                listener.onSuccess(outputFile);
                            });
                        }
                    } else if (workInfo.getState() == WorkInfo.State.FAILED) {
                        // Get error message
                        String error = workInfo.getOutputData().getString("error");
                        if (error == null) {
                            error = "Download failed";
                        }
                        final String finalError = error;
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError(finalError);
                        });
                    } else if (workInfo.getState() == WorkInfo.State.CANCELLED) {
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError("Download cancelled");
                        });
                    }
                }
            });

            // Enqueue work (replace any existing work)
            workManager.enqueueUniqueWork(
                    DOWNLOAD_WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    downloadWorkRequest);

            // Update state
            isDownloading = true;

            // Initial progress update
            if (progressListener != null) {
                handler.post(() -> progressListener.onProgressUpdate(0, 0, totalSizeMB, 0, 0, 0));
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to start download", e);
            listener.onError("Failed to start download: " + e.getMessage());
        }
    }

    /**
     * Cancel download
     */
    public void cancelDownload() {
        if (isDownloading && currentWorkId != null) {
            // Cancel the work
            workManager.cancelWorkById(currentWorkId);

            // Update state
            isDownloading = false;

            Log.d(TAG, "Download cancelled: " + currentWorkId);
        }
    }

    /**
     * Check if download is in progress
     * @return true if downloading, false otherwise
     */
    public boolean isDownloading() {
        return isDownloading;
    }

    /**
     * Set download progress listener
     * @param listener Download progress listener
     */
    public void setProgressListener(DownloadProgressListener listener) {
        this.progressListener = listener;
    }

    /**
     * Download game files (APK/OBB) based on download type using KeyAuth
     * @param gameName Game name
     * @param downloadType Type of download (APK only, APK+OBB, OBB only)
     * @param listener Download listener
     */
    public void downloadGameFiles(String gameName, DownloadType downloadType, DownloadListener listener) {
        // Check if already downloading
        if (isDownloading) {
            listener.onError("Download already in progress");
            return;
        }

        try {
            // Perform security check first
            AppSecurityManager.SecurityCheckResult securityResult = AppSecurityManager.performSecurityCheck(context);
            if (!securityResult.isSecure) {
                Log.e(TAG, "Security check failed: " + securityResult.message);
                listener.onError("Security verification failed: " + securityResult.message);
                return;
            }

            // Get PUBG Mobile configuration for the specified game
            PubgMobileConfig config = getPubgMobileConfig(gameName);
            if (config == null) {
                listener.onError("Unsupported game: " + gameName);
                return;
            }

            // Verify PUBG Mobile signature if already installed
            if (!AppSecurityManager.verifyPubgSignature(context, config.packageName)) {
                Log.w(TAG, "PUBG Mobile signature verification failed for: " + config.packageName);
                // Continue with download but log the warning
            }

            // Determine what to download based on type
            String fileId;
            String fileName;
            double totalSizeMB;

            switch (downloadType) {
                case APK_ONLY:
                    fileId = config.apkFileId;
                    fileName = config.packageName + ".apk";
                    totalSizeMB = config.apkSizeMB;
                    break;
                case APK_AND_OBB:
                    // For APK+OBB, we'll download APK first, then OBB
                    fileId = config.apkFileId;
                    fileName = config.packageName + ".apk";
                    totalSizeMB = config.apkSizeMB + config.obbSizeMB;
                    break;
                case OBB_ONLY:
                    fileId = config.obbFileId;
                    fileName = config.packageName + ".obb";
                    totalSizeMB = config.obbSizeMB;
                    break;
                default:
                    listener.onError("Invalid download type");
                    return;
            }

            // Check storage space before starting download
            if (!StorageManager.hasEnoughSpace(totalSizeMB)) {
                Log.w(TAG, "Insufficient storage space. Required: " + totalSizeMB + "MB");

                // Try to cleanup space
                if (StorageManager.cleanupForSpace(context, totalSizeMB)) {
                    Log.i(TAG, "Successfully freed up space for download");
                } else {
                    StorageManager.StorageInfo storageInfo = StorageManager.getStorageInfo();
                    listener.onError("Insufficient storage space. Required: " + (int)totalSizeMB +
                                   "MB, Available: " + storageInfo.availableSpaceMB + "MB. " +
                                   "Please free up space and try again.");
                    return;
                }
            }

            // Create download directories
            if (!StorageManager.createDownloadDirectories(context)) {
                listener.onError("Failed to create download directories");
                return;
            }

            // Download using KeyAuth file system
            downloadKeyAuthGameFile(fileId, fileName, totalSizeMB, downloadType, config, listener);

        } catch (Exception e) {
            Log.e(TAG, "Failed to start game download", e);
            listener.onError("Failed to start download: " + e.getMessage());
        }
    }

    /**
     * Enhanced APK/OBB download with progress dialog support using KeyAuth
     * @param context Application context
     * @param gameName Game name
     * @param downloadType Type of download
     * @param listener Download completion listener
     */
    public void downloadGameFilesEnhanced(Context context, String gameName, DownloadType downloadType,
                                         EnhancedDownloadListener listener) {

        if (isDownloading) {
            Log.w(TAG, "Download already in progress");
            listener.onError("Download already in progress");
            return;
        }

        Log.d(TAG, "Starting enhanced KeyAuth download for: " + gameName + " with type: " + downloadType);

        try {
            // Get PUBG Mobile configuration for the specified game
            PubgMobileConfig config = getPubgMobileConfig(gameName);
            if (config == null) {
                listener.onError("Unsupported game: " + gameName);
                return;
            }

            // Determine what to download based on type
            String fileId;
            String fileName;
            double totalSizeMB;

            switch (downloadType) {
                case APK_ONLY:
                    fileId = config.apkFileId;
                    fileName = config.packageName + ".apk";
                    totalSizeMB = config.apkSizeMB;
                    break;
                case APK_AND_OBB:
                    // For APK+OBB, we'll download APK first, then OBB
                    fileId = config.apkFileId;
                    fileName = config.packageName + ".apk";
                    totalSizeMB = config.apkSizeMB + config.obbSizeMB;
                    break;
                case OBB_ONLY:
                    fileId = config.obbFileId;
                    fileName = config.packageName + ".obb";
                    totalSizeMB = config.obbSizeMB;
                    break;
                default:
                    listener.onError("Invalid download type");
                    return;
            }

            // Check storage space before starting download
            if (!StorageManager.hasEnoughSpace(totalSizeMB)) {
                Log.w(TAG, "Insufficient storage space for enhanced download. Required: " + totalSizeMB + "MB");

                // Try to cleanup space
                if (StorageManager.cleanupForSpace(context, totalSizeMB)) {
                    Log.i(TAG, "Successfully freed up space for enhanced download");
                } else {
                    StorageManager.StorageInfo storageInfo = StorageManager.getStorageInfo();
                    String errorMsg = "Insufficient storage space. Required: " + (int)totalSizeMB +
                                    "MB, Available: " + storageInfo.availableSpaceMB + "MB. " +
                                    "Please free up space and try again.";
                    listener.onError(errorMsg);
                    return;
                }
            }

            // Create download directories
            if (!StorageManager.createDownloadDirectories(context)) {
                listener.onError("Failed to create download directories");
                return;
            }

            // Show progress dialog
            showEnhancedProgressDialog(context, gameName, downloadType, fileName);

            // Download using KeyAuth file system with enhanced progress tracking
            downloadKeyAuthGameFileEnhanced(fileId, fileName, totalSizeMB, downloadType, config, listener);

        } catch (Exception e) {
            Log.e(TAG, "Failed to start enhanced download", e);
            if (progressDialog != null) {
                progressDialog.dismiss();
                progressDialog = null;
            }
            listener.onError("Failed to start download: " + e.getMessage());
        }
    }

    /**
     * Download PUBG Mobile game file using KeyAuth
     * @param fileId KeyAuth file ID
     * @param fileName Target file name
     * @param totalSizeMB Expected file size in MB
     * @param downloadType Type of download
     * @param config PUBG Mobile configuration
     * @param listener Download listener
     */
    private void downloadKeyAuthGameFile(String fileId, String fileName, double totalSizeMB,
                                       DownloadType downloadType, PubgMobileConfig config, DownloadListener listener) {
        Log.d(TAG, "Starting KeyAuth download for file ID: " + fileId);

        // Update state
        isDownloading = true;

        // Initial progress update
        if (progressListener != null) {
            handler.post(() -> progressListener.onProgressUpdate(0, 0, totalSizeMB, 0, 0, 0));
        }

        // Use DirectKeyAuthManager for file download
        com.bearmod.loader.auth.DirectKeyAuthManager directKeyAuth =
            com.bearmod.loader.auth.DirectKeyAuthManager.getInstance();

        directKeyAuth.downloadFile(fileId, new com.bearmod.loader.auth.DirectKeyAuthManager.DownloadCallback() {
            @Override
            public void onSuccess(byte[] fileBytes) {
                executor.execute(() -> {
                    try {
                        // Determine download directory based on file type
                        File downloadDir = getDownloadDirectory(downloadType, config);
                        if (!downloadDir.exists() && !downloadDir.mkdirs()) {
                            throw new IOException("Failed to create download directory: " + downloadDir.getAbsolutePath());
                        }

                        // Create output file
                        File outputFile = new File(downloadDir, fileName);

                        // Write file with progress updates
                        writeFileWithProgress(fileBytes, outputFile, totalSizeMB);

                        // Verify file integrity
                        if (!verifyFileIntegrity(outputFile, fileBytes.length)) {
                            throw new IOException("File integrity verification failed");
                        }

                        // Verify APK signature if it's an APK file
                        if (fileName.endsWith(".apk")) {
                            if (!AppSecurityManager.verifyDownloadIntegrity(context, outputFile.getAbsolutePath(), config.packageName)) {
                                Log.w(TAG, "APK signature verification failed, but continuing with installation");
                                // Continue with installation but log warning
                            }
                        }

                        // Handle APK+OBB download sequence
                        if (downloadType == DownloadType.APK_AND_OBB && fileName.endsWith(".apk")) {
                            // After APK download, start OBB download
                            downloadObbFile(config, listener, outputFile);
                        } else {
                            // Single file download complete
                            handler.post(() -> {
                                isDownloading = false;

                                // Generate BearToken for target mod authentication
                                generateBearTokenForTargetMod(config.packageName);

                                // Auto-install APK if applicable
                                if (fileName.endsWith(".apk")) {
                                    autoInstallApk(context, outputFile);
                                }

                                listener.onSuccess(outputFile);
                            });
                        }

                    } catch (Exception e) {
                        Log.e(TAG, "Failed to save downloaded file", e);
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError("Failed to save file: " + e.getMessage());
                        });
                    }
                });
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "KeyAuth download failed: " + error);
                handler.post(() -> {
                    isDownloading = false;
                    listener.onError("Download failed: " + error);
                });
            }
        });
    }

    /**
     * Download OBB file after APK download
     */
    private void downloadObbFile(PubgMobileConfig config, DownloadListener listener, File apkFile) {
        Log.d(TAG, "Starting OBB download for: " + config.packageName);

        com.bearmod.loader.auth.DirectKeyAuthManager directKeyAuth =
            com.bearmod.loader.auth.DirectKeyAuthManager.getInstance();

        directKeyAuth.downloadFile(config.obbFileId, new com.bearmod.loader.auth.DirectKeyAuthManager.DownloadCallback() {
            @Override
            public void onSuccess(byte[] fileBytes) {
                executor.execute(() -> {
                    try {
                        // OBB files go to Android/obb directory
                        File obbDir = new File(android.os.Environment.getExternalStorageDirectory(),
                                             "Android/obb/" + config.packageName);
                        if (!obbDir.exists() && !obbDir.mkdirs()) {
                            throw new IOException("Failed to create OBB directory: " + obbDir.getAbsolutePath());
                        }

                        String obbFileName = "main." + config.versionCode + "." + config.packageName + ".obb";
                        File obbFile = new File(obbDir, obbFileName);

                        // Write OBB file with progress updates
                        writeFileWithProgress(fileBytes, obbFile, config.obbSizeMB);

                        // Verify OBB file integrity
                        if (!verifyFileIntegrity(obbFile, fileBytes.length)) {
                            throw new IOException("OBB file integrity verification failed");
                        }

                        handler.post(() -> {
                            isDownloading = false;

                            // Auto-install APK after both files are ready
                            autoInstallApk(context, apkFile);

                            listener.onSuccess(apkFile); // Return APK file as primary
                        });

                    } catch (Exception e) {
                        Log.e(TAG, "Failed to save OBB file", e);
                        handler.post(() -> {
                            isDownloading = false;
                            listener.onError("Failed to save OBB file: " + e.getMessage());
                        });
                    }
                });
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "OBB download failed: " + error);
                handler.post(() -> {
                    isDownloading = false;
                    listener.onError("OBB download failed: " + error);
                });
            }
        });
    }

    /**
     * Enhanced KeyAuth download with progress dialog integration
     */
    private void downloadKeyAuthGameFileEnhanced(String fileId, String fileName, double totalSizeMB,
                                                DownloadType downloadType, PubgMobileConfig config,
                                                EnhancedDownloadListener listener) {
        Log.d(TAG, "Starting enhanced KeyAuth download for file ID: " + fileId);

        // Update state
        isDownloading = true;

        // Initial progress update
        if (progressDialog != null) {
            progressDialog.updateProgress(0, 0, totalSizeMB, 0, 0, 0);
        }

        // Use DirectKeyAuthManager for file download
        com.bearmod.loader.auth.DirectKeyAuthManager directKeyAuth =
            com.bearmod.loader.auth.DirectKeyAuthManager.getInstance();

        directKeyAuth.downloadFile(fileId, new com.bearmod.loader.auth.DirectKeyAuthManager.DownloadCallback() {
            @Override
            public void onSuccess(byte[] fileBytes) {
                executor.execute(() -> {
                    try {
                        // Determine download directory based on file type
                        File downloadDir = getDownloadDirectory(downloadType, config);
                        if (!downloadDir.exists() && !downloadDir.mkdirs()) {
                            throw new IOException("Failed to create download directory: " + downloadDir.getAbsolutePath());
                        }

                        // Create output file
                        File outputFile = new File(downloadDir, fileName);

                        // Write file with enhanced progress updates
                        writeFileWithEnhancedProgress(fileBytes, outputFile, totalSizeMB);

                        // Verify file integrity
                        if (!verifyFileIntegrity(outputFile, fileBytes.length)) {
                            throw new IOException("File integrity verification failed");
                        }

                        // Handle APK+OBB download sequence
                        if (downloadType == DownloadType.APK_AND_OBB && fileName.endsWith(".apk")) {
                            // After APK download, start OBB download
                            downloadObbFileEnhanced(config, listener, outputFile);
                        } else {
                            // Single file download complete
                            handler.post(() -> {
                                isDownloading = false;

                                // Show completion in dialog
                                if (progressDialog != null) {
                                    progressDialog.showCompletion(true, "Download completed successfully");
                                }

                                // Auto-install APK if applicable
                                if (fileName.endsWith(".apk")) {
                                    autoInstallApk(context, outputFile);
                                }

                                listener.onSuccess(outputFile.getAbsolutePath());
                            });
                        }

                    } catch (Exception e) {
                        Log.e(TAG, "Failed to save downloaded file", e);
                        handler.post(() -> {
                            isDownloading = false;

                            // Show error in dialog
                            if (progressDialog != null) {
                                progressDialog.showCompletion(false, "Download failed: " + e.getMessage());
                            }

                            listener.onError("Failed to save file: " + e.getMessage());
                        });
                    }
                });
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Enhanced KeyAuth download failed: " + error);
                handler.post(() -> {
                    isDownloading = false;

                    // Show error in dialog
                    if (progressDialog != null) {
                        progressDialog.showCompletion(false, "Download failed: " + error);
                    }

                    listener.onError("Download failed: " + error);
                });
            }
        });
    }

    /**
     * Download OBB file after APK download with enhanced progress
     */
    private void downloadObbFileEnhanced(PubgMobileConfig config, EnhancedDownloadListener listener, File apkFile) {
        Log.d(TAG, "Starting enhanced OBB download for: " + config.packageName);

        // Update dialog title for OBB download
        if (progressDialog != null) {
            progressDialog.updateProgress(0, 0, config.obbSizeMB, 0, 0, 0);
        }

        com.bearmod.loader.auth.DirectKeyAuthManager directKeyAuth =
            com.bearmod.loader.auth.DirectKeyAuthManager.getInstance();

        directKeyAuth.downloadFile(config.obbFileId, new com.bearmod.loader.auth.DirectKeyAuthManager.DownloadCallback() {
            @Override
            public void onSuccess(byte[] fileBytes) {
                executor.execute(() -> {
                    try {
                        // OBB files go to Android/obb directory
                        File obbDir = new File(android.os.Environment.getExternalStorageDirectory(),
                                             "Android/obb/" + config.packageName);
                        if (!obbDir.exists() && !obbDir.mkdirs()) {
                            throw new IOException("Failed to create OBB directory: " + obbDir.getAbsolutePath());
                        }

                        String obbFileName = "main." + config.versionCode + "." + config.packageName + ".obb";
                        File obbFile = new File(obbDir, obbFileName);

                        // Write OBB file with enhanced progress updates
                        writeFileWithEnhancedProgress(fileBytes, obbFile, config.obbSizeMB);

                        // Verify OBB file integrity
                        if (!verifyFileIntegrity(obbFile, fileBytes.length)) {
                            throw new IOException("OBB file integrity verification failed");
                        }

                        handler.post(() -> {
                            isDownloading = false;

                            // Show completion in dialog
                            if (progressDialog != null) {
                                progressDialog.showCompletion(true, "APK + OBB download completed successfully");
                            }

                            // Auto-install APK after both files are ready
                            autoInstallApk(context, apkFile);

                            listener.onSuccess(apkFile.getAbsolutePath()); // Return APK file as primary
                        });

                    } catch (Exception e) {
                        Log.e(TAG, "Failed to save OBB file", e);
                        handler.post(() -> {
                            isDownloading = false;

                            // Show error in dialog
                            if (progressDialog != null) {
                                progressDialog.showCompletion(false, "OBB download failed: " + e.getMessage());
                            }

                            listener.onError("Failed to save OBB file: " + e.getMessage());
                        });
                    }
                });
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Enhanced OBB download failed: " + error);
                handler.post(() -> {
                    isDownloading = false;

                    // Show error in dialog
                    if (progressDialog != null) {
                        progressDialog.showCompletion(false, "OBB download failed: " + error);
                    }

                    listener.onError("OBB download failed: " + error);
                });
            }
        });
    }

    /**
     * Get PUBG Mobile configuration for the specified game
     * @param gameName Game name
     * @return PUBG Mobile configuration or null if not found
     */
    private PubgMobileConfig getPubgMobileConfig(String gameName) {
        switch (gameName.toLowerCase()) {
            case "pubg mobile global":
                return new PubgMobileConfig(
                    "com.tencent.ig",
                    "362906", // APK file ID - replace with actual KeyAuth file ID
                    "362907", // OBB file ID - replace with actual KeyAuth file ID
                    120.5,    // APK size in MB
                    2800.0,   // OBB size in MB
                    "3.8.0",
                    380000
                );
            case "pubg mobile kr":
                return new PubgMobileConfig(
                    "com.pubg.krmobile",
                    "362908", // APK file ID - replace with actual KeyAuth file ID
                    "362909", // OBB file ID - replace with actual KeyAuth file ID
                    125.0,    // APK size in MB
                    2850.0,   // OBB size in MB
                    "3.8.0",
                    380000
                );
            case "pubg mobile tw":
                return new PubgMobileConfig(
                    "com.rekoo.pubgm",
                    "362910", // APK file ID - replace with actual KeyAuth file ID
                    "362911", // OBB file ID - replace with actual KeyAuth file ID
                    118.0,    // APK size in MB
                    2750.0,   // OBB size in MB
                    "3.8.0",
                    380000
                );
            case "pubg mobile vn":
                return new PubgMobileConfig(
                    "com.vng.pubgmobile",
                    "362912", // APK file ID - replace with actual KeyAuth file ID
                    "362913", // OBB file ID - replace with actual KeyAuth file ID
                    122.0,    // APK size in MB
                    2900.0,   // OBB size in MB
                    "3.8.0",
                    380000
                );
            default:
                return null;
        }
    }

    /**
     * Get download directory based on download type and configuration
     * @param downloadType Type of download
     * @param config PUBG Mobile configuration
     * @return Download directory
     */
    private File getDownloadDirectory(DownloadType downloadType, PubgMobileConfig config) {
        switch (downloadType) {
            case APK_ONLY:
            case APK_AND_OBB:
                // APK files go to Downloads/BearMod folder
                return new File(android.os.Environment.getExternalStoragePublicDirectory(
                    android.os.Environment.DIRECTORY_DOWNLOADS), "BearMod");
            case OBB_ONLY:
                // OBB files go to Android/obb directory
                return new File(android.os.Environment.getExternalStorageDirectory(),
                               "Android/obb/" + config.packageName);
            default:
                // Default to app's external files directory
                return new File(context.getExternalFilesDir(null), "downloads");
        }
    }

    /**
     * Write file with progress updates
     * @param fileBytes File data
     * @param outputFile Output file
     * @param totalSizeMB Total size in MB for progress calculation
     */
    private void writeFileWithProgress(byte[] fileBytes, File outputFile, double totalSizeMB) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            int bufferSize = 8192;
            int totalBytes = fileBytes.length;
            int writtenBytes = 0;
            long lastUpdateTime = System.currentTimeMillis();

            while (writtenBytes < totalBytes) {
                int bytesToWrite = Math.min(bufferSize, totalBytes - writtenBytes);
                fos.write(fileBytes, writtenBytes, bytesToWrite);
                writtenBytes += bytesToWrite;

                // Update progress every 100ms
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastUpdateTime > 100) {
                    int progress = (int) ((writtenBytes * 100L) / totalBytes);
                    double downloadedMB = writtenBytes / (1024.0 * 1024.0);

                    if (progressListener != null) {
                        handler.post(() -> progressListener.onProgressUpdate(
                            progress, downloadedMB, totalSizeMB, 0, 0, 0));
                    }

                    lastUpdateTime = currentTime;
                }
            }

            // Final progress update
            if (progressListener != null) {
                handler.post(() -> progressListener.onProgressUpdate(
                    100, totalSizeMB, totalSizeMB, 0, 0, 0));
            }
        }
    }

    /**
     * Write file with enhanced progress updates for dialog
     * @param fileBytes File data
     * @param outputFile Output file
     * @param totalSizeMB Total size in MB for progress calculation
     */
    private void writeFileWithEnhancedProgress(byte[] fileBytes, File outputFile, double totalSizeMB) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            int bufferSize = 8192;
            int totalBytes = fileBytes.length;
            int writtenBytes = 0;
            long lastUpdateTime = System.currentTimeMillis();
            long startTime = System.currentTimeMillis();

            while (writtenBytes < totalBytes) {
                int bytesToWrite = Math.min(bufferSize, totalBytes - writtenBytes);
                fos.write(fileBytes, writtenBytes, bytesToWrite);
                writtenBytes += bytesToWrite;

                // Update progress every 100ms
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastUpdateTime > 100) {
                    int progress = (int) ((writtenBytes * 100L) / totalBytes);
                    double downloadedMB = writtenBytes / (1024.0 * 1024.0);

                    // Calculate speed and ETA
                    long elapsedTime = currentTime - startTime;
                    double speedMBps = 0;
                    int etaMinutes = 0;
                    int etaSeconds = 0;

                    if (elapsedTime > 0) {
                        speedMBps = downloadedMB / (elapsedTime / 1000.0);
                        if (speedMBps > 0) {
                            double remainingMB = totalSizeMB - downloadedMB;
                            double etaTotalSeconds = remainingMB / speedMBps;
                            etaMinutes = (int) (etaTotalSeconds / 60);
                            etaSeconds = (int) (etaTotalSeconds % 60);
                        }
                    }

                    // Update both progress listener and dialog
                    if (progressListener != null) {
                        final double finalSpeedMBps = speedMBps;
                        final int finalEtaMinutes = etaMinutes;
                        final int finalEtaSeconds = etaSeconds;
                        handler.post(() -> progressListener.onProgressUpdate(
                            progress, downloadedMB, totalSizeMB, finalSpeedMBps, finalEtaMinutes, finalEtaSeconds));
                    }

                    if (progressDialog != null) {
                        final double finalSpeedMBps = speedMBps;
                        final int finalEtaMinutes = etaMinutes;
                        final int finalEtaSeconds = etaSeconds;
                        handler.post(() -> progressDialog.updateProgress(
                            progress, downloadedMB, totalSizeMB, finalSpeedMBps, finalEtaMinutes, finalEtaSeconds));
                    }

                    lastUpdateTime = currentTime;
                }
            }

            // Final progress update
            if (progressListener != null) {
                handler.post(() -> progressListener.onProgressUpdate(
                    100, totalSizeMB, totalSizeMB, 0, 0, 0));
            }

            if (progressDialog != null) {
                handler.post(() -> progressDialog.updateProgress(
                    100, totalSizeMB, totalSizeMB, 0, 0, 0));
            }
        }
    }

    /**
     * Show enhanced progress dialog
     * @param context Application context
     * @param gameName Game name
     * @param downloadType Download type
     * @param fileName File name
     */
    private void showEnhancedProgressDialog(Context context, String gameName, DownloadType downloadType, String fileName) {
        String downloadTypeText = getDownloadTypeText(downloadType);

        // Determine icon based on game region
        int iconResId = getGameIcon(gameName);

        progressDialog = new DownloadProgressDialog(context, downloadTypeText, fileName, iconResId);
        progressDialog.show();

        // Set cancel listener
        progressDialog.setDownloadCancelListener(() -> {
            Log.d(TAG, "User requested download cancellation");
            // Cancel the download by setting isDownloading to false
            isDownloading = false;
            if (progressDialog != null) {
                progressDialog.dismiss();
                progressDialog = null;
            }
        });
    }

    /**
     * Get download type text for display
     * @param downloadType Download type
     * @return Display text
     */
    private String getDownloadTypeText(DownloadType downloadType) {
        switch (downloadType) {
            case APK_ONLY:
                return "APK Only";
            case APK_AND_OBB:
                return "APK + OBB";
            case OBB_ONLY:
                return "OBB Only";
            default:
                return "Game Files";
        }
    }

    /**
     * Get game icon based on game name
     * @param gameName Game name
     * @return Icon resource ID
     */
    private int getGameIcon(String gameName) {
        switch (gameName.toLowerCase()) {
            case "pubg mobile global":
                return com.bearmod.loader.R.drawable.ic_pubg_mobile_global_logo;
            case "pubg mobile kr":
                return com.bearmod.loader.R.drawable.ic_pubg_mobile_kr_logo;
            case "pubg mobile tw":
                return com.bearmod.loader.R.drawable.ic_pubg_mobile_tw_logo;
            case "pubg mobile vn":
                return com.bearmod.loader.R.drawable.ic_pubg_mobile_vn_logo;
            default:
                return com.bearmod.loader.R.drawable.ic_pubg_mobile_global_logo;
        }
    }

    /**
     * Generate BearToken for target mod authentication
     * @param packageName Target mod package name
     */
    private void generateBearTokenForTargetMod(String packageName) {
        try {
            Log.d(TAG, "Generating BearToken for target mod: " + packageName);

            // Get current KeyAuth session info (you'll need to implement this)
            String keyAuthSessionId = getCurrentKeyAuthSessionId();
            String userLicenseKey = getCurrentUserLicenseKey();

            if (keyAuthSessionId != null && userLicenseKey != null) {
                BearTokenManager tokenManager = BearTokenManager.getInstance(context);
                BearTokenManager.BearToken bearToken = tokenManager.generateBearToken(keyAuthSessionId, userLicenseKey);

                if (bearToken != null) {
                    Log.d(TAG, "BearToken generated successfully for " + packageName);
                } else {
                    Log.e(TAG, "Failed to generate BearToken for " + packageName);
                }
            } else {
                Log.w(TAG, "KeyAuth session info not available, cannot generate BearToken");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error generating BearToken for " + packageName, e);
        }
    }

    /**
     * Get current KeyAuth session ID
     * @return Session ID or null if not available
     */
    private String getCurrentKeyAuthSessionId() {
        try {
            if (keyAuthManager != null) {
                // Try to get session ID from KeyAuth manager
                return keyAuthManager.getSessionId();
            }
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get KeyAuth session ID", e);
            return null;
        }
    }

    /**
     * Get current user license key
     * @return License key or null if not available
     */
    private String getCurrentUserLicenseKey() {
        try {
            if (keyAuthManager != null) {
                // Try to get license key from KeyAuth manager
                return keyAuthManager.getUserLicenseKey();
            }
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get user license key", e);
            return null;
        }
    }

    /**
     * Verify file integrity
     * @param file File to verify
     * @param expectedSize Expected file size in bytes
     * @return true if file is valid, false otherwise
     */
    private boolean verifyFileIntegrity(File file, long expectedSize) {
        if (!file.exists()) {
            Log.e(TAG, "File does not exist: " + file.getAbsolutePath());
            return false;
        }

        long actualSize = file.length();
        if (actualSize != expectedSize) {
            Log.e(TAG, "File size mismatch. Expected: " + expectedSize + ", Actual: " + actualSize);
            return false;
        }

        Log.d(TAG, "File integrity verified: " + file.getAbsolutePath());
        return true;
    }

    /**
     * Create download info based on game name and download type
     * @param gameName Game name
     * @param downloadType Download type
     * @return Download info
     */
    private DownloadInfo createDownloadInfo(String gameName, DownloadType downloadType) {
        DownloadInfo info = new DownloadInfo();

        String baseName = gameName.replaceAll("\\s+", "-").toLowerCase();

        switch (downloadType) {
            case APK_ONLY:
                info.fileName = baseName + ".apk";
                // Use a working demo APK URL for testing (small file)
                info.downloadUrl = "https://github.com/mozilla-mobile/fenix/releases/download/v121.1.0/fenix-121.1.0-arm64-v8a.apk";
                info.totalSizeMB = 120.5;
                break;
            case APK_AND_OBB:
                info.fileName = baseName + "_full.zip";
                // Use a working demo large file URL for testing
                info.downloadUrl = "https://releases.ubuntu.com/20.04/ubuntu-20.04.6-desktop-amd64.iso";
                info.totalSizeMB = 1100.8;
                break;
            case OBB_ONLY:
                info.fileName = baseName + ".obb";
                // Use a working demo medium file URL for testing
                info.downloadUrl = "https://archive.org/download/SampleVideo1280x7205mb/SampleVideo_1280x720_5mb.mp4";
                info.totalSizeMB = 980.3;
                break;
        }

        return info;
    }

    /**
     * Show progress dialog
     * @param context Application context
     * @param downloadInfo Download information
     */
    private void showProgressDialog(Context context, DownloadInfo downloadInfo) {
        String downloadTypeText = getDownloadTypeText(downloadInfo.fileName);

        // Use PUBG icon for the dialog
        progressDialog = new DownloadProgressDialog(context, downloadTypeText, downloadInfo.fileName,
            com.bearmod.loader.R.drawable.ic_pubg_mobile_global_logo);
        progressDialog.show();
    }

    /**
     * Get download type text for display
     * @param fileName File name
     * @return Display text
     */
    private String getDownloadTypeText(String fileName) {
        if (fileName.endsWith(".apk")) {
            return "APK Only";
        } else if (fileName.contains("_full")) {
            return "APK + OBB";
        } else if (fileName.endsWith(".obb")) {
            return "OBB Only";
        }
        return "Game Files";
    }

    /**
     * Handle download success
     * @param context Application context
     * @param downloadType Download type
     * @param filePath Downloaded file path
     * @param listener Completion listener
     */
    private void handleDownloadSuccess(Context context, DownloadType downloadType, String filePath,
                                     EnhancedDownloadListener listener) {
        Log.d(TAG, "Download completed: " + filePath);

        if (progressDialog != null) {
            progressDialog.showCompletion(true, "Download completed successfully");
        }

        // Auto-install APK if applicable
        if (downloadType == DownloadType.APK_ONLY || downloadType == DownloadType.APK_AND_OBB) {
            File downloadedFile = new File(filePath);
            if (downloadedFile.getName().endsWith(".apk")) {
                autoInstallApk(context, downloadedFile);
            }
        }

        listener.onSuccess(filePath);
    }

    /**
     * Handle download error
     * @param error Error message
     * @param listener Completion listener
     */
    private void handleDownloadError(String error, EnhancedDownloadListener listener) {
        Log.e(TAG, "Download failed: " + error);

        if (progressDialog != null) {
            progressDialog.showCompletion(false, error);
        }

        listener.onError(error);
    }

    /**
     * Auto-install APK file
     * @param context Application context
     * @param apkFile APK file to install
     */
    private void autoInstallApk(Context context, File apkFile) {
        Log.d(TAG, "Auto-installing APK: " + apkFile.getAbsolutePath());

        ApkInstaller.installApk(context, apkFile, new ApkInstaller.InstallationCallback() {
            @Override
            public void onSuccess(String message) {
                Log.d(TAG, "APK installation started: " + message);
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "APK installation failed: " + error);
            }
        });
    }

    /**
     * Download listener interface
     */
    public interface DownloadListener {
        void onSuccess(File downloadedFile);
        void onError(String error);
    }

    /**
     * Enhanced download listener interface
     */
    public interface EnhancedDownloadListener {
        void onSuccess(String filePath);
        void onError(String error);
    }

    /**
     * Download progress listener interface with enhanced information
     */
    public interface DownloadProgressListener {
        /**
         * Called when download progress is updated
         * @param progress Download progress (0-100)
         * @param downloadedMB Downloaded size in MB
         * @param totalSizeMB Total size in MB
         * @param speedMBps Download speed in MB/s
         * @param etaMinutes Estimated time remaining (minutes)
         * @param etaSeconds Estimated time remaining (seconds)
         */
        void onProgressUpdate(int progress, double downloadedMB, double totalSizeMB,
                             double speedMBps, int etaMinutes, int etaSeconds);
    }

    /**
     * Download type enum
     */
    public enum DownloadType {
        APK_ONLY,
        APK_AND_OBB,
        OBB_ONLY
    }

    /**
     * Download info class
     */
    private static class DownloadInfo {
        String fileName;
        String downloadUrl;
        double totalSizeMB;
    }

    /**
     * PUBG Mobile configuration class
     */
    private static class PubgMobileConfig {
        final String packageName;
        final String apkFileId;
        final String obbFileId;
        final double apkSizeMB;
        final double obbSizeMB;
        final String version;
        final int versionCode;

        PubgMobileConfig(String packageName, String apkFileId, String obbFileId,
                        double apkSizeMB, double obbSizeMB, String version, int versionCode) {
            this.packageName = packageName;
            this.apkFileId = apkFileId;
            this.obbFileId = obbFileId;
            this.apkSizeMB = apkSizeMB;
            this.obbSizeMB = obbSizeMB;
            this.version = version;
            this.versionCode = versionCode;
        }
    }
}
