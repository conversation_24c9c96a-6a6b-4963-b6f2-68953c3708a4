// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutDownloadProgressModernBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final Button btnCancelDownload;

  @NonNull
  public final Button btnPauseResume;

  @NonNull
  public final MaterialCardView cardDownloadProgress;

  @NonNull
  public final Chip chipDownloadStatus;

  @NonNull
  public final CircularProgressIndicator progressCircular;

  @NonNull
  public final LinearProgressIndicator progressDownload;

  @NonNull
  public final TextView tvDownloadPercentage;

  @NonNull
  public final TextView tvDownloadSpeed;

  @NonNull
  public final TextView tvDownloadStatus;

  @NonNull
  public final TextView tvDownloadedSize;

  @NonNull
  public final TextView tvEta;

  private LayoutDownloadProgressModernBinding(@NonNull MaterialCardView rootView,
      @NonNull Button btnCancelDownload, @NonNull Button btnPauseResume,
      @NonNull MaterialCardView cardDownloadProgress, @NonNull Chip chipDownloadStatus,
      @NonNull CircularProgressIndicator progressCircular,
      @NonNull LinearProgressIndicator progressDownload, @NonNull TextView tvDownloadPercentage,
      @NonNull TextView tvDownloadSpeed, @NonNull TextView tvDownloadStatus,
      @NonNull TextView tvDownloadedSize, @NonNull TextView tvEta) {
    this.rootView = rootView;
    this.btnCancelDownload = btnCancelDownload;
    this.btnPauseResume = btnPauseResume;
    this.cardDownloadProgress = cardDownloadProgress;
    this.chipDownloadStatus = chipDownloadStatus;
    this.progressCircular = progressCircular;
    this.progressDownload = progressDownload;
    this.tvDownloadPercentage = tvDownloadPercentage;
    this.tvDownloadSpeed = tvDownloadSpeed;
    this.tvDownloadStatus = tvDownloadStatus;
    this.tvDownloadedSize = tvDownloadedSize;
    this.tvEta = tvEta;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutDownloadProgressModernBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutDownloadProgressModernBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_download_progress_modern, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutDownloadProgressModernBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel_download;
      Button btnCancelDownload = ViewBindings.findChildViewById(rootView, id);
      if (btnCancelDownload == null) {
        break missingId;
      }

      id = R.id.btn_pause_resume;
      Button btnPauseResume = ViewBindings.findChildViewById(rootView, id);
      if (btnPauseResume == null) {
        break missingId;
      }

      MaterialCardView cardDownloadProgress = (MaterialCardView) rootView;

      id = R.id.chip_download_status;
      Chip chipDownloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (chipDownloadStatus == null) {
        break missingId;
      }

      id = R.id.progress_circular;
      CircularProgressIndicator progressCircular = ViewBindings.findChildViewById(rootView, id);
      if (progressCircular == null) {
        break missingId;
      }

      id = R.id.progress_download;
      LinearProgressIndicator progressDownload = ViewBindings.findChildViewById(rootView, id);
      if (progressDownload == null) {
        break missingId;
      }

      id = R.id.tv_download_percentage;
      TextView tvDownloadPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadPercentage == null) {
        break missingId;
      }

      id = R.id.tv_download_speed;
      TextView tvDownloadSpeed = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadSpeed == null) {
        break missingId;
      }

      id = R.id.tv_download_status;
      TextView tvDownloadStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadStatus == null) {
        break missingId;
      }

      id = R.id.tv_downloaded_size;
      TextView tvDownloadedSize = ViewBindings.findChildViewById(rootView, id);
      if (tvDownloadedSize == null) {
        break missingId;
      }

      id = R.id.tv_eta;
      TextView tvEta = ViewBindings.findChildViewById(rootView, id);
      if (tvEta == null) {
        break missingId;
      }

      return new LayoutDownloadProgressModernBinding((MaterialCardView) rootView, btnCancelDownload,
          btnPauseResume, cardDownloadProgress, chipDownloadStatus, progressCircular,
          progressDownload, tvDownloadPercentage, tvDownloadSpeed, tvDownloadStatus,
          tvDownloadedSize, tvEta);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
