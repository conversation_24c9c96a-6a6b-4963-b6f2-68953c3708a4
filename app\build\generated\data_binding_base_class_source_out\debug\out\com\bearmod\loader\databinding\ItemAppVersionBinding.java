// Generated by view binder compiler. Do not edit!
package com.bearmod.loader.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bearmod.loader.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAppVersionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView appDescription;

  @NonNull
  public final ImageView appIcon;

  @NonNull
  public final TextView appName;

  @NonNull
  public final TextView appVersion;

  @NonNull
  public final MaterialButton btnAction;

  @NonNull
  public final TextView bulletPoint;

  @NonNull
  public final LinearLayout downloadProgressContainer;

  @NonNull
  public final ProgressBar progressBarInline;

  @NonNull
  public final TextView status;

  @NonNull
  public final TextView tvEtaInline;

  @NonNull
  public final TextView tvProgressInline;

  @NonNull
  public final TextView tvSpeedInline;

  private ItemAppVersionBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView appDescription, @NonNull ImageView appIcon, @NonNull TextView appName,
      @NonNull TextView appVersion, @NonNull MaterialButton btnAction,
      @NonNull TextView bulletPoint, @NonNull LinearLayout downloadProgressContainer,
      @NonNull ProgressBar progressBarInline, @NonNull TextView status,
      @NonNull TextView tvEtaInline, @NonNull TextView tvProgressInline,
      @NonNull TextView tvSpeedInline) {
    this.rootView = rootView;
    this.appDescription = appDescription;
    this.appIcon = appIcon;
    this.appName = appName;
    this.appVersion = appVersion;
    this.btnAction = btnAction;
    this.bulletPoint = bulletPoint;
    this.downloadProgressContainer = downloadProgressContainer;
    this.progressBarInline = progressBarInline;
    this.status = status;
    this.tvEtaInline = tvEtaInline;
    this.tvProgressInline = tvProgressInline;
    this.tvSpeedInline = tvSpeedInline;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAppVersionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAppVersionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_app_version, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAppVersionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_description;
      TextView appDescription = ViewBindings.findChildViewById(rootView, id);
      if (appDescription == null) {
        break missingId;
      }

      id = R.id.app_icon;
      ImageView appIcon = ViewBindings.findChildViewById(rootView, id);
      if (appIcon == null) {
        break missingId;
      }

      id = R.id.app_name;
      TextView appName = ViewBindings.findChildViewById(rootView, id);
      if (appName == null) {
        break missingId;
      }

      id = R.id.app_version;
      TextView appVersion = ViewBindings.findChildViewById(rootView, id);
      if (appVersion == null) {
        break missingId;
      }

      id = R.id.btn_action;
      MaterialButton btnAction = ViewBindings.findChildViewById(rootView, id);
      if (btnAction == null) {
        break missingId;
      }

      id = R.id.bullet_point;
      TextView bulletPoint = ViewBindings.findChildViewById(rootView, id);
      if (bulletPoint == null) {
        break missingId;
      }

      id = R.id.download_progress_container;
      LinearLayout downloadProgressContainer = ViewBindings.findChildViewById(rootView, id);
      if (downloadProgressContainer == null) {
        break missingId;
      }

      id = R.id.progress_bar_inline;
      ProgressBar progressBarInline = ViewBindings.findChildViewById(rootView, id);
      if (progressBarInline == null) {
        break missingId;
      }

      id = R.id.status;
      TextView status = ViewBindings.findChildViewById(rootView, id);
      if (status == null) {
        break missingId;
      }

      id = R.id.tv_eta_inline;
      TextView tvEtaInline = ViewBindings.findChildViewById(rootView, id);
      if (tvEtaInline == null) {
        break missingId;
      }

      id = R.id.tv_progress_inline;
      TextView tvProgressInline = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressInline == null) {
        break missingId;
      }

      id = R.id.tv_speed_inline;
      TextView tvSpeedInline = ViewBindings.findChildViewById(rootView, id);
      if (tvSpeedInline == null) {
        break missingId;
      }

      return new ItemAppVersionBinding((MaterialCardView) rootView, appDescription, appIcon,
          appName, appVersion, btnAction, bulletPoint, downloadProgressContainer, progressBarInline,
          status, tvEtaInline, tvProgressInline, tvSpeedInline);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
