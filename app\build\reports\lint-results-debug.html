<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 13 errors and 193 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Thu May 29 11:04:37 MMT 2025 by AGP (8.10.0)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#LintBaseline"><i class="material-icons warning-icon">warning</i>Baseline Applied (1)</a>
      <a class="mdl-navigation__link" href="#LintBaselineFixed"><i class="material-icons warning-icon">warning</i>Baselined Issues Fixed (1)</a>
      <a class="mdl-navigation__link" href="#WrongViewCast"><i class="material-icons error-icon">error</i>Mismatched view type (1)</a>
      <a class="mdl-navigation__link" href="#ScopedStorage"><i class="material-icons warning-icon">warning</i>Affected by scoped storage (1)</a>
      <a class="mdl-navigation__link" href="#NewApi"><i class="material-icons error-icon">error</i>Calling new methods on older versions (7)</a>
      <a class="mdl-navigation__link" href="#NotSibling"><i class="material-icons error-icon">error</i>Invalid Constraints (3)</a>
      <a class="mdl-navigation__link" href="#UnusedAttribute"><i class="material-icons warning-icon">warning</i>Attribute unused on older versions (1)</a>
      <a class="mdl-navigation__link" href="#InflateParams"><i class="material-icons warning-icon">warning</i>Layout Inflation without a Parent (1)</a>
      <a class="mdl-navigation__link" href="#AndroidGradlePluginVersion"><i class="material-icons warning-icon">warning</i>Obsolete Android Gradle Plugin Version (2)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (1)</a>
      <a class="mdl-navigation__link" href="#DiscouragedApi"><i class="material-icons warning-icon">warning</i>Using discouraged APIs (2)</a>
      <a class="mdl-navigation__link" href="#UseAppTint"><i class="material-icons error-icon">error</i><code>app:tint</code> attribute should be used on <code>ImageView</code> and <code>ImageButton</code> (2)</a>
      <a class="mdl-navigation__link" href="#NotifyDataSetChanged"><i class="material-icons warning-icon">warning</i>Invalidating All RecyclerView Data (1)</a>
      <a class="mdl-navigation__link" href="#ObsoleteSdkInt"><i class="material-icons warning-icon">warning</i>Obsolete SDK_INT Version Check (1)</a>
      <a class="mdl-navigation__link" href="#UseCompoundDrawables"><i class="material-icons warning-icon">warning</i>Node can be replaced by a <code>TextView</code> with compound drawables (3)</a>
      <a class="mdl-navigation__link" href="#VectorPath"><i class="material-icons warning-icon">warning</i>Long vector paths (1)</a>
      <a class="mdl-navigation__link" href="#Overdraw"><i class="material-icons warning-icon">warning</i>Overdraw: Painting regions more than once (1)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (117)</a>
      <a class="mdl-navigation__link" href="#UseTomlInstead"><i class="material-icons warning-icon">warning</i>Use TOML Version Catalog Instead (1)</a>
      <a class="mdl-navigation__link" href="#ClickableViewAccessibility"><i class="material-icons warning-icon">warning</i>Accessibility in Custom Views (2)</a>
      <a class="mdl-navigation__link" href="#ContentDescription"><i class="material-icons warning-icon">warning</i>Image without <code>contentDescription</code> (4)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (35)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (19)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Lint">Lint</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#LintBaseline">LintBaseline</a>: Baseline Applied</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#LintBaselineFixed">LintBaselineFixed</a>: Baselined Issues Fixed</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#WrongViewCast">WrongViewCast</a>: Mismatched view type</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ScopedStorage">ScopedStorage</a>: Affected by scoped storage</td></tr>
<tr>
<td class="countColumn">7</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#NewApi">NewApi</a>: Calling new methods on older versions</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#NotSibling">NotSibling</a>: Invalid Constraints</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedAttribute">UnusedAttribute</a>: Attribute unused on older versions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InflateParams">InflateParams</a>: Layout Inflation without a Parent</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AndroidGradlePluginVersion">AndroidGradlePluginVersion</a>: Obsolete Android Gradle Plugin Version</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DiscouragedApi">DiscouragedApi</a>: Using discouraged APIs</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#UseAppTint">UseAppTint</a>: <code>app:tint</code> attribute should be used on <code>ImageView</code> and <code>ImageButton</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#NotifyDataSetChanged">NotifyDataSetChanged</a>: Invalidating All RecyclerView Data</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteSdkInt">ObsoleteSdkInt</a>: Obsolete SDK_INT Version Check</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseCompoundDrawables">UseCompoundDrawables</a>: Node can be replaced by a <code>TextView</code> with compound drawables</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#VectorPath">VectorPath</a>: Long vector paths</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Overdraw">Overdraw</a>: Overdraw: Painting regions more than once</td></tr>
<tr>
<td class="countColumn">117</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Productivity">Productivity</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseTomlInstead">UseTomlInstead</a>: Use TOML Version Catalog Instead</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ClickableViewAccessibility">ClickableViewAccessibility</a>: Accessibility in Custom Views</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ContentDescription">ContentDescription</a>: Image without <code>contentDescription</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">35</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">19</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (48)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (39)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Lint"></a>
<a name="LintBaseline"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="LintBaselineCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Baseline Applied</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../lint-baseline.xml">../../lint-baseline.xml</a></span>: <span class="message">1 error and 94 warnings were filtered out because they are listed in the baseline file, lint-baseline.xml</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationLintBaseline" style="display: none;">
Lint can be configured with a "baseline"; a set of current issues found in a codebase, which future runs of lint will silently ignore. Only new issues not found in the baseline are reported.<br/>
<br/>
Note that while opening files in the IDE, baseline issues are not filtered out; the purpose of baselines is to allow you to get started using lint and break the build on all newly introduced errors, without having to go back and fix the entire codebase up front. However, when you open up existing files you still want to be aware of and fix issues as you come across them.<br/>
<br/>
This issue type is used to emit an informational-only warning if any issues were filtered out due to baseline matching. That way, you don't have a false sense of security if you forgot that you've checked in a baseline file.<br/>To suppress this error, use the issue id "LintBaseline" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">LintBaseline</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 10/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationLintBaselineLink" onclick="reveal('explanationLintBaseline');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="LintBaselineCardLink" onclick="hideid('LintBaselineCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="LintBaselineFixed"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="LintBaselineFixedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Baselined Issues Fixed</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../lint-baseline.xml">../../lint-baseline.xml</a></span>: <span class="message">17 errors/warnings were listed in the baseline file (lint-baseline.xml) but not found in the project; perhaps they have been fixed? Another possible explanation is that lint recently stopped analyzing (and including results from) dependent projects by default. You can turn this back on with <code>android.lintOptions.checkDependencies=true</code>. Unmatched issue types: ContentDescription (2), DefaultLocale (2), LockedOrientationActivity, Overdraw (2), SelectedPhotoAccess, UnusedResources (9)</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationLintBaselineFixed" style="display: none;">
If a lint baseline describes a problem which is no longer reported, then the problem has either been fixed, or perhaps the issue type has been disabled. In any case, the entry can be removed from the baseline (such that if the issue is reintroduced at some point, lint will complain rather than just silently starting to match the old baseline entry again.)<br/>To suppress this error, use the issue id "LintBaselineFixed" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">LintBaselineFixed</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Hint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 10/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationLintBaselineFixedLink" onclick="reveal('explanationLintBaselineFixed');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="LintBaselineFixedCardLink" onclick="hideid('LintBaselineFixedCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="WrongViewCast"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="WrongViewCastCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Mismatched view type</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/auth/TapToUnlockActivity.java">../../src/main/java/com/bearmod/loader/ui/auth/TapToUnlockActivity.java</a>:34</span>: <span class="message">Unexpected implicit cast to <code>ShapeableImageView</code>: layout tag was <code>ImageView</code></span><br /><pre class="errorlines">
<span class="lineno"> 31 </span>        
<span class="lineno"> 32 </span>        setContentView(R.layout.activity_tap_unlock);
<span class="lineno"> 33 </span>
<span class="caretline"><span class="lineno"> 34 </span>        <span class="error">glyphView = findViewById(R.id.glyphView)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 35 </span>        tapText = findViewById(R.id.tapText);
<span class="lineno"> 36 </span>
<span class="lineno"> 37 </span>        <span class="comment">// Set up shape appearance</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationWrongViewCast" style="display: none;">
Keeps track of the view types associated with ids and if it finds a usage of the id in the Java code it ensures that it is treated as the same type.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WrongViewCast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">WrongViewCast</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationWrongViewCastLink" onclick="reveal('explanationWrongViewCast');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="WrongViewCastCardLink" onclick="hideid('WrongViewCastCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ScopedStorage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ScopedStorageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Affected by scoped storage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:12</span>: <span class="message">WRITE_EXTERNAL_STORAGE no longer provides write access when targeting Android 10+</span><br /><pre class="errorlines">
<span class="lineno">   9 </span>    <span class="comment">&lt;!-- Storage permissions --></span>
<span class="lineno">  10 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_EXTERNAL_STORAGE"</span> 
<span class="lineno">  11 </span>        <span class="prefix">android:</span><span class="attribute">maxSdkVersion</span>=<span class="value">"32"</span> />
<span class="caretline"><span class="lineno">  12 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.WRITE_EXTERNAL_STORAGE</span></span><span class="value">"</span> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">maxSdkVersion</span>=<span class="value">"32"</span> />
<span class="lineno">  14 </span>    
<span class="lineno">  15 </span>    <span class="comment">&lt;!-- Media permissions for Android 13+ --></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationScopedStorage" style="display: none;">
Scoped storage is enforced on Android 10+ (or Android 11+ if using <code>requestLegacyExternalStorage</code>). In particular, <code>WRITE_EXTERNAL_STORAGE</code> will no longer provide write access to all files; it will provide the equivalent of <code>READ_EXTERNAL_STORAGE</code> instead.<br/>
<br/>
As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:<br/>
* <code>android.permission.READ_MEDIA_IMAGES</code><br/>
* <code>android.permission.READ_MEDIA_VIDEO</code><br/>
* <code>android.permission.READ_MEDIA_AUDIO</code><br/>
<br/>
and then add <code>maxSdkVersion="33"</code> to the older permission. See the developer guide for how to do this: <a href="https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions">https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions</a><br/>
<br/>
The <code>MANAGE_EXTERNAL_STORAGE</code> permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at <a href="https://goo.gle/android-mediastore-createwriterequest">https://goo.gle/android-mediastore-createwriterequest</a>.<br/>
<br/>
To learn more, read these resources: Play policy: <a href="https://goo.gle/policy-storage-help">https://goo.gle/policy-storage-help</a> Allowable use cases: <a href="https://goo.gle/policy-storage-usecases">https://goo.gle/policy-storage-usecases</a><br/><div class="moreinfo">More info: <a href="https://goo.gle/android-storage-usecases">https://goo.gle/android-storage-usecases</a>
</div>To suppress this error, use the issue id "ScopedStorage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ScopedStorage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationScopedStorageLink" onclick="reveal('explanationScopedStorage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ScopedStorageCardLink" onclick="hideid('ScopedStorageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NewApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NewApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Calling new methods on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:32</span>: <span class="message"><code>android:windowLightNavigationBar</code> requires API level 27 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  29 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno">  30 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:navigationBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno">  31 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowLightStatusBar"</span>>false<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno">  32 </span>        <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowLightNavigationBar"</span></span>>false<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  33 </span>
<span class="lineno">  34 </span>        <span class="comment">&lt;!-- Window background --></span>
<span class="lineno">  35 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowBackground"</span>>@color/background<span class="tag">&lt;/item></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:38</span>: <span class="message"><code>android:windowSplashScreenBackground</code> requires API level 31 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  35 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowBackground"</span>>@color/background<span class="tag">&lt;/item></span>
<span class="lineno">  36 </span>
<span class="lineno">  37 </span>  <span class="comment">&lt;!-- Splash screen theme --></span>
<span class="caretline"><span class="lineno">  38 </span>  <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowSplashScreenBackground"</span></span>>@color/background<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  39 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenAnimatedIcon"</span>>@drawable/ic_launcher_foreground<span class="tag">&lt;/item></span>
<span class="lineno">  40 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenIconBackgroundColor"</span>>@color/primary<span class="tag">&lt;/item></span>
<span class="lineno">  41 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenBrandingImage"</span>>@drawable/splash_branding<span class="tag">&lt;/item></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:39</span>: <span class="message"><code>android:windowSplashScreenAnimatedIcon</code> requires API level 31 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  36 </span>
<span class="lineno">  37 </span>  <span class="comment">&lt;!-- Splash screen theme --></span>
<span class="lineno">  38 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenBackground"</span>>@color/background<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno">  39 </span>  <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowSplashScreenAnimatedIcon"</span></span>>@drawable/ic_launcher_foreground<span class="tag">&lt;/item></span>&nbsp;</span>
<span class="lineno">  40 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenIconBackgroundColor"</span>>@color/primary<span class="tag">&lt;/item></span>
<span class="lineno">  41 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenBrandingImage"</span>>@drawable/splash_branding<span class="tag">&lt;/item></span>
</pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:40</span>: <span class="message"><code>android:windowSplashScreenIconBackgroundColor</code> requires API level 31 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  37 </span>  <span class="comment">&lt;!-- Splash screen theme --></span>
<span class="lineno">  38 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenBackground"</span>>@color/background<span class="tag">&lt;/item></span>
<span class="lineno">  39 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenAnimatedIcon"</span>>@drawable/ic_launcher_foreground<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno">  40 </span>  <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowSplashScreenIconBackgroundColor"</span></span>>@color/primary<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  41 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenBrandingImage"</span>>@drawable/splash_branding<span class="tag">&lt;/item></span>
<span class="lineno">  42 </span>
<span class="lineno">  43 </span>  <span class="comment">&lt;!-- Material shape appearance --></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:41</span>: <span class="message"><code>android:windowSplashScreenBrandingImage</code> requires API level 31 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  38 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenBackground"</span>>@color/background<span class="tag">&lt;/item></span>
<span class="lineno">  39 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenAnimatedIcon"</span>>@drawable/ic_launcher_foreground<span class="tag">&lt;/item></span>
<span class="lineno">  40 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowSplashScreenIconBackgroundColor"</span>>@color/primary<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno">  41 </span>  <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowSplashScreenBrandingImage"</span></span>>@drawable/splash_branding<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  42 </span>
<span class="lineno">  43 </span>  <span class="comment">&lt;!-- Material shape appearance --></span>
<span class="lineno">  44 </span>  <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"shapeAppearanceSmallComponent"</span>>@style/ShapeAppearance.BearLoader.SmallComponent<span class="tag">&lt;/item></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:57</span>: <span class="message"><code>android:windowLightNavigationBar</code> requires API level 27 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  54 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno">  55 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:navigationBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno">  56 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowLightStatusBar"</span>>false<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno">  57 </span>        <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowLightNavigationBar"</span></span>>false<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  58 </span>    <span class="tag">&lt;/style></span>
<span class="lineno">  59 </span>
<span class="lineno">  60 </span>    <span class="comment">&lt;!-- Splash screen theme --></span></pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:255</span>: <span class="message"><code>android:windowLightNavigationBar</code> requires API level 27 (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno"> 252 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno"> 253 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:navigationBarColor"</span>>@android:color/transparent<span class="tag">&lt;/item></span>
<span class="lineno"> 254 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:windowLightStatusBar"</span>>false<span class="tag">&lt;/item></span>
<span class="caretline"><span class="lineno"> 255 </span>        <span class="tag">&lt;item</span><span class="attribute"> </span><span class="error"><span class="attribute">name</span>=<span class="value">"android:windowLightNavigationBar"</span></span>>false<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 256 </span>    <span class="tag">&lt;/style></span>
<span class="lineno"> 257 </span><span class="tag">&lt;/resources></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNewApi" style="display: none;">
This check scans through all the Android API calls in the application and warns about any calls that are not available on <b>all</b> versions targeted by this application (according to its minimum SDK attribute in the manifest).<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>
<br/>
If you are deliberately setting <code>android:</code> attributes in style definitions, make sure you place this in a <code>values-v</code><i>NN</i> folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.<br/>
<br/>
Similarly, you can use tools:targetApi="11" in an XML file to indicate that the element will only be inflated in an adequate context.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NewApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNewApiLink" onclick="reveal('explanationNewApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NewApiCardLink" onclick="hideid('NewApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NotSibling"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NotSiblingCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Invalid Constraints</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:110</span>: <span class="message"><code>@+id/bottom_navigation</code> is not a sibling in the same <code>ConstraintLayout</code></span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="lineno"> 108 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="lineno"> 109 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>
<span class="caretline"><span class="lineno"> 110 </span>                <span class="error"><span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toTopOf</span>=<span class="value">"@+id/bottom_navigation"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 112 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 113 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toBottomOf</span>=<span class="value">"@+id/tv_available_patches"</span>>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:133</span>: <span class="message"><code>@+id/bottom_navigation</code> is not a sibling in the same <code>ConstraintLayout</code></span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span>
<span class="lineno"> 131 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span>
<span class="lineno"> 132 </span>                <span class="prefix">android:</span><span class="attribute">visibility</span>=<span class="value">"gone"</span>
<span class="caretline"><span class="lineno"> 133 </span>                <span class="error"><span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toTopOf</span>=<span class="value">"@+id/bottom_navigation"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 135 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 136 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toBottomOf</span>=<span class="value">"@+id/tv_available_patches"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:161</span>: <span class="message"><code>@+id/bottom_navigation</code> is not a sibling in the same <code>ConstraintLayout</code></span><br /><pre class="errorlines">
<span class="lineno"> 158 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno"> 159 </span>                <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="lineno"> 160 </span>                <span class="prefix">android:</span><span class="attribute">visibility</span>=<span class="value">"gone"</span>
<span class="caretline"><span class="lineno"> 161 </span>                <span class="error"><span class="prefix">app:</span><span class="attribute">layout_constraintBottom_toTopOf</span>=<span class="value">"@+id/bottom_navigation"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 162 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 163 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toStartOf</span>=<span class="value">"parent"</span>
<span class="lineno"> 164 </span>                <span class="prefix">app:</span><span class="attribute">layout_constraintTop_toBottomOf</span>=<span class="value">"@+id/tv_available_patches"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNotSibling" style="display: none;">
Layout constraints in a given <code>ConstraintLayout</code> or <code>RelativeLayout</code> should reference other views within the same relative layout (but not itself!)<br/>To suppress this error, use the issue id "NotSibling" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NotSibling</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Fatal</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNotSiblingLink" onclick="reveal('explanationNotSibling');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NotSiblingCardLink" onclick="hideid('NotSiblingCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedAttribute"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedAttributeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Attribute unused on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/item_patch.xml">../../src/main/res/layout/item_patch.xml</a>:71</span>: <span class="message">Attribute <code>iconTint</code> is only used in API level 26 and higher (current min is 24)</span><br /><pre class="errorlines">
<span class="lineno">  68 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span>
<span class="lineno">  69 </span>            <span class="prefix">android:</span><span class="attribute">backgroundTint</span>=<span class="value">"@color/accent"</span>
<span class="lineno">  70 </span>            <span class="prefix">android:</span><span class="attribute">icon</span>=<span class="value">"@drawable/ic_download"</span>
<span class="caretline"><span class="lineno">  71 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">iconTint</span>=<span class="value">"@color/white"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  72 </span>            <span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">""</span>
<span class="lineno">  73 </span>            <span class="prefix">app:</span><span class="attribute">cornerRadius</span>=<span class="value">"24dp"</span>
<span class="lineno">  74 </span>            <span class="prefix">app:</span><span class="attribute">layout_constraintEnd_toEndOf</span>=<span class="value">"parent"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedAttribute" style="display: none;">
This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the <code>minSdkVersion</code> attribute).<br/>
<br/>
This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.<br/>
<br/>
Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new <code>&lt;tag></code> element in layouts introduced in API 21.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedAttribute" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedAttribute</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedAttributeLink" onclick="reveal('explanationUnusedAttribute');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedAttributeCardLink" onclick="hideid('UnusedAttributeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InflateParams"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InflateParamsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Layout Inflation without a Parent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:58</span>: <span class="message">Avoid passing <code>null</code> as the view root (needed to resolve layout parameters on the inflated layout's root element)</span><br /><pre class="errorlines">
<span class="lineno">  55 </span>  <span class="keyword">super</span>.onCreate(savedInstanceState);
<span class="lineno">  56 </span>  
<span class="lineno">  57 </span>  <span class="comment">// Inflate custom layout</span>
<span class="caretline"><span class="lineno">  58 </span>  View view = LayoutInflater.from(getContext()).inflate(R.layout.dialog_download_progress, <span class="warning"><span class="keyword">null</span></span>);</span>
<span class="lineno">  59 </span>  setView(view);
<span class="lineno">  60 </span>  
<span class="lineno">  61 </span>  <span class="comment">// Initialize views</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInflateParams" style="display: none;">
When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored.<br/><div class="moreinfo">More info: <a href="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/">https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/</a>
</div>To suppress this error, use the issue id "InflateParams" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InflateParams</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInflateParamsLink" onclick="reveal('explanationInflateParams');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InflateParamsCardLink" onclick="hideid('InflateParamsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AndroidGradlePluginVersion"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AndroidGradlePluginVersionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Android Gradle Plugin Version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.application than 8.10.0 is available: 8.10.1</span><br /><pre class="errorlines">
<span class="lineno">   1 </span>[versions]
<span class="caretline"><span class="lineno">   2 </span>agp = <span class="warning">"8.10.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>concurrentFutures = "1.2.0"
<span class="lineno">   4 </span>constraintlayoutVersion = "2.2.1"
<span class="lineno">   5 </span>converterGson = "3.0.0"
</pre>

<span class="location"><a href="../../../gradle/libs.versions.toml">../../../gradle/libs.versions.toml</a>:2</span>: <span class="message">A newer version of com.android.library than 8.10.0 is available: 8.10.1</span><br /><pre class="errorlines">
<span class="lineno">   1 </span>[versions]
<span class="caretline"><span class="lineno">   2 </span>agp = <span class="warning">"8.10.0"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   3 </span>concurrentFutures = "1.2.0"
<span class="lineno">   4 </span>constraintlayoutVersion = "2.2.1"
<span class="lineno">   5 </span>converterGson = "3.0.0"
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAndroidGradlePluginVersion" style="display: none;">
This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AndroidGradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AndroidGradlePluginVersion</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAndroidGradlePluginVersionLink" onclick="reveal('explanationAndroidGradlePluginVersion');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AndroidGradlePluginVersionCardLink" onclick="hideid('AndroidGradlePluginVersionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:110</span>: <span class="message">A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-alpha07</span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>    implementation(libs.json)
<span class="lineno"> 108 </span>
<span class="lineno"> 109 </span>    // AndroidX Security Crypto for EncryptedSharedPreferences
<span class="caretline"><span class="lineno"> 110 </span>    implementation(<span class="warning">"androidx.security:security-crypto:1.1.0-alpha06"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>
<span class="lineno"> 112 </span>    // Room database
<span class="lineno"> 113 </span>    implementation(libs.room.runtime.v271)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DiscouragedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DiscouragedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using discouraged APIs</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:99</span>: <span class="message">Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.</span><br /><pre class="errorlines">
<span class="lineno">  96 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".ui.download.DownloadActivity"</span>
<span class="lineno">  97 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"false"</span>
<span class="lineno">  98 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.BearLoader.NoActionBar"</span>
<span class="caretline"><span class="lineno">  99 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"unspecified"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 100 </span>
<span class="lineno"> 101 </span>        <span class="comment">&lt;!-- Settings Activity --></span>
<span class="lineno"> 102 </span>        <span class="tag">&lt;activity</span></pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:106</span>: <span class="message">Should not restrict activity to fixed orientation. This may not be suitable for different form factors, causing the app to be letterboxed.</span><br /><pre class="errorlines">
<span class="lineno"> 103 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">".ui.settings.SettingsActivity"</span>
<span class="lineno"> 104 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"false"</span>
<span class="lineno"> 105 </span>            <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.BearLoader.NoActionBar"</span>
<span class="caretline"><span class="lineno"> 106 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">screenOrientation</span>=<span class="value">"unspecified"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 107 </span>
<span class="lineno"> 108 </span>        <span class="comment">&lt;!-- FileProvider for APK installation --></span>
<span class="lineno"> 109 </span>        <span class="tag">&lt;provider</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDiscouragedApi" style="display: none;">
Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior).<br/>To suppress this error, use the issue id "DiscouragedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DiscouragedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDiscouragedApiLink" onclick="reveal('explanationDiscouragedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DiscouragedApiCardLink" onclick="hideid('DiscouragedApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseAppTint"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseAppTintCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">app:tint attribute should be used on ImageView and ImageButton</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:303</span>: <span class="message">Must use <code>app:tint</code> instead of <code>android:tint</code></span><br /><pre class="errorlines">
<span class="lineno"> 300 </span>                        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"24dp"</span>
<span class="lineno"> 301 </span>                        <span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"@string/icon_search"</span>
<span class="lineno"> 302 </span>                        <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@drawable/ic_search"</span>
<span class="caretline"><span class="lineno"> 303 </span>                        <span class="error"><span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"@color/text_secondary"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 304 </span>
<span class="lineno"> 305 </span>                    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 306 </span><span class="attribute">                        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tvTotalSize"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_download_modern.xml">../../src/main/res/layout/activity_download_modern.xml</a>:312</span>: <span class="message">Must use <code>app:tint</code> instead of <code>android:tint</code></span><br /><pre class="errorlines">
<span class="lineno"> 309 </span>                        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"24dp"</span>
<span class="lineno"> 310 </span>                        <span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"@string/icon_search"</span>
<span class="lineno"> 311 </span>                        <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@drawable/ic_search"</span>
<span class="caretline"><span class="lineno"> 312 </span>                        <span class="error"><span class="prefix">android:</span><span class="attribute">tint</span>=<span class="value">"@color/text_secondary"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 313 </span>
<span class="lineno"> 314 </span>                    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 315 </span><span class="attribute">                        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_total_size"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseAppTint" style="display: none;">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/>To suppress this error, use the issue id "UseAppTint" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseAppTint</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseAppTintLink" onclick="reveal('explanationUseAppTint');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseAppTintCardLink" onclick="hideid('UseAppTintCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="NotifyDataSetChanged"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NotifyDataSetChangedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Invalidating All RecyclerView Data</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/main/AppVersionAdapter.java">../../src/main/java/com/bearmod/loader/ui/main/AppVersionAdapter.java</a>:36</span>: <span class="message">It will always be more efficient to use more specific change events if you can. Rely on <code>notifyDataSetChanged</code> as a last resort.</span><br /><pre class="errorlines">
<span class="lineno">  33 </span>
<span class="lineno">  34 </span>    <span class="keyword">public</span> <span class="keyword">void</span> setAppVersions(List&lt;AppVersion> appVersions) {
<span class="lineno">  35 </span>        <span class="keyword">this</span>.appVersions = appVersions;
<span class="caretline"><span class="lineno">  36 </span>        <span class="warning">notifyDataSetChanged()</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  37 </span>    }
<span class="lineno">  38 </span>
<span class="lineno">  39 </span>    <span class="annotation">@NonNull</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNotifyDataSetChanged" style="display: none;">
The <code>RecyclerView</code> adapter's <code>onNotifyDataSetChanged</code> method does not specify what about the data set has changed, forcing any observers to assume that all existing items and structure may no longer be valid. `LayoutManager`s will be forced to fully rebind and relayout all visible views.<br/>To suppress this error, use the issue id "NotifyDataSetChanged" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NotifyDataSetChanged</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNotifyDataSetChangedLink" onclick="reveal('explanationNotifyDataSetChanged');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NotifyDataSetChangedCardLink" onclick="hideid('NotifyDataSetChangedCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ObsoleteSdkInt"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteSdkIntCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete SDK_INT Version Check</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/utils/ApkInstaller.java">../../src/main/java/com/bearmod/loader/utils/ApkInstaller.java</a>:56</span>: <span class="message">Unnecessary; <code>SDK_INT</code> is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  53 </span>   installIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
<span class="lineno">  54 </span>   
<span class="lineno">  55 </span>   Uri apkUri;
<span class="caretline"><span class="lineno">  56 </span>   <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.N</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  57 </span>       <span class="comment">// Use FileProvider for Android 7.0+</span>
<span class="lineno">  58 </span>       apkUri = FileProvider.getUriForFile(context, FILE_PROVIDER_AUTHORITY, apkFile);
<span class="lineno">  59 </span>   } <span class="keyword">else</span> {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteSdkInt" style="display: none;">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteSdkInt</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteSdkIntLink" onclick="reveal('explanationObsoleteSdkInt');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteSdkIntCardLink" onclick="hideid('ObsoleteSdkIntCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseCompoundDrawables"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseCompoundDrawablesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Node can be replaced by a TextView with compound drawables</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:232</span>: <span class="message">This tag and its children can be replaced by one <code>&lt;TextView/></code> and a compound drawable</span><br /><pre class="errorlines">
<span class="lineno"> 229 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span>
<span class="lineno"> 230 </span>                <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
<span class="lineno"> 231 </span>
<span class="caretline"><span class="lineno"> 232 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 233 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 234 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 235 </span>                    <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:256</span>: <span class="message">This tag and its children can be replaced by one <code>&lt;TextView/></code> and a compound drawable</span><br /><pre class="errorlines">
<span class="lineno"> 253 </span>
<span class="lineno"> 254 </span>                <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 255 </span>
<span class="caretline"><span class="lineno"> 256 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 257 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 258 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 259 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:291</span>: <span class="message">This tag and its children can be replaced by one <code>&lt;TextView/></code> and a compound drawable</span><br /><pre class="errorlines">
<span class="lineno"> 288 </span>                <span class="prefix">app:</span><span class="attribute">cardCornerRadius</span>=<span class="value">"12dp"</span>
<span class="lineno"> 289 </span>                <span class="prefix">app:</span><span class="attribute">cardElevation</span>=<span class="value">"0dp"</span>>
<span class="lineno"> 290 </span>
<span class="caretline"><span class="lineno"> 291 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 292 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 293 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 294 </span>                    <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseCompoundDrawables" style="display: none;">
A <code>LinearLayout</code> which contains an <code>ImageView</code> and a <code>TextView</code> can be more efficiently handled as a compound drawable (a single TextView, using the <code>drawableTop</code>, <code>drawableLeft</code>, <code>drawableRight</code> and/or <code>drawableBottom</code> attributes to draw one or more images adjacent to the text).<br/>
<br/>
If the two widgets are offset from each other with margins, this can be replaced with a <code>drawablePadding</code> attribute.<br/>
<br/>
There's a lint quickfix to perform this conversion in the Eclipse plugin.<br/>To suppress this error, use the issue id "UseCompoundDrawables" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseCompoundDrawables</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseCompoundDrawablesLink" onclick="reveal('explanationUseCompoundDrawables');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseCompoundDrawablesCardLink" onclick="hideid('UseCompoundDrawablesCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="VectorPath"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="VectorPathCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Long vector paths</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/drawable/ic_nav_settings.xml">../../src/main/res/drawable/ic_nav_settings.xml</a>:9</span>: <span class="message">Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">viewportHeight</span>=<span class="value">"24"</span>>
<span class="lineno">  7 </span>    <span class="tag">&lt;path</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">fillColor</span>=<span class="value">"#FFFFFF"</span>
<span class="caretline"><span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">pathData</span>=<span class="value">"</span><span class="warning"><span class="value">M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z</span></span><span class="value">"</span>/>
</span>
<span class="lineno"> 10 </span><span class="tag">&lt;/vector></span> </pre>

</div>
<div class="metadata"><div class="explanation" id="explanationVectorPath" style="display: none;">
Using long vector paths is bad for performance. There are several ways to make the <code>pathData</code> shorter:<br/>
* Using less precision<br/>
* Removing some minor details<br/>
* Using the Android Studio vector conversion tool<br/>
* Rasterizing the image (converting to PNG)<br/>To suppress this error, use the issue id "VectorPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">VectorPath</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationVectorPathLink" onclick="reveal('explanationVectorPath');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="VectorPathCardLink" onclick="hideid('VectorPathCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Overdraw"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverdrawCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overdraw: Painting regions more than once</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_tap_unlock.xml">../../src/main/res/layout/activity_tap_unlock.xml</a>:5</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/black</code> with a theme that also paints a background (inferred theme is <code>@style/Theme_BearLoader_NoActionBar</code>)</span><br /><pre class="errorlines">
<span class="lineno">  2 </span><span class="tag">&lt;RelativeLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">  5 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/black"</span></span>>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  6 </span>
<span class="lineno">  7 </span>    <span class="tag">&lt;ImageView</span><span class="attribute">
</span><span class="lineno">  8 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/glyphView"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOverdraw" style="display: none;">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Overdraw</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOverdrawLink" onclick="reveal('explanationOverdraw');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverdrawCardLink" onclick="hideid('OverdrawCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:2</span>: <span class="message">The resource <code>R.layout.activity_download</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="warning"><span class="tag">&lt;androidx.coordinatorlayout.widget.CoordinatorLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span></span>
<span class="lineno">   3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">   4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:4</span>: <span class="message">The resource <code>R.color.purple_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Original colors (keeping for reference) --></span>
<span class="caretline"><span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_200"</span></span>>#FFBB86FC<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:6</span>: <span class="message">The resource <code>R.color.purple_700</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>    <span class="comment">&lt;!-- Original colors (keeping for reference) --></span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_200"</span>>#FFBB86FC<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_700"</span></span>>#FF3700B3<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  7 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno">  8 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_700"</span>>#FF018786<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:22</span>: <span class="message">The resource <code>R.color.primary_container</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary"</span>>#2196F3<span class="tag">&lt;/color></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_dark"</span>>#1976D2<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_light"</span>>#BBDEFB<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"primary_container"</span></span>>#E3F2FD<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"on_primary"</span>>#FFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 24 </span>
<span class="lineno"> 25 </span>    <span class="comment">&lt;!-- Secondary colors --></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:23</span>: <span class="message">The resource <code>R.color.on_primary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_dark"</span>>#1976D2<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_light"</span>>#BBDEFB<span class="tag">&lt;/color></span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"primary_container"</span>>#E3F2FD<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"on_primary"</span></span>>#FFFFFF<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>
<span class="lineno"> 25 </span>    <span class="comment">&lt;!-- Secondary colors --></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary"</span>>#03DAC5<span class="tag">&lt;/color></span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UnusedResourcesDivLink" onclick="reveal('UnusedResourcesDiv');" />+ 112 More Occurrences...</button>
<div id="UnusedResourcesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:28</span>: <span class="message">The resource <code>R.color.secondary_light</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 25 </span>    <span class="comment">&lt;!-- Secondary colors --></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary"</span>>#03DAC5<span class="tag">&lt;/color></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_dark"</span>>#018786<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"secondary_light"</span></span>>#B2EBF2<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_container"</span>>#E0F7FA<span class="tag">&lt;/color></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"on_secondary"</span>>#000000<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:29</span>: <span class="message">The resource <code>R.color.secondary_container</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary"</span>>#03DAC5<span class="tag">&lt;/color></span>
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_dark"</span>>#018786<span class="tag">&lt;/color></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_light"</span>>#B2EBF2<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"secondary_container"</span></span>>#E0F7FA<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"on_secondary"</span>>#000000<span class="tag">&lt;/color></span>
<span class="lineno"> 31 </span>
<span class="lineno"> 32 </span>    <span class="comment">&lt;!-- Accent colors --></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:30</span>: <span class="message">The resource <code>R.color.on_secondary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_dark"</span>>#018786<span class="tag">&lt;/color></span>
<span class="lineno"> 28 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_light"</span>>#B2EBF2<span class="tag">&lt;/color></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"secondary_container"</span>>#E0F7FA<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"on_secondary"</span></span>>#000000<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>
<span class="lineno"> 32 </span>    <span class="comment">&lt;!-- Accent colors --></span>
<span class="lineno"> 33 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"accent"</span>>#FF4081<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:44</span>: <span class="message">The resource <code>R.color.on_surface</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 41 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface_container_lowest"</span>>#FFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 42 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface_container_high"</span>>#EEEEEE<span class="tag">&lt;/color></span>
<span class="lineno"> 43 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"surface_container_highest"</span>>#E0E0E0<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 44 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"on_surface"</span></span>>#000000<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 45 </span>
<span class="lineno"> 46 </span>    <span class="comment">&lt;!-- Background colors --></span>
<span class="lineno"> 47 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background"</span>>#121212<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:49</span>: <span class="message">The resource <code>R.color.on_background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 46 </span>    <span class="comment">&lt;!-- Background colors --></span>
<span class="lineno"> 47 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background"</span>>#121212<span class="tag">&lt;/color></span>
<span class="lineno"> 48 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background_light"</span>>#1E1E1E<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 49 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"on_background"</span></span>>#FFFFFF<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 50 </span>
<span class="lineno"> 51 </span>    <span class="comment">&lt;!-- Card colors --></span>
<span class="lineno"> 52 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"card_background"</span>>#1F1F1F<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:61</span>: <span class="message">The resource <code>R.color.transparent</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_hint"</span>>#80FFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 59 </span>
<span class="lineno"> 60 </span>    <span class="comment">&lt;!-- Common colors --></span>
<span class="caretline"><span class="lineno"> 61 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"transparent"</span></span>>#00000000<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 62 </span>
<span class="lineno"> 63 </span>    <span class="comment">&lt;!-- Status colors --></span>
<span class="lineno"> 64 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"warning"</span>>#FFC107<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:71</span>: <span class="message">The resource <code>R.color.progress_background</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 68 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"divider"</span>>#E0E0E0<span class="tag">&lt;/color></span>
<span class="lineno"> 69 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"ripple"</span>>#33FFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 70 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"shimmer_color"</span>>#DDDDDD<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 71 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"progress_background"</span></span>>#424242<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 72 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:10</span>: <span class="message">The resource <code>R.dimen.margin_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"text_size_caption"</span>>12sp<span class="tag">&lt;/dimen></span>
<span class="lineno">  8 </span>
<span class="lineno">  9 </span>    <span class="comment">&lt;!-- Margins and Padding --></span>
<span class="caretline"><span class="lineno"> 10 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"margin_large"</span></span>>24dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"margin_medium"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"margin_small"</span>>8dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"padding_large"</span>>24dp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:13</span>: <span class="message">The resource <code>R.dimen.padding_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"margin_large"</span>>24dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 11 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"margin_medium"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"margin_small"</span>>8dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"padding_large"</span></span>>24dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"padding_medium"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 15 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"padding_small"</span>>8dp<span class="tag">&lt;/dimen></span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:15</span>: <span class="message">The resource <code>R.dimen.padding_small</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"margin_small"</span>>8dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"padding_large"</span>>24dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"padding_medium"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 15 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"padding_small"</span></span>>8dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Corner Radii --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"corner_radius_large"</span>>24dp<span class="tag">&lt;/dimen></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:19</span>: <span class="message">The resource <code>R.dimen.corner_radius_medium</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Corner Radii --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"corner_radius_large"</span>>24dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 19 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"corner_radius_medium"</span></span>>16dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"corner_radius_small"</span>>8dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"glyph_corner_radius"</span>>28dp<span class="tag">&lt;/dimen></span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:20</span>: <span class="message">The resource <code>R.dimen.corner_radius_small</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- Corner Radii --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"corner_radius_large"</span>>24dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"corner_radius_medium"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 20 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"corner_radius_small"</span></span>>8dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 21 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"glyph_corner_radius"</span>>28dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>    <span class="comment">&lt;!-- Elevations --></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:24</span>: <span class="message">The resource <code>R.dimen.elevation_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"glyph_corner_radius"</span>>28dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>    <span class="comment">&lt;!-- Elevations --></span>
<span class="caretline"><span class="lineno"> 24 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"elevation_large"</span></span>>8dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"elevation_medium"</span>>4dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"elevation_small"</span>>2dp<span class="tag">&lt;/dimen></span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:25</span>: <span class="message">The resource <code>R.dimen.elevation_medium</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>    <span class="comment">&lt;!-- Elevations --></span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"elevation_large"</span>>8dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 25 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"elevation_medium"</span></span>>4dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"elevation_small"</span>>2dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>    <span class="comment">&lt;!-- Icon Sizes --></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:29</span>: <span class="message">The resource <code>R.dimen.icon_size_large</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"elevation_small"</span>>2dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>    <span class="comment">&lt;!-- Icon Sizes --></span>
<span class="caretline"><span class="lineno"> 29 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"icon_size_large"</span></span>>48dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"icon_size_medium"</span>>24dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 31 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"icon_size_small"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 32 </span><span class="tag">&lt;/resources></span></pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:30</span>: <span class="message">The resource <code>R.dimen.icon_size_medium</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>    <span class="comment">&lt;!-- Icon Sizes --></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"icon_size_large"</span>>48dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"icon_size_medium"</span></span>>24dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"icon_size_small"</span>>16dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 32 </span><span class="tag">&lt;/resources></span>
</pre>

<span class="location"><a href="../../src/main/res/values/dimens.xml">../../src/main/res/values/dimens.xml</a>:31</span>: <span class="message">The resource <code>R.dimen.icon_size_small</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 28 </span>    <span class="comment">&lt;!-- Icon Sizes --></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"icon_size_large"</span>>48dp<span class="tag">&lt;/dimen></span>
<span class="lineno"> 30 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> name</span>=<span class="value">"icon_size_medium"</span>>24dp<span class="tag">&lt;/dimen></span>
<span class="caretline"><span class="lineno"> 31 </span>    <span class="tag">&lt;dimen</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"icon_size_small"</span></span>>16dp<span class="tag">&lt;/dimen></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 32 </span><span class="tag">&lt;/resources></span>
</pre>

<span class="location"><a href="../../src/main/res/raw/download_complete.json">../../src/main/res/raw/download_complete.json</a></span>: <span class="message">The resource <code>R.raw.download_complete</code> appears to be unused</span><br />
<span class="location"><a href="../../src/main/res/drawable/ic_error.xml">../../src/main/res/drawable/ic_error.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_error</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/drawable/ic_key.xml">../../src/main/res/drawable/ic_key.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_key</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span></pre>

<span class="location"><a href="../../src/main/res/drawable/ic_nav_cloud.xml">../../src/main/res/drawable/ic_nav_cloud.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_nav_cloud</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_nav_home.xml">../../src/main/res/drawable/ic_nav_home.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_nav_home</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_nav_profile.xml">../../src/main/res/drawable/ic_nav_profile.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_nav_profile</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_nav_settings.xml">../../src/main/res/drawable/ic_nav_settings.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_nav_settings</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_paste.xml">../../src/main/res/drawable/ic_paste.xml</a>:2</span>: <span class="message">The resource <code>R.drawable.ic_paste</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;vector</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">width</span>=<span class="value">"24dp"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">height</span>=<span class="value">"24dp"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">viewportWidth</span>=<span class="value">"24"</span>
</pre>

<span class="location"><a href="../../src/main/res/drawable/ic_pubg_gl.png">../../src/main/res/drawable/ic_pubg_gl.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/ic_pubg_gl.png" /><span class="message">The resource <code>R.drawable.ic_pubg_gl</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/ic_pubg_kr.png">../../src/main/res/drawable/ic_pubg_kr.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/ic_pubg_kr.png" /><span class="message">The resource <code>R.drawable.ic_pubg_kr</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/ic_pubg_tw.png">../../src/main/res/drawable/ic_pubg_tw.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/ic_pubg_tw.png" /><span class="message">The resource <code>R.drawable.ic_pubg_tw</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/drawable/ic_pubg_vn.png">../../src/main/res/drawable/ic_pubg_vn.png</a></span>: <img class="embedimage" align="right" src="../../src/main/res/drawable/ic_pubg_vn.png" /><span class="message">The resource <code>R.drawable.ic_pubg_vn</code> appears to be unused</span><br clear="right"/>
<span class="location"><a href="../../src/main/res/layout/item_release.xml">../../src/main/res/layout/item_release.xml</a>:2</span>: <span class="message">The resource <code>R.layout.item_release</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">   2 </span><span class="warning"><span class="tag">&lt;com.google.android.material.card.MaterialCardView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span></span>
<span class="lineno">   3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">   4 </span>    <span class="prefix">xmlns:</span><span class="attribute">tools</span>=<span class="value">"http://schemas.android.com/tools"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/card_release"</span></pre>

<span class="location"><a href="../../src/main/res/anim/slide_in_left.xml">../../src/main/res/anim/slide_in_left.xml</a>:2</span>: <span class="message">The resource <code>R.anim.slide_in_left</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;set</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">duration</span>=<span class="value">"300"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">interpolator</span>=<span class="value">"@android:interpolator/fast_out_slow_in"</span>>
<span class="lineno">  5 </span>    <span class="tag">&lt;translate</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/anim/slide_out_right.xml">../../src/main/res/anim/slide_out_right.xml</a>:2</span>: <span class="message">The resource <code>R.anim.slide_out_right</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="caretline"><span class="lineno">  2 </span><span class="warning"><span class="tag">&lt;set</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  3 </span>    <span class="prefix">android:</span><span class="attribute">duration</span>=<span class="value">"300"</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">interpolator</span>=<span class="value">"@android:interpolator/fast_out_slow_in"</span>>
<span class="lineno">  5 </span>    <span class="tag">&lt;translate</span><span class="attribute">
</span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:9</span>: <span class="message">The resource <code>R.string.login_subtitle</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   6 </span>
<span class="lineno">   7 </span>    <span class="comment">&lt;!-- Login Screen --></span>
<span class="lineno">   8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_logo"</span>>App Logo<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">   9 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"login_subtitle"</span></span>>Enter your license key to continue<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_key"</span>>License Key<span class="tag">&lt;/string></span>
<span class="lineno">  11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"enter_license_key"</span>>Enter your license key<span class="tag">&lt;/string></span>
<span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"invalid_license_key_format"</span>>Invalid license key format<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:10</span>: <span class="message">The resource <code>R.string.license_key</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   7 </span>    <span class="comment">&lt;!-- Login Screen --></span>
<span class="lineno">   8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_logo"</span>>App Logo<span class="tag">&lt;/string></span>
<span class="lineno">   9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_subtitle"</span>>Enter your license key to continue<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  10 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"license_key"</span></span>>License Key<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"enter_license_key"</span>>Enter your license key<span class="tag">&lt;/string></span>
<span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"invalid_license_key_format"</span>>Invalid license key format<span class="tag">&lt;/string></span>
<span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"remember_me"</span>>Remember me<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:11</span>: <span class="message">The resource <code>R.string.enter_license_key</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   8 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"app_logo"</span>>App Logo<span class="tag">&lt;/string></span>
<span class="lineno">   9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_subtitle"</span>>Enter your license key to continue<span class="tag">&lt;/string></span>
<span class="lineno">  10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_key"</span>>License Key<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  11 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"enter_license_key"</span></span>>Enter your license key<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"invalid_license_key_format"</span>>Invalid license key format<span class="tag">&lt;/string></span>
<span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"remember_me"</span>>Remember me<span class="tag">&lt;/string></span>
<span class="lineno">  14 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login"</span>>Login<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:12</span>: <span class="message">The resource <code>R.string.invalid_license_key_format</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">   9 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_subtitle"</span>>Enter your license key to continue<span class="tag">&lt;/string></span>
<span class="lineno">  10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_key"</span>>License Key<span class="tag">&lt;/string></span>
<span class="lineno">  11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"enter_license_key"</span>>Enter your license key<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"invalid_license_key_format"</span></span>>Invalid license key format<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"remember_me"</span>>Remember me<span class="tag">&lt;/string></span>
<span class="lineno">  14 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login"</span>>Login<span class="tag">&lt;/string></span>
<span class="lineno">  15 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_success"</span>>Login successful<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:13</span>: <span class="message">The resource <code>R.string.remember_me</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  10 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_key"</span>>License Key<span class="tag">&lt;/string></span>
<span class="lineno">  11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"enter_license_key"</span>>Enter your license key<span class="tag">&lt;/string></span>
<span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"invalid_license_key_format"</span>>Invalid license key format<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"remember_me"</span></span>>Remember me<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  14 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login"</span>>Login<span class="tag">&lt;/string></span>
<span class="lineno">  15 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_success"</span>>Login successful<span class="tag">&lt;/string></span>
<span class="lineno">  16 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_error"</span>>Login failed: %1$s<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:14</span>: <span class="message">The resource <code>R.string.login</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  11 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"enter_license_key"</span>>Enter your license key<span class="tag">&lt;/string></span>
<span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"invalid_license_key_format"</span>>Invalid license key format<span class="tag">&lt;/string></span>
<span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"remember_me"</span>>Remember me<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  14 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"login"</span></span>>Login<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  15 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_success"</span>>Login successful<span class="tag">&lt;/string></span>
<span class="lineno">  16 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_error"</span>>Login failed: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  17 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_valid_until"</span>>License valid until: %1$s<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:15</span>: <span class="message">The resource <code>R.string.login_success</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  12 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"invalid_license_key_format"</span>>Invalid license key format<span class="tag">&lt;/string></span>
<span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"remember_me"</span>>Remember me<span class="tag">&lt;/string></span>
<span class="lineno">  14 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login"</span>>Login<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  15 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"login_success"</span></span>>Login successful<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  16 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_error"</span>>Login failed: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  17 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_valid_until"</span>>License valid until: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  18 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"registration_date"</span>>Registration date: %1$s<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:16</span>: <span class="message">The resource <code>R.string.login_error</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  13 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"remember_me"</span>>Remember me<span class="tag">&lt;/string></span>
<span class="lineno">  14 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login"</span>>Login<span class="tag">&lt;/string></span>
<span class="lineno">  15 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_success"</span>>Login successful<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  16 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"login_error"</span></span>>Login failed: %1$s<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  17 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_valid_until"</span>>License valid until: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  18 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"registration_date"</span>>Registration date: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  19 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"days_remaining"</span>>%1$d days remaining<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:18</span>: <span class="message">The resource <code>R.string.registration_date</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  15 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_success"</span>>Login successful<span class="tag">&lt;/string></span>
<span class="lineno">  16 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"login_error"</span>>Login failed: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  17 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_valid_until"</span>>License valid until: %1$s<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  18 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"registration_date"</span></span>>Registration date: %1$s<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  19 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"days_remaining"</span>>%1$d days remaining<span class="tag">&lt;/string></span>
<span class="lineno">  20 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"keyauth_init_warning"</span>>KeyAuth initialization warning. You may experience login issues.<span class="tag">&lt;/string></span>
<span class="lineno">  21 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"keyauth_init_failed"</span>>Failed to initialize KeyAuth. Please check your internet connection and try again.<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:20</span>: <span class="message">The resource <code>R.string.keyauth_init_warning</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  17 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"license_valid_until"</span>>License valid until: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  18 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"registration_date"</span>>Registration date: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  19 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"days_remaining"</span>>%1$d days remaining<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  20 </span>  <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"keyauth_init_warning"</span></span>>KeyAuth initialization warning. You may experience login issues.<span class="tag">&lt;/string></span></span>
<span class="lineno">  21 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"keyauth_init_failed"</span>>Failed to initialize KeyAuth. Please check your internet connection and try again.<span class="tag">&lt;/string></span>
<span class="lineno">  22 </span>  <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"version_info"</span>>Version %1$s<span class="tag">&lt;/string></span>
</pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:54</span>: <span class="message">The resource <code>R.string.available_releases</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  51 </span>    <span class="comment">&lt;!-- Download Screen --></span>
<span class="lineno">  52 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"download_title"</span>>Download<span class="tag">&lt;/string></span>
<span class="lineno">  53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"download_patches"</span>>Download Patches<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  54 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"available_releases"</span></span>>Available Releases<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  55 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"no_releases_available"</span>>No releases available<span class="tag">&lt;/string></span>
<span class="lineno">  56 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"select_release_first"</span>>Please select a release first<span class="tag">&lt;/string></span>
<span class="lineno">  57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"released"</span>>Released: %1$s<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:55</span>: <span class="message">The resource <code>R.string.no_releases_available</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  52 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"download_title"</span>>Download<span class="tag">&lt;/string></span>
<span class="lineno">  53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"download_patches"</span>>Download Patches<span class="tag">&lt;/string></span>
<span class="lineno">  54 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"available_releases"</span>>Available Releases<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  55 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"no_releases_available"</span></span>>No releases available<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  56 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"select_release_first"</span>>Please select a release first<span class="tag">&lt;/string></span>
<span class="lineno">  57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"released"</span>>Released: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"apk_size"</span>>APK Size: %1$s<span class="tag">&lt;/string></span></pre>

<span class="location"><a href="../../src/main/res/values/strings.xml">../../src/main/res/values/strings.xml</a>:56</span>: <span class="message">The resource <code>R.string.select_release_first</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  53 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"download_patches"</span>>Download Patches<span class="tag">&lt;/string></span>
<span class="lineno">  54 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"available_releases"</span>>Available Releases<span class="tag">&lt;/string></span>
<span class="lineno">  55 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"no_releases_available"</span>>No releases available<span class="tag">&lt;/string></span>
<span class="caretline"><span class="lineno">  56 </span>    <span class="tag">&lt;string</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"select_release_first"</span></span>>Please select a release first<span class="tag">&lt;/string></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  57 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"released"</span>>Released: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  58 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"apk_size"</span>>APK Size: %1$s<span class="tag">&lt;/string></span>
<span class="lineno">  59 </span>    <span class="tag">&lt;string</span><span class="attribute"> name</span>=<span class="value">"obb_size"</span>>OBB Size: %1$s<span class="tag">&lt;/string></span></pre>

<br/><b>NOTE: 67 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>skip-libraries</b> (default is true):<br/>
Whether the unused resource check should skip reporting unused resources in libraries.<br/>
<br/>
Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with <code>checkDependencies=true</code>).<br/>
<br/>
However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnusedResources"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"skip-libraries"</span> <span class="attribute">value</span>=<span class="value">"true"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Productivity"></a>
<a name="UseTomlInstead"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseTomlInsteadCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Use TOML Version Catalog Instead</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle.kts">../../build.gradle.kts</a>:110</span>: <span class="message">Use version catalog instead</span><br /><pre class="errorlines">
<span class="lineno"> 107 </span>    implementation(libs.json)
<span class="lineno"> 108 </span>
<span class="lineno"> 109 </span>    // AndroidX Security Crypto for EncryptedSharedPreferences
<span class="caretline"><span class="lineno"> 110 </span>    implementation(<span class="warning">"androidx.security:security-crypto:1.1.0-alpha06"</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 111 </span>
<span class="lineno"> 112 </span>    // Room database
<span class="lineno"> 113 </span>    implementation(libs.room.runtime.v271)
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseTomlInstead" style="display: none;">
If your project is using a <code>libs.versions.toml</code> file, you should place all Gradle dependencies in the TOML file. This lint check looks for version declarations outside of the TOML file and suggests moving them (and in the IDE, provides a quickfix to performing the operation automatically).<br/>To suppress this error, use the issue id "UseTomlInstead" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseTomlInstead</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Productivity</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseTomlInsteadLink" onclick="reveal('explanationUseTomlInstead');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseTomlInsteadCardLink" onclick="hideid('UseTomlInsteadCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ClickableViewAccessibility"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ClickableViewAccessibilityCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Accessibility in Custom Views</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/auth/TapToUnlockActivity.java">../../src/main/java/com/bearmod/loader/ui/auth/TapToUnlockActivity.java</a>:50</span>: <span class="message">Custom view `<code>ShapeableImageView</code>` has <code>setOnTouchListener</code> called on it but does not override <code>performClick</code></span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>        glyphView.startAnimation(pulse);
<span class="lineno"> 48 </span>        tapText.startAnimation(pulse);
<span class="lineno"> 49 </span>
<span class="caretline"><span class="lineno"> 50 </span>        <span class="warning">glyphView.setOnTouchListener((v, event) -> {</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>            <span class="keyword">if</span> (!unlocked &amp;&amp; event.getAction() == MotionEvent.ACTION_DOWN) {
<span class="lineno"> 52 </span>                unlockLoader();
<span class="lineno"> 53 </span>                <span class="keyword">return</span> <span class="keyword">true</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/auth/TapToUnlockActivity.java">../../src/main/java/com/bearmod/loader/ui/auth/TapToUnlockActivity.java</a>:50</span>: <span class="message"><code>onTouch</code> lambda should call <code>View#performClick</code> when a click is detected</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>        glyphView.startAnimation(pulse);
<span class="lineno"> 48 </span>        tapText.startAnimation(pulse);
<span class="lineno"> 49 </span>
<span class="caretline"><span class="lineno"> 50 </span>        glyphView.setOnTouchListener(<span class="warning">(v, event) -> {</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>            <span class="keyword">if</span> (!unlocked &amp;&amp; event.getAction() == MotionEvent.ACTION_DOWN) {
<span class="lineno"> 52 </span>                unlockLoader();
<span class="lineno"> 53 </span>                <span class="keyword">return</span> <span class="keyword">true</span>;
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationClickableViewAccessibility" style="display: none;">
If a <code>View</code> that overrides <code>onTouchEvent</code> or uses an <code>OnTouchListener</code> does not also implement <code>performClick</code> and call it when clicks are detected, the <code>View</code> may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in <code>View#performClick</code> as some accessibility services invoke <code>performClick</code> when a click action should occur.<br/>To suppress this error, use the issue id "ClickableViewAccessibility" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ClickableViewAccessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationClickableViewAccessibilityLink" onclick="reveal('explanationClickableViewAccessibility');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ClickableViewAccessibilityCardLink" onclick="hideid('ClickableViewAccessibilityCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ContentDescription"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ContentDescriptionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image without contentDescription</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:157</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 154 </span>                    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"32dp"</span>
<span class="lineno"> 155 </span>                    <span class="prefix">android:</span><span class="attribute">visibility</span>=<span class="value">"gone"</span>>
<span class="lineno"> 156 </span>
<span class="caretline"><span class="lineno"> 157 </span>                    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 158 </span><span class="attribute">                        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"120dp"</span>
<span class="lineno"> 159 </span>                        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"120dp"</span>
<span class="lineno"> 160 </span>                        <span class="prefix">android:</span><span class="attribute">alpha</span>=<span class="value">"0.6"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:238</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 235 </span>                    <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span>
<span class="lineno"> 236 </span>                    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 237 </span>
<span class="caretline"><span class="lineno"> 238 </span>                    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 239 </span><span class="attribute">                        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"20dp"</span>
<span class="lineno"> 240 </span>                        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"20dp"</span>
<span class="lineno"> 241 </span>                        <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@drawable/ic_download"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:263</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 260 </span>                    <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center_vertical"</span>
<span class="lineno"> 261 </span>                    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 262 </span>
<span class="caretline"><span class="lineno"> 263 </span>                    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 264 </span><span class="attribute">                        </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"20dp"</span>
<span class="lineno"> 265 </span>                        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"20dp"</span>
<span class="lineno"> 266 </span>                        <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@drawable/ic_download"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:31</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  28 </span>                <span class="prefix">app:</span><span class="attribute">strokeWidth</span>=<span class="value">"0dp"</span>
<span class="lineno">  29 </span>                <span class="prefix">app:</span><span class="attribute">cardBackgroundColor</span>=<span class="value">"@color/surface_variant"</span>>
<span class="lineno">  30 </span>
<span class="caretline"><span class="lineno">  31 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  32 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/logoImage"</span>
<span class="lineno">  33 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"160dp"</span>
<span class="lineno">  34 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"160dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationContentDescription" style="display: none;">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to <code>@null</code>. If your app's minSdkVersion is 16 or higher, you can instead set these graphical elements' <code>android:importantForAccessibility</code> attributes to <code>no</code>.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ContentDescription</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationContentDescriptionLink" onclick="reveal('explanationContentDescription');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ContentDescriptionCardLink" onclick="hideid('ContentDescriptionCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/main/AppVersionAdapter.java">../../src/main/java/com/bearmod/loader/ui/main/AppVersionAdapter.java</a>:49</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  46 </span>      android.util.Log.e(<span class="string">"AppVersionAdapter"</span>, <span class="string">"Error creating view holder: "</span> + e.getMessage(), e);
<span class="lineno">  47 </span>      <span class="comment">// Create a simple fallback view</span>
<span class="lineno">  48 </span>      android.widget.TextView fallbackView = <span class="keyword">new</span> android.widget.TextView(context);
<span class="caretline"><span class="lineno">  49 </span>      fallbackView.setText(<span class="warning"><span class="string">"Error loading item"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  50 </span>      fallbackView.setPadding(<span class="number">16</span>, <span class="number">16</span>, <span class="number">16</span>, <span class="number">16</span>);
<span class="lineno">  51 </span>      <span class="keyword">return</span> <span class="keyword">new</span> ViewHolder(fallbackView);
<span class="lineno">  52 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/main/AppVersionAdapter.java">../../src/main/java/com/bearmod/loader/ui/main/AppVersionAdapter.java</a>:96</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  93 </span>          holder.status.setText(statusText);
<span class="lineno">  94 </span>      } <span class="keyword">catch</span> (Exception e) {
<span class="lineno">  95 </span>          android.util.Log.e(<span class="string">"AppVersionAdapter"</span>, <span class="string">"Error setting status: "</span> + e.getMessage());
<span class="caretline"><span class="lineno">  96 </span>          holder.status.setText(<span class="warning"><span class="string">"Unknown"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  97 </span>      }
<span class="lineno">  98 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:35</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  32 </span>
<span class="lineno">  33 </span>            <span class="comment">// Add title</span>
<span class="lineno">  34 </span>            TextView title = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  35 </span>            title.setText(<span class="warning"><span class="string">"Enhanced Download System"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  36 </span>            title.setTextSize(<span class="number">24</span>);
<span class="lineno">  37 </span>            title.setPadding(<span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">32</span>);
<span class="lineno">  38 </span>            layout.addView(title);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:42</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  39 </span>
<span class="lineno">  40 </span>  <span class="comment">// Add description</span>
<span class="lineno">  41 </span>  TextView description = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  42 </span>  description.setText(<span class="warning"><span class="string">"This is the new enhanced download system with APK/OBB options, progress tracking, and auto-installation features."</span></span>);</span>
<span class="lineno">  43 </span>  description.setPadding(<span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">32</span>);
<span class="lineno">  44 </span>  layout.addView(description);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:48</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  45 </span>
<span class="lineno">  46 </span>            <span class="comment">// Add patch execution section</span>
<span class="lineno">  47 </span>            TextView patchTitle = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  48 </span>            patchTitle.setText(<span class="warning"><span class="string">"Patch Management"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  49 </span>            patchTitle.setTextSize(<span class="number">20</span>);
<span class="lineno">  50 </span>            patchTitle.setPadding(<span class="number">0</span>, <span class="number">16</span>, <span class="number">0</span>, <span class="number">16</span>);
<span class="lineno">  51 </span>            layout.addView(patchTitle);
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="SetTextI18nDivLink" onclick="reveal('SetTextI18nDiv');" />+ 30 More Occurrences...</button>
<div id="SetTextI18nDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:54</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  51 </span>  layout.addView(patchTitle);
<span class="lineno">  52 </span>
<span class="lineno">  53 </span>  TextView patchDescription = <span class="keyword">new</span> TextView(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  54 </span>  patchDescription.setText(<span class="warning"><span class="string">"Select root or no-root patching mode and apply patches to your games."</span></span>);</span>
<span class="lineno">  55 </span>  patchDescription.setPadding(<span class="number">0</span>, <span class="number">0</span>, <span class="number">0</span>, <span class="number">16</span>);
<span class="lineno">  56 </span>  layout.addView(patchDescription);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:60</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  57 </span>
<span class="lineno">  58 </span>            <span class="comment">// Add root mode button</span>
<span class="lineno">  59 </span>            Button rootModeButton = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  60 </span>            rootModeButton.setText(<span class="warning"><span class="string">"Root Mode Patching"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  61 </span>            rootModeButton.setOnClickListener(v -> showPatchModeDialog(<span class="keyword">true</span>));
<span class="lineno">  62 </span>            layout.addView(rootModeButton);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:66</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  63 </span>
<span class="lineno">  64 </span>            <span class="comment">// Add no-root mode button</span>
<span class="lineno">  65 </span>            Button noRootModeButton = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  66 </span>            noRootModeButton.setText(<span class="warning"><span class="string">"No-Root Mode Patching"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  67 </span>            noRootModeButton.setOnClickListener(v -> showPatchModeDialog(<span class="keyword">false</span>));
<span class="lineno">  68 </span>            layout.addView(noRootModeButton);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadActivity.java</a>:72</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  69 </span>
<span class="lineno">  70 </span>            <span class="comment">// Add back button</span>
<span class="lineno">  71 </span>            Button backButton = <span class="keyword">new</span> Button(<span class="keyword">this</span>);
<span class="caretline"><span class="lineno">  72 </span>            backButton.setText(<span class="warning"><span class="string">"Back to Main"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  73 </span>            backButton.setOnClickListener(v -> {
<span class="lineno">  74 </span>                Intent intent = <span class="keyword">new</span> Intent(<span class="keyword">this</span>, MainActivity.<span class="keyword">class</span>);
<span class="lineno">  75 </span>                startActivity(intent);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:94</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno">  91 </span><span class="javadoc">     * Setup initial state
</span><span class="lineno">  92 </span><span class="javadoc">     */</span>
<span class="lineno">  93 </span>    <span class="keyword">private</span> <span class="keyword">void</span> setupInitialState() {
<span class="caretline"><span class="lineno">  94 </span>        tvTitle.setText(<span class="warning"><span class="string">"Downloading "</span> + downloadType</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  95 </span>        tvFileName.setText(fileName);
<span class="lineno">  96 </span>        tvProgress.setText(<span class="string">"0%"</span>);
<span class="lineno">  97 </span>        tvSpeed.setText(<span class="string">"0 MB/s"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:94</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  91 </span><span class="javadoc">     * Setup initial state
</span><span class="lineno">  92 </span><span class="javadoc">     */</span>
<span class="lineno">  93 </span>    <span class="keyword">private</span> <span class="keyword">void</span> setupInitialState() {
<span class="caretline"><span class="lineno">  94 </span>        tvTitle.setText(<span class="warning"><span class="string">"Downloading "</span></span> + downloadType);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  95 </span>        tvFileName.setText(fileName);
<span class="lineno">  96 </span>        tvProgress.setText(<span class="string">"0%"</span>);
<span class="lineno">  97 </span>        tvSpeed.setText(<span class="string">"0 MB/s"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:97</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  94 </span>        tvTitle.setText(<span class="string">"Downloading "</span> + downloadType);
<span class="lineno">  95 </span>        tvFileName.setText(fileName);
<span class="lineno">  96 </span>        tvProgress.setText(<span class="string">"0%"</span>);
<span class="caretline"><span class="lineno">  97 </span>        tvSpeed.setText(<span class="warning"><span class="string">"0 MB/s"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  98 </span>        tvEta.setText(<span class="string">"Calculating..."</span>);
<span class="lineno">  99 </span>        tvFileSize.setText(<span class="string">"0 / 0 MB"</span>);
<span class="lineno"> 100 </span>        progressBar.setProgress(<span class="number">0</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:98</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  95 </span>        tvFileName.setText(fileName);
<span class="lineno">  96 </span>        tvProgress.setText(<span class="string">"0%"</span>);
<span class="lineno">  97 </span>        tvSpeed.setText(<span class="string">"0 MB/s"</span>);
<span class="caretline"><span class="lineno">  98 </span>        tvEta.setText(<span class="warning"><span class="string">"Calculating..."</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  99 </span>        tvFileSize.setText(<span class="string">"0 / 0 MB"</span>);
<span class="lineno"> 100 </span>        progressBar.setProgress(<span class="number">0</span>);
<span class="lineno"> 101 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:99</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  96 </span>        tvProgress.setText(<span class="string">"0%"</span>);
<span class="lineno">  97 </span>        tvSpeed.setText(<span class="string">"0 MB/s"</span>);
<span class="lineno">  98 </span>        tvEta.setText(<span class="string">"Calculating..."</span>);
<span class="caretline"><span class="lineno">  99 </span>        tvFileSize.setText(<span class="warning"><span class="string">"0 / 0 MB"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 100 </span>        progressBar.setProgress(<span class="number">0</span>);
<span class="lineno"> 101 </span>    }
<span class="lineno"> 102 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:119</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 116 </span>        progressBar.setProgress(progress);
<span class="lineno"> 117 </span>        
<span class="lineno"> 118 </span>        <span class="comment">// Update progress text</span>
<span class="caretline"><span class="lineno"> 119 </span>        tvProgress.setText(<span class="warning">progress + <span class="string">"%"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>        
<span class="lineno"> 121 </span>        <span class="comment">// Update speed</span>
<span class="lineno"> 122 </span>        <span class="keyword">if</span> (speedMBps > <span class="number">0</span>) {
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:123</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>        
<span class="lineno"> 121 </span>        <span class="comment">// Update speed</span>
<span class="lineno"> 122 </span>        <span class="keyword">if</span> (speedMBps > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 123 </span>            tvSpeed.setText(<span class="warning">DECIMAL_FORMAT.format(speedMBps) + <span class="string">" MB/s"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 125 </span>            tvSpeed.setText(<span class="string">"0 MB/s"</span>);
<span class="lineno"> 126 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:123</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 120 </span>        
<span class="lineno"> 121 </span>        <span class="comment">// Update speed</span>
<span class="lineno"> 122 </span>        <span class="keyword">if</span> (speedMBps > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 123 </span>            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + <span class="warning"><span class="string">" MB/s"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 124 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 125 </span>            tvSpeed.setText(<span class="string">"0 MB/s"</span>);
<span class="lineno"> 126 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:125</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 122 </span>        <span class="keyword">if</span> (speedMBps > <span class="number">0</span>) {
<span class="lineno"> 123 </span>            tvSpeed.setText(DECIMAL_FORMAT.format(speedMBps) + <span class="string">" MB/s"</span>);
<span class="lineno"> 124 </span>        } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 125 </span>            tvSpeed.setText(<span class="warning"><span class="string">"0 MB/s"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 126 </span>        }
<span class="lineno"> 127 </span>        
<span class="lineno"> 128 </span>        <span class="comment">// Update ETA</span></pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:131</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>        <span class="comment">// Update ETA</span>
<span class="lineno"> 129 </span>        <span class="keyword">if</span> (etaMinutes > <span class="number">0</span> || etaSeconds > <span class="number">0</span>) {
<span class="lineno"> 130 </span>            <span class="keyword">if</span> (etaMinutes > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 131 </span>                tvEta.setText(<span class="warning">etaMinutes + <span class="string">"m "</span> + etaSeconds + <span class="string">"s remaining"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 133 </span>                tvEta.setText(etaSeconds + <span class="string">"s remaining"</span>);
<span class="lineno"> 134 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:131</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>        <span class="comment">// Update ETA</span>
<span class="lineno"> 129 </span>        <span class="keyword">if</span> (etaMinutes > <span class="number">0</span> || etaSeconds > <span class="number">0</span>) {
<span class="lineno"> 130 </span>            <span class="keyword">if</span> (etaMinutes > <span class="number">0</span>) {
<span class="caretline"><span class="lineno"> 131 </span>                tvEta.setText(etaMinutes + <span class="string">"m "</span> + etaSeconds + <span class="warning"><span class="string">"s remaining"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 132 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 133 </span>                tvEta.setText(etaSeconds + <span class="string">"s remaining"</span>);
<span class="lineno"> 134 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:133</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>            <span class="keyword">if</span> (etaMinutes > <span class="number">0</span>) {
<span class="lineno"> 131 </span>                tvEta.setText(etaMinutes + <span class="string">"m "</span> + etaSeconds + <span class="string">"s remaining"</span>);
<span class="lineno"> 132 </span>            } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 133 </span>                tvEta.setText(<span class="warning">etaSeconds + <span class="string">"s remaining"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>            }
<span class="lineno"> 135 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 136 </span>            tvEta.setText(<span class="string">"Calculating..."</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:133</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>            <span class="keyword">if</span> (etaMinutes > <span class="number">0</span>) {
<span class="lineno"> 131 </span>                tvEta.setText(etaMinutes + <span class="string">"m "</span> + etaSeconds + <span class="string">"s remaining"</span>);
<span class="lineno"> 132 </span>            } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 133 </span>                tvEta.setText(etaSeconds + <span class="warning"><span class="string">"s remaining"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>            }
<span class="lineno"> 135 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 136 </span>            tvEta.setText(<span class="string">"Calculating..."</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:136</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 133 </span>                tvEta.setText(etaSeconds + <span class="string">"s remaining"</span>);
<span class="lineno"> 134 </span>            }
<span class="lineno"> 135 </span>        } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 136 </span>            tvEta.setText(<span class="warning"><span class="string">"Calculating..."</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 137 </span>        }
<span class="lineno"> 138 </span>        
<span class="lineno"> 139 </span>        <span class="comment">// Update file size</span></pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:140</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 137 </span>        }
<span class="lineno"> 138 </span>        
<span class="lineno"> 139 </span>        <span class="comment">// Update file size</span>
<span class="caretline"><span class="lineno"> 140 </span>        tvFileSize.setText(<span class="warning">DECIMAL_FORMAT.format(downloadedMB) + <span class="string">" / "</span> + </span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 141 </span>                          DECIMAL_FORMAT.format(totalSizeMB) + <span class="string">" MB"</span>);
<span class="lineno"> 142 </span>    }
<span class="lineno"> 143 </span>    
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:141</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 138 </span>        
<span class="lineno"> 139 </span>        <span class="comment">// Update file size</span>
<span class="lineno"> 140 </span>        tvFileSize.setText(DECIMAL_FORMAT.format(downloadedMB) + <span class="string">" / "</span> + 
<span class="caretline"><span class="lineno"> 141 </span>                          DECIMAL_FORMAT.format(totalSizeMB) + <span class="warning"><span class="string">" MB"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 142 </span>    }
<span class="lineno"> 143 </span>    
<span class="lineno"> 144 </span>    <span class="javadoc">/**
</span></pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:151</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 148 </span><span class="javadoc">     */</span>
<span class="lineno"> 149 </span>    <span class="keyword">public</span> <span class="keyword">void</span> showCompletion(<span class="keyword">boolean</span> success, String message) {
<span class="lineno"> 150 </span>        <span class="keyword">if</span> (success) {
<span class="caretline"><span class="lineno"> 151 </span>            tvTitle.setText(<span class="warning"><span class="string">"Download Complete"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 152 </span>            tvProgress.setText(<span class="string">"100%"</span>);
<span class="lineno"> 153 </span>            progressBar.setProgress(<span class="number">100</span>);
<span class="lineno"> 154 </span>            tvSpeed.setText(<span class="string">"Complete"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:152</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 149 </span>    <span class="keyword">public</span> <span class="keyword">void</span> showCompletion(<span class="keyword">boolean</span> success, String message) {
<span class="lineno"> 150 </span>        <span class="keyword">if</span> (success) {
<span class="lineno"> 151 </span>            tvTitle.setText(<span class="string">"Download Complete"</span>);
<span class="caretline"><span class="lineno"> 152 </span>            tvProgress.setText(<span class="warning"><span class="string">"100%"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 153 </span>            progressBar.setProgress(<span class="number">100</span>);
<span class="lineno"> 154 </span>            tvSpeed.setText(<span class="string">"Complete"</span>);
<span class="lineno"> 155 </span>            tvEta.setText(<span class="string">"Done"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:154</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 151 </span>            tvTitle.setText(<span class="string">"Download Complete"</span>);
<span class="lineno"> 152 </span>            tvProgress.setText(<span class="string">"100%"</span>);
<span class="lineno"> 153 </span>            progressBar.setProgress(<span class="number">100</span>);
<span class="caretline"><span class="lineno"> 154 </span>            tvSpeed.setText(<span class="warning"><span class="string">"Complete"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 155 </span>            tvEta.setText(<span class="string">"Done"</span>);
<span class="lineno"> 156 </span>            btnCancel.setText(<span class="string">"Close"</span>);
<span class="lineno"> 157 </span>        } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:155</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 152 </span>            tvProgress.setText(<span class="string">"100%"</span>);
<span class="lineno"> 153 </span>            progressBar.setProgress(<span class="number">100</span>);
<span class="lineno"> 154 </span>            tvSpeed.setText(<span class="string">"Complete"</span>);
<span class="caretline"><span class="lineno"> 155 </span>            tvEta.setText(<span class="warning"><span class="string">"Done"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 156 </span>            btnCancel.setText(<span class="string">"Close"</span>);
<span class="lineno"> 157 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 158 </span>            tvTitle.setText(<span class="string">"Download Failed"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:156</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 153 </span>            progressBar.setProgress(<span class="number">100</span>);
<span class="lineno"> 154 </span>            tvSpeed.setText(<span class="string">"Complete"</span>);
<span class="lineno"> 155 </span>            tvEta.setText(<span class="string">"Done"</span>);
<span class="caretline"><span class="lineno"> 156 </span>            btnCancel.setText(<span class="warning"><span class="string">"Close"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 157 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 158 </span>            tvTitle.setText(<span class="string">"Download Failed"</span>);
<span class="lineno"> 159 </span>            tvProgress.setText(<span class="string">"Failed"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:158</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 155 </span>            tvEta.setText(<span class="string">"Done"</span>);
<span class="lineno"> 156 </span>            btnCancel.setText(<span class="string">"Close"</span>);
<span class="lineno"> 157 </span>        } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 158 </span>            tvTitle.setText(<span class="warning"><span class="string">"Download Failed"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 159 </span>            tvProgress.setText(<span class="string">"Failed"</span>);
<span class="lineno"> 160 </span>            tvSpeed.setText(<span class="string">"Error"</span>);
<span class="lineno"> 161 </span>            tvEta.setText(message);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:159</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 156 </span>            btnCancel.setText(<span class="string">"Close"</span>);
<span class="lineno"> 157 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 158 </span>            tvTitle.setText(<span class="string">"Download Failed"</span>);
<span class="caretline"><span class="lineno"> 159 </span>            tvProgress.setText(<span class="warning"><span class="string">"Failed"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 160 </span>            tvSpeed.setText(<span class="string">"Error"</span>);
<span class="lineno"> 161 </span>            tvEta.setText(message);
<span class="lineno"> 162 </span>            btnCancel.setText(<span class="string">"Close"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:160</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 157 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 158 </span>            tvTitle.setText(<span class="string">"Download Failed"</span>);
<span class="lineno"> 159 </span>            tvProgress.setText(<span class="string">"Failed"</span>);
<span class="caretline"><span class="lineno"> 160 </span>            tvSpeed.setText(<span class="warning"><span class="string">"Error"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 161 </span>            tvEta.setText(message);
<span class="lineno"> 162 </span>            btnCancel.setText(<span class="string">"Close"</span>);
<span class="lineno"> 163 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java">../../src/main/java/com/bearmod/loader/ui/download/DownloadProgressDialog.java</a>:162</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 159 </span>            tvProgress.setText(<span class="string">"Failed"</span>);
<span class="lineno"> 160 </span>            tvSpeed.setText(<span class="string">"Error"</span>);
<span class="lineno"> 161 </span>            tvEta.setText(message);
<span class="caretline"><span class="lineno"> 162 </span>            btnCancel.setText(<span class="warning"><span class="string">"Close"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 163 </span>        }
<span class="lineno"> 164 </span>        
<span class="lineno"> 165 </span>        <span class="comment">// Update cancel button to close dialog</span></pre>

<span class="location"><a href="../../src/main/java/com/bearmod/loader/ui/download/ReleaseAdapter.java">../../src/main/java/com/bearmod/loader/ui/download/ReleaseAdapter.java</a>:53</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno">  50 </span>      android.util.Log.e(<span class="string">"ReleaseAdapter"</span>, <span class="string">"Error creating view holder: "</span> + e.getMessage(), e);
<span class="lineno">  51 </span>      <span class="comment">// Create a simple fallback view</span>
<span class="lineno">  52 </span>      android.widget.TextView fallbackView = <span class="keyword">new</span> android.widget.TextView(context);
<span class="caretline"><span class="lineno">  53 </span>      fallbackView.setText(<span class="warning"><span class="string">"Error loading release item"</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  54 </span>      fallbackView.setPadding(<span class="number">16</span>, <span class="number">16</span>, <span class="number">16</span>, <span class="number">16</span>);
<span class="lineno">  55 </span>      <span class="keyword">return</span> <span class="keyword">new</span> ReleaseViewHolder(fallbackView);
<span class="lineno">  56 </span>  }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_download.xml">../../src/main/res/layout/activity_download.xml</a>:375</span>: <span class="message">Hardcoded string "0%", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 372 </span>               <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 373 </span>               <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"end"</span>
<span class="lineno"> 374 </span>               <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"4dp"</span>
<span class="caretline"><span class="lineno"> 375 </span>               <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"0%"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 376 </span>               <span class="prefix">android:</span><span class="attribute">textAppearance</span>=<span class="value">"@style/TextAppearance.Material3.LabelLarge"</span>
<span class="lineno"> 377 </span>               <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:45</span>: <span class="message">Hardcoded string "BEAR-MOD", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  42 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/appNameText"</span>
<span class="lineno">  43 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  44 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  45 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"BEAR-MOD"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  46 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  47 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"32sp"</span>
<span class="lineno">  48 </span>                <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:59</span>: <span class="message">Hardcoded string "License Key", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  56 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  57 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  58 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"48dp"</span>
<span class="caretline"><span class="lineno">  59 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">hint</span>=<span class="value">"License Key"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  60 </span>                <span class="prefix">app:</span><span class="attribute">hintTextColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  61 </span>                <span class="prefix">app:</span><span class="attribute">boxStrokeColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  62 </span>                <span class="prefix">app:</span><span class="attribute">endIconMode</span>=<span class="value">"password_toggle"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:86</span>: <span class="message">Hardcoded string "Remember Key", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  83 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/checkboxRemember"</span>
<span class="lineno">  84 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  85 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  86 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Remember Key"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  87 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  88 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"24dp"</span>
<span class="lineno">  89 </span>                <span class="prefix">android:</span><span class="attribute">buttonTint</span>=<span class="value">"@color/white"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:95</span>: <span class="message">Hardcoded string "Automatic Login", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  92 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/checkboxAutoLogin"</span>
<span class="lineno">  93 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  94 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  95 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Automatic Login"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  96 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  97 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"8dp"</span>
<span class="lineno">  98 </span>                <span class="prefix">android:</span><span class="attribute">buttonTint</span>=<span class="value">"@color/white"</span> />
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="HardcodedTextDivLink" onclick="reveal('HardcodedTextDiv');" />+ 14 More Occurrences...</button>
<div id="HardcodedTextDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/activity_login.xml">../../src/main/res/layout/activity_login.xml</a>:104</span>: <span class="message">Hardcoded string "LOGIN", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btnLogin"</span>
<span class="lineno"> 102 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 103 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"64dp"</span>
<span class="caretline"><span class="lineno"> 104 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"LOGIN"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 105 </span>                <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno"> 106 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 107 </span>                <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"32dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_splash.xml">../../src/main/res/layout/activity_splash.xml</a>:42</span>: <span class="message">Hardcoded string "BEAR-MOD", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  39 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/appNameText"</span>
<span class="lineno">  40 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  41 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  42 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"BEAR-MOD"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  43 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  44 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"42sp"</span>
<span class="lineno">  45 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_splash.xml">../../src/main/res/layout/activity_splash.xml</a>:63</span>: <span class="message">Hardcoded string "Advanced Mod Loader", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  60 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/subtitleText"</span>
<span class="lineno">  61 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  62 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  63 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Advanced Mod Loader"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  64 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno">  65 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno">  66 </span>        <span class="prefix">android:</span><span class="attribute">fontFamily</span>=<span class="value">"sans-serif-light"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_splash.xml">../../src/main/res/layout/activity_splash.xml</a>:102</span>: <span class="message">Hardcoded string "Initializing...", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  99 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/loadingText"</span>
<span class="lineno"> 100 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 101 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 102 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Initializing..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 103 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span>
<span class="lineno"> 104 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno"> 105 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"16dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_tap_unlock.xml">../../src/main/res/layout/activity_tap_unlock.xml</a>:13</span>: <span class="message">Hardcoded string "Locked Glyph", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"180dp"</span>
<span class="lineno"> 11 </span>        <span class="prefix">android:</span><span class="attribute">layout_centerInParent</span>=<span class="value">"true"</span>
<span class="lineno"> 12 </span>        <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@drawable/ic_glyph_lock"</span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">contentDescription</span>=<span class="value">"Locked Glyph"</span></span> />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>
<span class="lineno"> 15 </span>    <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 16 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tapText"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_tap_unlock.xml">../../src/main/res/layout/activity_tap_unlock.xml</a>:19</span>: <span class="message">Hardcoded string "Tap to unlock", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tapText"</span>
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 19 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Tap to unlock"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@android:color/white"</span>
<span class="lineno"> 22 </span>        <span class="prefix">android:</span><span class="attribute">layout_below</span>=<span class="value">"@id/glyphView"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:14</span>: <span class="message">Hardcoded string "Downloading APK + OBB", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  11 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_download_title"</span>
<span class="lineno">  12 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  13 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  14 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Downloading APK + OBB"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  15 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  16 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20sp"</span>
<span class="lineno">  17 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span></pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:26</span>: <span class="message">Hardcoded string "pubg-mobile-gl.apk", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  23 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_file_name"</span>
<span class="lineno">  24 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  25 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  26 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"pubg-mobile-gl.apk"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  27 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno">  28 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno">  29 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span></pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:57</span>: <span class="message">Hardcoded string "0%", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  54 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno">  55 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  56 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno">  57 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"0%"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  58 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  59 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno">  60 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:67</span>: <span class="message">Hardcoded string "0 MB/s", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  64 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_speed"</span>
<span class="lineno">  65 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  66 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  67 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"0 MB/s"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  68 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno">  69 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:86</span>: <span class="message">Hardcoded string "0 / 0 MB", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  83 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno">  84 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  85 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno">  86 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"0 / 0 MB"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  87 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno">  88 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:95</span>: <span class="message">Hardcoded string "Calculating...", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  92 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_eta"</span>
<span class="lineno">  93 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  94 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  95 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Calculating..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  96 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno">  97 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/dialog_download_progress.xml">../../src/main/res/layout/dialog_download_progress.xml</a>:107</span>: <span class="message">Hardcoded string "Cancel", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 104 </span>        <span class="attribute">style</span>=<span class="value">"@style/Widget.BearLoader.Button"</span>
<span class="lineno"> 105 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 106 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 107 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Cancel"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 108 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span> />
<span class="lineno"> 109 </span>
<span class="lineno"> 110 </span><span class="tag">&lt;/LinearLayout></span></pre>

<span class="location"><a href="../../src/main/res/layout/item_app_version.xml">../../src/main/res/layout/item_app_version.xml</a>:74</span>: <span class="message">Hardcoded string "&#8226;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  71 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  72 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginStart</span>=<span class="value">"4dp"</span>
<span class="lineno">  73 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"2dp"</span>
<span class="caretline"><span class="lineno">  74 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"•"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  75 </span>            <span class="prefix">android:</span><span class="attribute">textAppearance</span>=<span class="value">"@style/TextAppearance.Material3.BodySmall"</span>
<span class="lineno">  76 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno">  77 </span>            <span class="prefix">app:</span><span class="attribute">layout_constraintStart_toEndOf</span>=<span class="value">"@+id/app_version"</span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">BadConfigurationProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
An <code>android.app.Application</code> must implement <code>androidx.work.Configuration.Provider</code><br/>
for on-demand initialization.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BadPeriodicWorkRequestEnqueue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>enqueue()</code> for `PeriodicWorkRequest`s, you might end up enqueuing<br/>
duplicate requests unintentionally. You should be using<br/>
<code>enqueueUniquePeriodicWork</code> with an <code>ExistingPeriodicWorkPolicy.KEEP</code> instead.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeepLinkInActivityDestination<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attaching a &lt;deeplink> to an &lt;activity> destination will never give                 the right behavior when using an implicit deep link on another app's task                 (where the system back should immediately take the user back to the app that                 triggered the deep link). Instead, attach the deep link directly to                 the second activity (either by manually writing the appropriate &lt;intent-filter>                 or by adding the &lt;deeplink> to the start destination of a nav host in that second                 activity).<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DetachAndAttachSameFragment<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When doing a FragmentTransaction that includes both attach()                 and detach() operations being committed on the same fragment instance, it is a                 no-op. The reason for this is that the FragmentManager optimizes all operations                 within a single transaction so the attach() and detach() cancel each other out                 and neither is actually executed. To get the desired behavior, you should separate                 the attach() and detach() calls into separate FragmentTransactions.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DialogFragmentCallbacksDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using a <code>DialogFragment</code>, the <code>setOnCancelListener</code> and                 <code>setOnDismissListener</code> callback functions within the <code>onCreateDialog</code> function                  __must not be used__ because the <code>DialogFragment</code> owns these callbacks.                  Instead the respective <code>onCancel</code> and <code>onDismiss</code> functions can be used to                  achieve the desired effect.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EmptyNavDeepLink<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Attempting to create an empty NavDeepLink will result in an IllegalStateException at runtime. You may set these arguments within the lambda of the call to navDeepLink.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentAddMenuProvider<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IdleBatteryChargingConstraints<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Some devices are never considered charging and idle at the same time.<br/>
Consider removing one of these constraints.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPeriodicWorkRequestInterval<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The interval duration for a <code>PeriodicWorkRequest</code> must be at least 15 minutes.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidSetHasFixedSize<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a RecyclerView uses <code>setHasFixedSize(...)</code> you cannot use <code>wrap_content</code> for  size in the scrolling direction.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.recyclerview<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460887">https://issuetracker.google.com/issues/new?component=460887</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingKeepAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Type-safe nav arguments such as Enum types can get incorrectly obfuscated in minified builds when not referenced directly<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingKeepAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Type-safe nav arguments such as Enum types can get incorrectly obfuscated in minified builds when not referenced directly<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingSerializableAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The destination needs to be annotated with @Serializable in order for Navigation library to convert the class or object declaration into a NavDestination.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingSerializableAnnotation<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The destination needs to be annotated with @Serializable in order for Navigation library to convert the class or object declaration into a NavDestination.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RemoveWorkManagerInitializer<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If an <code>android.app.Application</code> implements <code>androidx.work.Configuration.Provider</code>,<br/>
the default <code>androidx.startup.InitializationProvider</code> needs to be removed from the<br/>
AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyForegroundServiceType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using the setForegroundAsync() API, the application must override &lt;service /> entry for <code>SystemForegroundService</code> to include the foreground service type in the  <code>AndroidManifest.xml</code> file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpecifyJobSchedulerIdRange<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When using <code>JobScheduler</code> APIs directly, <code>WorkManager</code> requires that developers specify a range of <code>JobScheduler</code> ids that are safe for <code>WorkManager</code> to use so the `id`s do not collide. <br/>
For more information look at <code>androidx.work.Configuration.Builder.setJobSchedulerJobIdRange(int, int)</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeRepeatOnLifecycleDetector<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used with the viewLifecycleOwner                 in Fragments as opposed to lifecycleOwner.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseGetLayoutInflater<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Using LayoutInflater.from(Context) can return a LayoutInflater                  that does not have the correct theme.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460964">https://issuetracker.google.com/issues/new?component=460964</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRxSetProgress2<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>setCompletableProgress(...)</code> instead of `setProgress(...) in <code>RxWorker</code>.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>MaterialSwitch</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WorkerHasAPublicModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When you define a ListenableWorker which is constructed using the <br/>
default WorkerFactory, the ListenableWorker sub-type needs to be public.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.work<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409906">https://issuetracker.google.com/issues/new?component=409906</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongNavigateRouteType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the destination class contains arguments, the route is expected to be class instance with the arguments filled in.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRequiresOptIn<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental features defined in Kotlin source code must be annotated with the Kotlin<br/>
<code>@RequiresOptIn</code> annotation. Using <code>androidx.annotation.RequiresOptIn</code> will prevent the<br/>
Kotlin compiler from enforcing its opt-in policies.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongStartDestinationType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the startDestination contains arguments, the arguments must be provided to navigation via a fully formed route (a class instance with argumentsfilled in), or else it will be treated as a case of missing arguments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongStartDestinationType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the startDestination contains arguments, the arguments must be provided to navigation via a fully formed route (a class instance with argumentsfilled in), or else it will be treated as a case of missing arguments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.navigation.common<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=409828">https://issuetracker.google.com/issues/new?component=409828</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>PsiEquivalenceUtil.areElementsEquivalent(PsiElement, PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivacySandboxBlockedCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Many APIs are unavailable in the Privacy Sandbox, depending on the <code>targetSdk</code>.<br/>
<br/>
If your code is designed to run in the sandbox (and never outside the sandbox) then you should remove the blocked calls to avoid exceptions at runtime.<br/>
<br/>
If your code is part of a library that can be executed both inside and outside the sandbox, surround the code with <code>if (!Process.isSdkSandbox()) { ... }</code> (or use your own field or method annotated with <code>@ChecksRestrictedEnvironment</code>) to avoid executing blocked calls when in the sandbox. Or, add the <code>@RestrictedForEnvironment</code> annotation to the containing method if the entire method should not be called when in the sandbox.<br/>
<br/>
This check is disabled by default, and should only be enabled in modules that may execute in the Privacy Sandbox.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>