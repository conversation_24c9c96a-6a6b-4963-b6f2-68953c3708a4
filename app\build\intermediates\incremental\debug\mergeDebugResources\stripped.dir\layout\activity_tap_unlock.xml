<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <ImageView
        android:id="@+id/glyphView"
        android:layout_width="180dp"
        android:layout_height="180dp"
        android:layout_centerInParent="true"
        android:src="@drawable/ic_glyph_lock"
        android:contentDescription="Locked Glyph"
        tools:ignore="HardcodedText" />

    <TextView
        android:id="@+id/tapText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Tap to unlock"
        android:textSize="18sp"
        android:textColor="@android:color/white"
        android:layout_below="@id/glyphView"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp"
        tools:ignore="HardcodedText" />

</RelativeLayout> 